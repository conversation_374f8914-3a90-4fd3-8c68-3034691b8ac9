from django.conf import settings

from ztbory_django_shield.core.options import DjangoOptions
from ztbory_django_shield.core.slide_captcha.defaults import LjflGallery, DefaultGallery


class Options(DjangoOptions):
    """滑动校验码配置"""

    def __init__(
        self,
        gallery: str = "DefaultGallery",
        width: int = 460,
        height: int = 260,
        expire_in_seconds: int = 5 * 60,
    ):
        """

        :param width: 图片宽度 (px)
        :param height: 图片高度 (px)
        :param expire_in_seconds: 校验码有效时间 (s)
        """
        self.gallery = gallery
        self.width = width
        self.height = height
        self.expire_in_seconds = expire_in_seconds

        self.load_django_settings("slide_captcha")

    @property
    def gallery_instance(self):
        default_gallery_cls = DefaultGallery
        gallery_mapping = {"LjflGallery": LjflGallery}
        return gallery_mapping.get(self.gallery, default_gallery_cls)()

    @property
    def is_debug(self):
        return settings.DEBUG
