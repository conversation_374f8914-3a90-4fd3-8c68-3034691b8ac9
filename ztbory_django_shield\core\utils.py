import random
import string
import uuid


def random_sample(length, seeds=string.ascii_letters + string.digits):
    """生成随机字符串

    :param length: 长度
    :param seeds: 随机种子库
    :return:
    """
    value = "".join(random.sample(seeds, length))
    return value


def random_uuid():
    """生成uuid"""
    return f"{uuid.uuid4()}"


def seconds_to_tips(seconds: int):
    """时间(s)转为可读语句

    :param seconds: 时长 (s)
    :return:
    """
    minute, second = divmod(seconds, 60)
    hour, minute = divmod(minute, 60)
    tips = ""
    if hour:
        tips += f"{hour}小时"
    if minute:
        tips += f"{minute}分钟"
    if not hour and (second or not tips):
        tips += f"{second}秒"
    return tips


def get_client_ip(request):
    """获取访客ip"""
    forwared_for = request.META.get("HTTP_X_FORWARED_FOR")
    if forwared_for:
        return forwared_for.split(",")[0]
    real_ip = request.META.get("HTTP_X_REAL_IP")
    if real_ip:
        return real_ip
    return request.META.get("REMOTE_ADDR") or ""
