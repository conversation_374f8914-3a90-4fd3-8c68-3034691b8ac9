# Generated by Django 5.2.1 on 2025-07-07 08:53

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0070_certification_category_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="CertificationPlan",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "purpose",
                    models.CharField(max_length=100, verbose_name="产品碳足迹标识认证的目的"),
                ),
                (
                    "certifying_unit",
                    models.CharField(max_length=100, verbose_name="认证单元"),
                ),
                (
                    "product_type",
                    models.CharField(max_length=100, verbose_name="产品种类"),
                ),
                (
                    "cert_standard",
                    models.Char<PERSON>ield(max_length=100, verbose_name="认证依据标准"),
                ),
                (
                    "data_time_range",
                    models.Char<PERSON>ield(max_length=100, verbose_name="数据时间边界"),
                ),
                (
                    "field_inspection_requirement",
                    models.TextField(null=True, verbose_name="现场检查要求"),
                ),
                ("inspectors", models.JSONField(verbose_name="检查组成员")),
                ("reviewers", models.JSONField(verbose_name="技术评审组成员")),
                (
                    "accompanying_inspectors",
                    models.TextField(null=True, verbose_name="检查组同行人员"),
                ),
                (
                    "progress_schedule",
                    models.TextField(null=True, verbose_name="进度安排"),
                ),
                (
                    "create_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "certification",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="plan",
                        to="lca.certification",
                        verbose_name="认证记录",
                    ),
                ),
            ],
        ),
    ]
