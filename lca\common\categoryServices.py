from lca.accounting.models import Category


class CategoryServices:
    @staticmethod
    def get_all_id(parent_id: str):
        """获取所有子分类的id，包含当前分类"""
        ids = [
            x.id
            for x in Category.objects.raw(
                """
            WITH RECURSIVE subcategories AS (
                SELECT id, name, parent_id
                FROM lca_category
                WHERE parent_id = %s

                UNION ALL

                SELECT c.id, c.name, c.parent_id
                FROM lca_category c
                INNER JOIN subcategories s ON c.parent_id = s.id
            )
            SELECT * FROM subcategories;
            """,
                [parent_id],
            )
        ]
        return ids

    @staticmethod
    def is_first_level_category(category_id: str) -> bool:
        """检查分类是否是顶级分类"""
        return Category.objects.filter(id=category_id, parent_id__isnull=True).exists()
