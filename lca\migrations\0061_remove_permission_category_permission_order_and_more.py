# Generated by Django 5.2.1 on 2025-07-04 02:10

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0060_group_description"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="permission",
            name="category",
        ),
        migrations.AddField(
            model_name="permission",
            name="order",
            field=models.PositiveSmallIntegerField(default=0, verbose_name="排序，小的在前"),
        ),
        migrations.AddField(
            model_name="permission",
            name="parent",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="children",
                to="lca.permission",
                verbose_name="上级",
            ),
        ),
        migrations.AddField(
            model_name="permission",
            name="type",
            field=models.CharField(
                choices=[("menu", "菜单"), ("button", "按钮")],
                default="menu",
                max_length=100,
                verbose_name="类型",
            ),
        ),
        migrations.AlterField(
            model_name="user",
            name="groups",
            field=models.ManyToManyField(related_name="users", to="lca.group", verbose_name="角色"),
        ),
        migrations.DeleteModel(
            name="PermissionCategory",
        ),
    ]
