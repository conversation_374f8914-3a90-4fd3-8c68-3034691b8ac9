"""
简化判断 None 的 filter
"""

from datetime import date, timedelta
from django.db.models import QuerySet, Q as DjangoQ


def filter(queryset: QuerySet, *kwargs, ignore_none: bool = True, **args):
    for key, val in args.items():
        if not val and ignore_none:
            continue
        if val is None:
            val = True
            key = f"{key}__isnull"
        elif isinstance(val, tuple):
            val, fun = val
            if ignore_none and not val:
                continue
            val = fun()
        queryset = queryset.filter(**{key: val})
    return queryset.filter(*kwargs)


def Q(run: bool = True, **kwargs):
    if not run:
        return DjangoQ()
    return DjangoQ(**kwargs)


def next_day(date: date):
    def _next_day():
        return date + timedelta(days=1)

    return date, _next_day


def all_categories(category_id: str):
    from lca.common.categoryServices import CategoryServices

    def run():
        ids = CategoryServices.get_all_id(category_id)
        return ids

    return category_id, run
