from ninja import Query, Router
from infra.PageNumberPagination import PageNumberPagination
from infra.decorators import permission_required
from ninja.pagination import paginate

from lca.accounting.schema import BaseModelSchema, ModelSchema


router = Router(tags=["后台-碳核算"])


@router.get(
    "models",
    summary="获取模型列表",
    response=list[BaseModelSchema],
    operation_id="adminGetModelList",
)
@paginate(PageNumberPagination)
@permission_required("SystemAccounting")
def get_model_list(
    request,
    name: str = Query(None, title="名称"),
    category_id: str = Query(None, title="分类ID"),
):
    pass


@router.get(
    "models/plateform",
    summary="获取平台模型列表",
    response=list[BaseModelSchema],
    operation_id="adminGetPlateformModelList",
)
@paginate(PageNumberPagination)
@permission_required("SystemAccounting")
def get_plateform_model_list(request):
    pass


@router.post("models/plateform/{id}/delete", summary="删除平台模型", operation_id="adminDeletePlateformModel")
@permission_required("SystemAccounting")
def delete_plateform_model(request, id: int):
    pass


@router.post(
    "models/{id}/platform",
    summary="发布为平台模型",
    response=ModelSchema,
    operation_id="adminPublishModel",
)
@permission_required("SystemAccounting")
def publish_model(request, id: int):
    pass
