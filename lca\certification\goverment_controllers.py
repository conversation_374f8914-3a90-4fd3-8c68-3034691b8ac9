from ninja import Router
from infra.decorators import (
    certificated_user_required_government,
)
from lca.certification.schema import (
    CertificationGovmentSchema,
)

router = Router(tags=["碳认证-政府"])


@router.get(
    "",
    summary="首页",
    response=CertificationGovmentSchema,
    operation_id="governmentGetCertificationList",
)
@certificated_user_required_government()
def governmentGetCertificationList(
    request,
):
    pass
