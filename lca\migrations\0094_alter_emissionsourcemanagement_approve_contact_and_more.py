# Generated by Django 5.2.1 on 2025-07-24 06:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('lca', '0093_emissionsourceapplication_uuid_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='emissionsourcemanagement',
            name='approve_contact',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='数据审核者联系方式'),
        ),
        migrations.AlterField(
            model_name='emissionsourcemanagement',
            name='approve_contact_detail',
            field=models.CharField(max_length=255, verbose_name='数据审核联系方式'),
        ),
        migrations.AlterField(
            model_name='emissionsourcemanagement',
            name='generate_contact',
            field=models.CharField(max_length=255, verbose_name='数据生成联系人'),
        ),
        migrations.AlterField(
            model_name='emissionsourcemanagement',
            name='generate_contact_detail',
            field=models.Char<PERSON>ield(max_length=255, verbose_name='数据生成联系方式'),
        ),
        migrations.AlterField(
            model_name='emissionsourcemanagement',
            name='generate_version',
            field=models.CharField(max_length=255, verbose_name='数据生成版本'),
        ),
        migrations.AlterField(
            model_name='emissionsourcemanagement',
            name='input_contact_detail',
            field=models.CharField(max_length=255, verbose_name='数据录入联系方式'),
        ),
        migrations.AlterField(
            model_name='emissionsourcemanagement',
            name='inputer_contact',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='数据录入者'),
        ),
        migrations.AlterField(
            model_name='emissionsourcemanagement',
            name='owener',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='数据拥有者'),
        ),
        migrations.AlterField(
            model_name='emissionsourcemanagement',
            name='owener_version',
            field=models.CharField(max_length=255, verbose_name='数据拥有者版本'),
        ),
        migrations.AlterField(
            model_name='emissionsourcemanagementapplication',
            name='approve_contact',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='数据审核者联系方式'),
        ),
        migrations.AlterField(
            model_name='emissionsourcemanagementapplication',
            name='approve_contact_detail',
            field=models.CharField(max_length=255, verbose_name='数据审核联系方式'),
        ),
        migrations.AlterField(
            model_name='emissionsourcemanagementapplication',
            name='generate_contact',
            field=models.CharField(max_length=255, verbose_name='数据生成联系人'),
        ),
        migrations.AlterField(
            model_name='emissionsourcemanagementapplication',
            name='generate_contact_detail',
            field=models.CharField(max_length=255, verbose_name='数据生成联系方式'),
        ),
        migrations.AlterField(
            model_name='emissionsourcemanagementapplication',
            name='generate_version',
            field=models.CharField(max_length=255, verbose_name='数据生成版本'),
        ),
        migrations.AlterField(
            model_name='emissionsourcemanagementapplication',
            name='input_contact_detail',
            field=models.CharField(max_length=255, verbose_name='数据录入联系方式'),
        ),
        migrations.AlterField(
            model_name='emissionsourcemanagementapplication',
            name='inputer_contact',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='数据录入者'),
        ),
        migrations.AlterField(
            model_name='emissionsourcemanagementapplication',
            name='owener',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='数据拥有者'),
        ),
        migrations.AlterField(
            model_name='emissionsourcemanagementapplication',
            name='owener_version',
            field=models.CharField(max_length=255, verbose_name='数据拥有者版本'),
        ),
    ]
