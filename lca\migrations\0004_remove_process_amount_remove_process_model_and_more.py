# Generated by Django 5.1.7 on 2025-03-18 17:24

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0003_delete_emissionfactor"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="process",
            name="amount",
        ),
        migrations.RemoveField(
            model_name="process",
            name="model",
        ),
        migrations.RemoveField(
            model_name="process",
            name="product",
        ),
        migrations.RemoveField(
            model_name="process",
            name="unit",
        ),
        migrations.CreateModel(
            name="Craft",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200, verbose_name="过程名称")),
                ("product", models.CharField(max_length=200, verbose_name="产品")),
                (
                    "amount",
                    models.DecimalField(decimal_places=5, max_digits=20, verbose_name="数量"),
                ),
                (
                    "co2e",
                    models.DecimalField(decimal_places=5, max_digits=20, verbose_name="CO2e"),
                ),
                (
                    "model",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="crafts",
                        to="lca.model",
                        verbose_name="模型",
                    ),
                ),
                (
                    "unit",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.unit",
                        verbose_name="单位",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="process",
            name="craft",
            field=models.ForeignKey(
                default=1,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="processes",
                to="lca.craft",
                verbose_name="工艺",
            ),
            preserve_default=False,
        ),
    ]
