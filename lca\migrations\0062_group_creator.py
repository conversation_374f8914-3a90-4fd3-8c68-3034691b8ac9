# Generated by Django 5.2.1 on 2025-07-04 02:24

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0061_remove_permission_category_permission_order_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="group",
            name="creator",
            field=models.ForeignKey(
                default=1,
                on_delete=django.db.models.deletion.CASCADE,
                to=settings.AUTH_USER_MODEL,
                verbose_name="创建者",
            ),
        ),
    ]
