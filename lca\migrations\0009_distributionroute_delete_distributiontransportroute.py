# Generated by Django 5.1.7 on 2025-03-27 02:33

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0008_alter_distribution_model"),
    ]

    operations = [
        migrations.CreateModel(
            name="DistributionRoute",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("origin", models.CharField(max_length=100, verbose_name="起始地")),
                ("terminal", models.CharField(max_length=100, verbose_name="目的地")),
                (
                    "distance",
                    models.DecimalField(decimal_places=5, max_digits=20, verbose_name="运输距离"),
                ),
                (
                    "distribution",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="transports",
                        to="lca.distribution",
                        verbose_name="分销阶段",
                    ),
                ),
                (
                    "emmission_source",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.emissionsource",
                        verbose_name="运输方式",
                    ),
                ),
            ],
        ),
        migrations.DeleteModel(
            name="DistributionTransportRoute",
        ),
    ]
