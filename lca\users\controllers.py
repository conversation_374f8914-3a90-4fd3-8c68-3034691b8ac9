from typing import Optional
from ninja import Router
from infra.decorators import login_required
from lca.users.models import UserCertification
from lca.users.schema import (
    LoginSchema,
    RegisterInSchema,
    SendSMSINSchema,
    UpdateUserSchema,
    UserCertificationInSchema,
    UserCertificationSchema,
    UserMySchema,
)
from lca.users.services import UserService

router = Router(tags=["用户"])


@router.post("/login", response=UserMySchema, summary="登录", operation_id="login")
def login(request, data: LoginSchema):
    UserService.verify_code(data.phone, data.code)
    return UserService.login(request, data.phone)


@router.post("/send-sms-code", summary="发送短信验证码", operation_id="send_sms_code")
def send_sms_code(request, data: SendSMSINSchema):
    UserService.send_code_sms(data.phone, data.uuid, data.secret)


@router.post("/logout", summary="退出登录", operation_id="logout")
def logout(request):
    UserService.logout(request)


@router.post("/register", summary="注册", response=UserMySchema, operation_id="register")
def register(request, data: RegisterInSchema):
    UserService.verify_code(data.phone, data.code)
    return UserService.register(request, data)


@router.post("/update", summary="更新用户信息", response=UserMySchema, operation_id="update_user")
@login_required
def update_user(request, data: UpdateUserSchema):
    user = request.user
    user.nickname = data.nickname
    user.save()
    return user


@router.post(
    "/update-phone",
    summary="更新手机号",
    response=UserMySchema,
    operation_id="update_phone",
)
@login_required
def update_phone(request, data: LoginSchema):
    user = request.user
    UserService.verify_code(data.phone, data.code)
    UserService.update_phone(user, data.phone)
    return user


@router.get("/", response=UserMySchema, summary="获取用户信息", operation_id="get_user_info")
@login_required
def get_user_info(request):
    return request.user


@router.get(
    "/certification",
    summary="获取认证信息",
    response=Optional[UserCertificationSchema],
    operation_id="get_user_certification",
)
@login_required
def get_user_certification(request):
    user = request.user
    return UserCertification.objects.filter(user=user).order_by("-id").first()


@router.post(
    "/certification",
    summary="提交认证信息",
    response=UserCertificationSchema,
    operation_id="submit_user_certification",
)
@login_required
def submit_user_certification(request, data: UserCertificationInSchema):
    user = request.user
    return UserService.submit_user_certification(user, data)
