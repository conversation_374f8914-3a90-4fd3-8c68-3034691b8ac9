from django.core.management import BaseCommand
from infra.utils import label_to_value
import csv
from django.db import transaction

from lca.database.models import FLOW_TYPES, Flow


class Command(BaseCommand):
    help = "初始化流"

    def handle(self, *args, **options):
        run()


def run():
    with open("lca/management/commands/data/flow.csv", "r", encoding="utf-8") as file:
        reader = csv.DictReader(file)
        with transaction.atomic():
            for row in reader:
                print(row["名称"])
                item = Flow.objects.filter(name=row["名称"]).first()
                data = dict(
                    name=row["名称"],
                    type=label_to_value(row["类型"], FLOW_TYPES),
                )
                if item is None:
                    item = Flow()
                for key, value in data.items():
                    setattr(item, key, value)
                item.save()
