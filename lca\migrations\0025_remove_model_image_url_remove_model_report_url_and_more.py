# Generated by Django 5.1.7 on 2025-04-25 03:46

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0024_category_parent_alter_endoflifemodel_eol_model_type"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="model",
            name="image_url",
        ),
        migrations.RemoveField(
            model_name="model",
            name="report_url",
        ),
        migrations.RemoveField(
            model_name="model",
            name="source",
        ),
        migrations.AddField(
            model_name="model",
            name="specs",
            field=models.CharField(default="默认型号", max_length=100, verbose_name="产品型号"),
            preserve_default=False,
        ),
        migrations.CreateModel(
            name="File",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "path",
                    models.FileField(max_length=200, upload_to="", verbose_name="文件路径"),
                ),
                ("name", models.CharField(max_length=200, verbose_name="名称")),
                ("size", models.BigIntegerField(verbose_name="大小")),
                (
                    "create_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "bom",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="boms",
                        to="lca.model",
                        verbose_name="BOM表",
                    ),
                ),
                (
                    "energy_other",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="energy_others",
                        to="lca.model",
                        verbose_name="能源其他凭证",
                    ),
                ),
                (
                    "energy_report",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="energy_reports",
                        to="lca.model",
                        verbose_name="《能源购进、消费与库存表》",
                    ),
                ),
                (
                    "energy_tax",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="energy_taxs",
                        to="lca.model",
                        verbose_name="能源购买发票",
                    ),
                ),
                (
                    "environment_check_report",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="environment_check_reports",
                        to="lca.model",
                        verbose_name="环境检测报告",
                    ),
                ),
                (
                    "environment_impact_report",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="environment_impact_reports",
                        to="lca.model",
                        verbose_name="环评报告",
                    ),
                ),
                (
                    "outlet_flow_report",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="outlet_flow_reports",
                        to="lca.model",
                        verbose_name="排水口检测流量统计表",
                    ),
                ),
                (
                    "production_report",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="production_reports",
                        to="lca.model",
                        verbose_name="《工业产销总值及主要产品产量》或《生产统计产量表》",
                    ),
                ),
                (
                    "purchase_contract",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="purchase_contracts",
                        to="lca.model",
                        verbose_name="采购合同",
                    ),
                ),
                (
                    "waste_ledger",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="waste_ledgers",
                        to="lca.model",
                        verbose_name="废弃物台账",
                    ),
                ),
                (
                    "water_bill",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="water_bills",
                        to="lca.model",
                        verbose_name="水费结算单",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="model",
            name="image",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="imageModel",
                to="lca.file",
                verbose_name="图谱",
            ),
        ),
        migrations.AddField(
            model_name="model",
            name="report",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="reportModel",
                to="lca.file",
                verbose_name="报告",
            ),
        ),
    ]
