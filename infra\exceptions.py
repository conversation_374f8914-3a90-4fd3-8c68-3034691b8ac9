from typing import Any

from pydantic import BaseModel, Field, Json


class APIException(Exception):
    status_code = 500
    default_message = "服务器错误"
    default_code = 50000

    def __init__(self, message: str = None, code: int = None):
        if message is None:
            message = self.default_message
        if code is None:
            code = self.default_code
        self.message = message
        self.code = code


class Error(BaseModel):
    code: str = Field(
        ...,
        title="错误代码",
        description="为了方便客户端处理错误，为每个错误定义一个唯一的错误代码。客户端可以根据错误代码采取特定的操作或显示适当的错误信息。",
    )
    message: str = Field(..., title="错误消息", description="消息是用户友好的，帮助用户了解问题所在")
    detail: Json[Any] = Field(
        None,
        title="错误详情（可选）",
        description="可选，面向开发者，帮助开发者了解具体的错误原因、调试信息等。以结构化的方式（JSON对象）提供",
    )


class BadRequest(APIException):
    status_code = 400
    default_message = "请求参数错误"
    default_code = 40000


class AuthenticationFailed(APIException):
    status_code = 401
    default_message = "身份验证凭据不正确"
    default_code = 40001


class NotAuthenticated(APIException):
    status_code = 401
    default_message = "身份验证凭据未提供"
    default_code = 40001


class PermissionDenied(APIException):
    status_code = 403
    default_message = "无权执行该操作"
    default_code = 40003


class NotFound(APIException):
    status_code = 404
    default_message = "404 Not Found"
    default_code = 40004


class ResourceAlreadyExists(APIException):
    status_code = 409
    default_message = "Request conflict"
    default_code = 40009


class ServiceUnavailable(APIException):
    status_code = 503
    default_message = "服务暂不可用"
    default_code = 50003
