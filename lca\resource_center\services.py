from typing import Optional

from django.db.models import QuerySet
from django.utils import timezone


from lca.operation_log.models import OperationType
from lca.resource_center.errors import ResourceCenterError, raise_error
from lca.resource_center.models import (
    InformationStatus,
    ResourceCategoryGroup,
    ResourceCenter,
    ResourceType,
)
from lca.resource_center.schema import (
    BaseResourceSchema,
    NewsAndAnnouncementInSchema,
    NewsAndAnnouncementUpdateSchema,
    StandardSpecificationSchema,
)
from lca.resource_center.validators import validator


category_group_mapping = {
    ResourceType.INFO_ANNOUNCEMENT: ResourceCategoryGroup.POLICY_NEWS,
    ResourceType.DOMESTIC_CARBON_NEWS: ResourceCategoryGroup.POLICY_NEWS,
    ResourceType.INTERNATIONAL_CARBON_NEWS: ResourceCategoryGroup.POLICY_NEWS,
    ResourceType.RESEARCH_REPORT: ResourceCategoryGroup.CARBON_RESOURCE,
    ResourceType.STANDARD_SPECIFICATION: ResourceCategoryGroup.CARBON_RESOURCE,
}


class ResourceCenterService:
    @staticmethod
    def publish(id_: int):
        """草稿箱中发布"""
        resource_center: ResourceCenter = ResourceCenter.objects.get(id=id_)
        if resource_center.status != InformationStatus.DRAFT:
            raise_error(ResourceCenterError.PUBLISHED_CANNOT_REPEAT)

        now = timezone.now()
        ResourceCenter.objects.filter(id=id_).update(
            status=InformationStatus.PUBLISHED, publish_time=now, unpublish_time=None, update_time=now
        )

    @staticmethod
    def unpublish(id_: int):
        """下架 直接删除"""
        resource_center: ResourceCenter = ResourceCenter.objects.get(id=id_)
        if resource_center.status != InformationStatus.PUBLISHED:
            raise_error(ResourceCenterError.NOT_PUBLISHED_CANNOT_UNPUBLISH)

        ResourceCenter.objects.filter(id=id_).delete()

    @staticmethod
    def delete(id_: int):
        """删除"""
        resource_center: ResourceCenter = ResourceCenter.objects.get(id=id_)
        if resource_center.status != InformationStatus.DRAFT:
            raise_error(ResourceCenterError.NOT_DRAFT_CANNOT_DELETE)
        resource_center.delete()

    @staticmethod
    def get_detail(id_: int, status: InformationStatus = None) -> ResourceCenter:
        """获取详情"""
        filter_: dict = {
            "id": id_,
        }
        if status:
            filter_["status"] = status
        resource_center = ResourceCenter.objects.get(**filter_)
        return resource_center

    @staticmethod
    def list_order_by(queryset: QuerySet, status: InformationStatus, order_by: str | None = None):
        if order_by:
            return queryset.order_by(order_by)
        if status == InformationStatus.PUBLISHED:
            return queryset.order_by("-publish_date", "-publish_time")
        return queryset.order_by("-create_time")

    @staticmethod
    def list(
        status: InformationStatus,
        content_type: ResourceType = None,
        title: Optional[str] = None,
        publish_organization: Optional[str] = None,
    ) -> QuerySet:
        """列表数据"""
        filters: dict = {
            "status": status,
        }
        if content_type:
            filters["content_type"] = content_type
        if title:
            filters["title__icontains"] = title
        if publish_organization:
            filters["publish_organization__icontains"] = publish_organization
        queryset = ResourceCenter.objects.filter(**filters)
        return ResourceCenterService.list_order_by(queryset, status)

    @staticmethod
    def list_news_announcement(
        content_type: ResourceType,
        status: InformationStatus,
        title: Optional[str] = None,
        publish_organization: Optional[str] = None,
    ) -> QuerySet:
        """信息公告、国内碳资讯、国外碳资讯、研究报告列表"""
        if content_type not in [
            ResourceType.INFO_ANNOUNCEMENT,
            ResourceType.DOMESTIC_CARBON_NEWS,
            ResourceType.INTERNATIONAL_CARBON_NEWS,
            ResourceType.RESEARCH_REPORT,
        ]:
            raise_error(ResourceCenterError.CONTENT_TYPE_INVALID)

        filters: dict = {
            "content_type": content_type,
            "status": status,
        }
        if title:
            filters["title__icontains"] = title
        if publish_organization:
            filters["publish_organization__icontains"] = publish_organization
        return ResourceCenter.objects.filter(**filters)

    @staticmethod
    def create_news_and_announcement(
        data: NewsAndAnnouncementInSchema,
    ) -> ResourceCenter:
        """创建信息公告
        信息公告、国内碳资讯、国外碳资讯、研究报告
        """
        validator.validate_news(data)

        now = timezone.now()
        if data.status == InformationStatus.PUBLISHED:
            publish_time = now
        else:
            publish_time = None

        if not data.cover_image_id:
            data.cover_image_id = None
        if not data.file_upload_id:
            data.file_upload_id = None

        return ResourceCenter.objects.create(
            **data.model_dump(),
            category_group=category_group_mapping[data.content_type],
            publish_time=publish_time,
        )

    @staticmethod
    def update_news_and_announcement(id_: int, data: NewsAndAnnouncementUpdateSchema) -> ResourceCenter:
        """更新信息公告
        信息公告、国内碳资讯、国外碳资讯、研究报告
        """
        resource_center: ResourceCenter = ResourceCenter.objects.get(id=id_)
        # 草稿箱中才能更新
        if resource_center.status != InformationStatus.DRAFT:
            raise_error(ResourceCenterError.RESOURCE_ALREADY_PUBLISHED_CANNOT_EDIT)

        validator.validate_news(data)

        now = timezone.now()
        if data.status == InformationStatus.PUBLISHED:
            publish_time = now
        else:
            publish_time = None

        if not data.cover_image_id:
            data.cover_image_id = None
        if not data.file_upload_id:
            data.file_upload_id = None

        ResourceCenter.objects.filter(id=id_).update(
            **data.model_dump(),
            publish_time=publish_time,
            update_time=now,
        )
        return ResourceCenter.objects.get(id=id_)

    @staticmethod
    def list_standard_specification(
        status: InformationStatus,
        title: Optional[str] = None,
        standard_number: Optional[str] = None,
        standard_scope: Optional[str] = None,
    ) -> QuerySet:
        """标准规范列表"""

        filters: dict = {
            "content_type": ResourceType.STANDARD_SPECIFICATION,
            "status": status,
        }
        if title:
            filters["title__icontains"] = title
        if standard_number:
            filters["standard_number__icontains"] = standard_number
        if standard_scope:
            filters["standard_scope__icontains"] = standard_scope
        queryset = ResourceCenter.objects.filter(**filters)
        return ResourceCenterService.list_order_by(queryset, status)

    @staticmethod
    def create_standard_specification(
        data: StandardSpecificationSchema,
    ) -> ResourceCenter:
        """添加标准规范"""
        validator.validate_standard(data)

        now = timezone.now()
        if data.status == InformationStatus.PUBLISHED:
            publish_time = now
        else:
            publish_time = None

        data = ResourceCenterService.clean_field(data)

        return ResourceCenter.objects.create(
            **data.model_dump(),
            category_group=category_group_mapping[ResourceType.STANDARD_SPECIFICATION],
            content_type=ResourceType.STANDARD_SPECIFICATION,
            publish_time=publish_time,
        )

    @staticmethod
    def update_standard_specification(id_: int, data: StandardSpecificationSchema) -> ResourceCenter:
        """更新标准规范"""
        resource_center: ResourceCenter = ResourceCenter.objects.get(id=id_)
        # 草稿箱中才能更新
        if resource_center.status != InformationStatus.DRAFT:
            raise_error(ResourceCenterError.RESOURCE_ALREADY_PUBLISHED_CANNOT_EDIT)

        validator.validate_standard(data)

        now = timezone.now()
        if data.status == InformationStatus.PUBLISHED:
            publish_time = now
        else:
            publish_time = None

        data = ResourceCenterService.clean_field(data)

        ResourceCenter.objects.filter(id=id_).update(
            **data.model_dump(),
            publish_time=publish_time,
            update_time=now,
        )
        return ResourceCenter._default_manager.get(id=id_)

    @staticmethod
    def clean_field(data: BaseResourceSchema):
        if not data.cover_image_id:
            data.cover_image_id = None
        if not data.file_upload_id:
            data.file_upload_id = None
        return data


class ResourceCenterLogService:
    """日志服务类"""

    @staticmethod
    def create_or_edit_news_operation(
        payload: NewsAndAnnouncementInSchema | NewsAndAnnouncementUpdateSchema,
    ) -> (OperationType, str):
        return ResourceCenterLogService._save_create_or_edit_operation(payload, payload.content_type)

    @staticmethod
    def create_or_edit_standard_operation(payload: StandardSpecificationSchema) -> (OperationType, str):
        return ResourceCenterLogService._save_create_or_edit_operation(payload, ResourceType.STANDARD_SPECIFICATION)

    @staticmethod
    def _save_create_or_edit_operation(payload: BaseResourceSchema, content_type: ResourceType) -> (OperationType, str):
        if payload.status == InformationStatus.PUBLISHED:
            operation_type = OperationType.PUBLISH_INFO
        elif payload.status == InformationStatus.DRAFT:
            operation_type = OperationType.CREATE_INFO
        else:
            return None, f"保存 {payload.title}"
        return operation_type, f"{operation_type.label}{content_type.label}{payload.title}"

    @staticmethod
    def delete_operation(id_: int) -> str:
        try:
            resource_center: ResourceCenter = ResourceCenter.objects.get(id=id_)
            return f"删除{ResourceType(resource_center.content_type).label}{resource_center.title}"
        except ResourceCenter.DoesNotExist:
            return "删除信息"

    @staticmethod
    def publish_operation(id_: int) -> str:
        try:
            resource_center: ResourceCenter = ResourceCenter.objects.get(id=id_)
            return f"发布了{ResourceType(resource_center.content_type).label}{resource_center.title}"
        except ResourceCenter.DoesNotExist:
            return "发布信息"

    @staticmethod
    def unpublish_operation(id_: int) -> str:
        try:
            resource_center: ResourceCenter = ResourceCenter.objects.get(id=id_)
            return f"下架了{ResourceType(resource_center.content_type).label}{resource_center.title}"
        except ResourceCenter.DoesNotExist:
            return "下架了信息"
