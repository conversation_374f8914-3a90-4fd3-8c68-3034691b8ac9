import json
from django.core.management import BaseCommand
from lca.common.models import Region, REGION_LEVEL


class Command(BaseCommand):
    help = "初始化地理区域表"

    def handle(self, *args, **options):
        initial_region()


def initial_region():
    with open("lca/management/commands/data/pc-code.json", "r", encoding="utf-8") as file:
        data = json.load(file)
        for province in data:
            print(province["name"])
            Region.objects.create(
                name=province["name"],
                id=province["code"],
                level=REGION_LEVEL.PROVINCE.value,
            )

            for city in province["children"]:
                print(city["name"])
                Region.objects.create(
                    name=city["name"],
                    id=city["code"],
                    parent_id=province["code"],
                    level=REGION_LEVEL.CITY.value,
                )
