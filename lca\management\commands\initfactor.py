from django.core.management import BaseCommand
from lca.accounting.models import EmissionSource, INPUT_TYPES
import csv
from django.db import transaction
import re


class Command(BaseCommand):
    help = "初始化因子"

    def handle(self, *args, **options):
        run()


def run():
    with open("lca/management/commands/data/factor.csv", "r", encoding="utf-8") as file:
        reader = csv.DictReader(file)
        with transaction.atomic():
            for row in reader:
                print(row["名称"])
                item = EmissionSource.objects.filter(name=row["名称"]).first()
                data = dict(
                    name=row["名称"],
                    type=type_label_to_value(row["通用分类"]),
                    category_id=get_cat_id(row),
                    year=row["基准年"],
                    geography_id="CN",
                    amount=1,
                    unit_id=get_unit(row["单位"]),
                    source=row["数据来源"],
                    co2e=row["排放量"],
                )
                if item is None:
                    item = EmissionSource()
                for key, value in data.items():
                    setattr(item, key, value)
                item.save()


def get_cat_id(row):
    text = row["一级分类"]
    if row["二级分类"] != "":
        text = row["二级分类"]
    if row["三级分类"] != "":
        text = row["三级分类"]

    match = re.match(r"^([a-zA-Z0-9]+)", text)
    if match is None:
        raise ValueError(f"get_cat_id fail {text}")
    return match.group(1)


def type_label_to_value(labelo):
    for value, label in INPUT_TYPES.choices:
        if labelo == label:
            return value
    return "natural-resource"


def get_unit(text):
    return text.split("/")[1]
