# Generated by Django 5.2.1 on 2025-05-15 02:49

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0031_alter_emissionsource_type_alter_input_type"),
    ]

    operations = [
        migrations.CreateModel(
            name="AuthorityCategory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="model",
            name="parent_id",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="children",
                to="lca.model",
                verbose_name="父模型，用于记录历史记录",
            ),
        ),
        migrations.CreateModel(
            name="Authority",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(max_length=100, unique=True, verbose_name="机构名称"),
                ),
                (
                    "code",
                    models.CharField(max_length=100, unique=True, verbose_name="认证公司代码"),
                ),
                ("active", models.BooleanField(default=True, verbose_name="机构状态")),
                ("description", models.TextField(verbose_name="机构描述")),
                ("address", models.CharField(max_length=100, verbose_name="机构地址")),
                (
                    "avatar",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.file",
                        verbose_name="机构图标",
                    ),
                ),
                (
                    "geography",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="lca.geography",
                        verbose_name="机构所在区域",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Certification",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("principal", models.CharField(max_length=100, verbose_name="委托人")),
                (
                    "product_name",
                    models.CharField(max_length=100, verbose_name="产品名称"),
                ),
                (
                    "manufacturer_name",
                    models.CharField(max_length=100, verbose_name="生产企业名称"),
                ),
                (
                    "application_status",
                    models.CharField(
                        choices=[
                            ("waiting", "等待受理"),
                            ("accepted", "已受理"),
                            ("rejected", "拒绝受理"),
                            ("withdrawn", "已撤回申请"),
                        ],
                        db_index=True,
                        max_length=100,
                        verbose_name="受理状态",
                    ),
                ),
                (
                    "certification_status",
                    models.CharField(
                        choices=[
                            ("waiting", "等待认证"),
                            ("ongoing", "认证中"),
                            ("approved", "已通过"),
                            ("rejected", "未通过"),
                        ],
                        db_index=True,
                        max_length=100,
                        null=True,
                        verbose_name="认证状态",
                    ),
                ),
                (
                    "application_finish_time",
                    models.DateTimeField(db_index=True, verbose_name="受理完成时间"),
                ),
                (
                    "certification_finish_time",
                    models.DateTimeField(db_index=True, verbose_name="认证完成时间"),
                ),
                (
                    "create_time",
                    models.DateTimeField(auto_now_add=True, db_index=True, verbose_name="创建时间"),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, db_index=True, verbose_name="更新时间"),
                ),
                (
                    "agency_relationship",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="certification_agency_relationship",
                        to="lca.file",
                        verbose_name="委托关系",
                    ),
                ),
                (
                    "authority",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="lca.authority",
                        verbose_name="认证机构",
                    ),
                ),
                (
                    "bom",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="certification_bom",
                        to="lca.file",
                        verbose_name="原辅材料清单",
                    ),
                ),
                (
                    "business_license",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="certification_business_license",
                        to="lca.file",
                        verbose_name="营业执照",
                    ),
                ),
                (
                    "intellectual_property",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="certification_intellectual_property",
                        to="lca.file",
                        verbose_name="知识产权关系",
                    ),
                ),
                (
                    "letter_of_authorization",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="certification_letter_of_authorization",
                        to="lca.file",
                        verbose_name="委托书",
                    ),
                ),
                (
                    "model",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        to="lca.model",
                        verbose_name="模型历史",
                    ),
                ),
                (
                    "organizational_chart",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="certification_organizational_chart",
                        to="lca.file",
                        verbose_name="生产企业组织机构图",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="file",
            name="capability",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="capabilities",
                to="lca.certification",
                verbose_name="保证能力相关管理文件或目录",
            ),
        ),
        migrations.AddField(
            model_name="file",
            name="carbon_reduction_plan",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="plans",
                to="lca.certification",
                verbose_name="产品降碳方案或计划",
            ),
        ),
        migrations.AddField(
            model_name="file",
            name="equipment",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="equipments",
                to="lca.certification",
                verbose_name="主要生产、计量设备清单",
            ),
        ),
        migrations.AddField(
            model_name="file",
            name="label",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="labels",
                to="lca.certification",
                verbose_name="标识认证数据和信息采集清单",
            ),
        ),
        migrations.AddField(
            model_name="file",
            name="other",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="others",
                to="lca.certification",
                verbose_name="其它",
            ),
        ),
        migrations.AddField(
            model_name="file",
            name="process_flow",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="certification_process_flows",
                to="lca.certification",
                verbose_name="产品工艺流程图",
            ),
        ),
        migrations.CreateModel(
            name="CertificationInfo",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("plan", "认证方案"),
                            ("file", "文件评审"),
                            ("on-site", "现场"),
                            ("footprint", "碳足迹核查"),
                            ("verify-report", "核查报告"),
                            ("re-verify-report", "复合报告"),
                            ("certificate", "证书"),
                            ("supervision", "监督"),
                        ],
                        db_index=True,
                        max_length=100,
                        verbose_name="认证信息状态",
                    ),
                ),
                (
                    "file_model",
                    models.BooleanField(null=True, verbose_name="文件评审-生命周期模型"),
                ),
                (
                    "file_material",
                    models.BooleanField(null=True, verbose_name="文件评审-认证材料"),
                ),
                ("on_site", models.BooleanField(null=True, verbose_name="现场检查")),
                (
                    "footprint",
                    models.BooleanField(null=True, verbose_name="碳足迹核查"),
                ),
                ("supervision", models.BooleanField(null=True, verbose_name="监督")),
                (
                    "certificate",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="certification_certificate",
                        to="lca.file",
                        verbose_name="证书",
                    ),
                ),
                (
                    "certification",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="info",
                        to="lca.certification",
                        verbose_name="关联认证",
                    ),
                ),
                (
                    "file_material_reject_report",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="certification_file_material_reject_report",
                        to="lca.file",
                        verbose_name="文件评审-认证材料-不通过报告",
                    ),
                ),
                (
                    "file_model_reject_report",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="certification_file_model_reject_report",
                        to="lca.file",
                        verbose_name="文件评审-生命周期模型-不通过报告",
                    ),
                ),
                (
                    "footprint_reject_report",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="certification_footprint_reject_report",
                        to="lca.file",
                        verbose_name="碳足迹核查-不通过报告",
                    ),
                ),
                (
                    "footprint_report",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="certification_footprint_report",
                        to="lca.file",
                        verbose_name="碳足迹核查报告",
                    ),
                ),
                (
                    "footprint_report2",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="certification_footprint_report2",
                        to="lca.file",
                        verbose_name="碳足迹复合报告",
                    ),
                ),
                (
                    "on_site_reject_report",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="certification_on_site_reject_report",
                        to="lca.file",
                        verbose_name="现场检查-不通过报告",
                    ),
                ),
                (
                    "on_site_report",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="certification_on_site_report",
                        to="lca.file",
                        verbose_name="现场检查报告",
                    ),
                ),
                (
                    "on_site_report2",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="certification_on_site_report2",
                        to="lca.file",
                        verbose_name="现场检查报告",
                    ),
                ),
                (
                    "plan",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="certification_info_plan",
                        to="lca.file",
                        verbose_name="认证方案",
                    ),
                ),
                (
                    "reject_report",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="certification_reject_report",
                        to="lca.file",
                        verbose_name="不通过的报告",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Manufacturer",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(max_length=255, unique=True, verbose_name="名称"),
                ),
                (
                    "create_time",
                    models.DateTimeField(auto_now_add=True, db_index=True, verbose_name="创建时间"),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, db_index=True, verbose_name="更新时间"),
                ),
                (
                    "geography",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.geography",
                        verbose_name="区域",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="certification",
            name="manufacturer",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to="lca.manufacturer",
                verbose_name="生产企业",
            ),
        ),
    ]
