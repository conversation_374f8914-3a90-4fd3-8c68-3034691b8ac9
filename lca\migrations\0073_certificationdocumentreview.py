# Generated by Django 5.2.1 on 2025-07-07 12:11

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0072_certificationplan_file"),
    ]

    operations = [
        migrations.CreateModel(
            name="CertificationDocumentReview",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "model_accepted",
                    models.BooleanField(null=True, verbose_name="生命周期模型是否通过"),
                ),
                (
                    "material_accepted",
                    models.BooleanField(null=True, verbose_name="认证材料是否通过"),
                ),
                (
                    "create_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "certification",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="document_review",
                        to="lca.certification",
                        verbose_name="认证记录",
                    ),
                ),
                (
                    "material_file",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="document_review_material_file",
                        to="lca.file",
                        verbose_name="认证材料审批文件",
                    ),
                ),
                (
                    "model_file",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="document_review_model_file",
                        to="lca.file",
                        verbose_name="生命周期模型审批文件",
                    ),
                ),
            ],
        ),
    ]
