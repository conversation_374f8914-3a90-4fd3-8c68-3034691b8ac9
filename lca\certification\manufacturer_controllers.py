from datetime import date
from ninja import Query, Router
from infra.PageNumberPagination import PageNumberPagination
from infra.filter import next_day, filter
from infra.decorators import certificated_user_required_manufacturer
from lca.accounting.models import CERTIFICATE_PRODUCT, Geography, Model
from lca.certification.models import (
    CERTIFICATION_APPLICATION_STATUS,
    CERTIFICATION_STATUS,
    SUPERVISION_STATUS,
    Authority,
    AuthorityProduct,
    Certification,
)
from lca.certification.schema import (
    CertificationInSchema,
    CertificationManufactureRowSchema,
    CertificationManufacturerSchema,
    CopyModelFileSchema,
    PageNumberPaginationRegion,
)
from ninja.pagination import paginate

from lca.certification.services import CertificationService
from lca.policies import authorize

router = Router(
    tags=["碳认证-生产企业"],
)


@router.get(
    "authority",
    response=PageNumberPaginationRegion,
    summary="认证公司列表",
    operation_id="certGetAuthorityList",
)
@certificated_user_required_manufacturer()
def get_authority_list(
    request,
    product: CERTIFICATE_PRODUCT = Query(..., title="产品"),
    page_size: int = Query(..., title="page_size", min=1),
    page: int = Query(..., title="page", min=1),
):
    region_ids = [6401, 1302]

    queryset = Authority.objects.filter(
        id__in=AuthorityProduct.objects.filter(product=product).values_list("authority_id", flat=True)
    )

    return {
        "items": queryset[(page - 1) * page_size : (page) * page_size],
        "count": queryset.count(),
        "product": product,
        "regions": Geography.objects.filter(id__in=region_ids),
    }


@router.post(
    "",
    response=CertificationManufacturerSchema,
    summary="申请认证",
    operation_id="manufacturerCreateApply",
)
@certificated_user_required_manufacturer()
def create_apply(request, data: CertificationInSchema):
    manufacturer = request.user.manufacturer
    authority = Authority.objects.get(pk=data.authority_id)
    item = CertificationService.create(data, manufacturer=manufacturer, authority=authority)
    return item


@router.put(
    "{id}",
    response=CertificationManufacturerSchema,
    summary="编辑认证",
    operation_id="manufacturerUpdateApply",
)
@certificated_user_required_manufacturer()
def update_apply(request, id: int, data: CertificationInSchema):
    item = Certification.objects.get(pk=id)
    authorize(request.user, "update", item)
    item = CertificationService.update(item, data)
    return item


@router.get(
    "{id}",
    response=CertificationManufacturerSchema,
    summary="认证详情",
    operation_id="manufacturerGetApplyDetail",
)
@certificated_user_required_manufacturer()
def get_certification_detail(request, id: int):
    item = Certification.objects.get(pk=id)
    authorize(request.user, "view", item)
    return item


@router.post(
    "{id}/withdraw",
    response=CertificationManufacturerSchema,
    summary="撤回申请",
    operation_id="manufacturerWithdrawApply",
)
@certificated_user_required_manufacturer()
def withdraw_certification(request, id: int):
    item = Certification.objects.get(pk=id)
    authorize(request.user, "withdraw", item)
    item = CertificationService.withdraw(item)
    return item


@router.get(
    "",
    response=list[CertificationManufactureRowSchema],
    summary="认证列表",
    operation_id="manufacturerGetApplyList",
)
@paginate(PageNumberPagination)
@certificated_user_required_manufacturer()
def get_certification_list(
    request,
    product_name: str | None = Query(None, title="产品名称"),
    authority_name: str | None = Query(None, title="认证机构名称"),
    application_status: CERTIFICATION_APPLICATION_STATUS | None = Query(None, title="受理状态"),
    certification_status: CERTIFICATION_STATUS | None = Query(None, title="认证状态"),
    supervision_status: SUPERVISION_STATUS | None = Query(None, title="监督管理状态"),
    date_start: date | None = Query(None, title="开始日期"),
    date_end: date | None = Query(None, title="结束日期"),
):
    return filter(
        Certification.objects.order_by("-create_time"),
        manufacturer=request.user.manufacturer,
        product_name__icontains=product_name,
        authority__name__icontains=authority_name,
        certification_status=certification_status,
        application_status=application_status,
        supervision_status=supervision_status,
        create_time__gte=date_start,
        create_time__lte=next_day(date_end),
    ).prefetch_related("authority")


@router.post(
    "copy-file-from-model/{id}",
    response=CopyModelFileSchema,
    summary="复用模型文件",
    operation_id="manufacturerCopyFileFromModel",
)
@certificated_user_required_manufacturer()
def copy_file_from_model(request, id: int):
    model = Model.objects.get(id=id)
    return CertificationService.copy_file_from_model(model)
