from django.utils.translation import gettext as _
from ninja.errors import HttpError


class ResourceCenterError:
    APPLICABLE_AREA_REQUIRED = (422, _("请选择适用区域"))
    APPLICABLE_AREA_INVALID = (422, _("适用区域参数错误"))
    CATEGORY_NOT_FIRST_LEVEL = (422, _("相关行业参数错误"))
    ALL_FIELDS_EMPTY = (422, _("文件、链接、内容不能都为空"))
    INVALID_URL = (422, _("文章链接不是正确的链接地址"))
    PUBLISHED_CANNOT_REPEAT = (422, _("资料已经发布，请勿重复发布"))
    NOT_PUBLISHED_CANNOT_UNPUBLISH = (422, _("资料未发布，请勿重复下架"))
    NOT_DRAFT_CANNOT_DELETE = (422, _("资料不在草稿箱中，无法删除"))
    CONTENT_TYPE_INVALID = (422, _("文章类型参数错误"))
    RESOURCE_ALREADY_PUBLISHED_CANNOT_EDIT = (
        422,
        _("资料已经发布，无法更新，请先下架"),
    )
    COVER_IMAGE_NOT_EXIST = (422, _("请先上传正确的封面图片"))
    FILE_NOT_EXIST = (422, _("请先上传正确的文件"))
    PERMISSION_DENIED = (403, _("您没有权限执行该操作"))


def raise_error(error: ResourceCenterError, **kwargs):
    code, message = error
    raise HttpError(code, message=_(message))
