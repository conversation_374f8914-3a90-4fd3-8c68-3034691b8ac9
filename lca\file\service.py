import base64
import hashlib
import io
import logging
import os
import subprocess
import time
from urllib.parse import quote
from uuid import uuid4
import zipfile

import matplotlib
import requests
from django.http import FileResponse, StreamingHttpResponse
from matplotlib.font_manager import FontProperties
from ninja.errors import HttpError
import urllib

from lca.file.models import File

matplotlib.use("Agg")  # 在导入pyplot之前设置
import matplotlib.pyplot as plt
from django.conf import settings
from docx import Document
from docx.enum.table import WD_CELL_VERTICAL_ALIGNMENT
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.shared import Pt, RGBColor, Inches

from infra.minio import minio_service
from lca.accounting.models import BOUNDARIES, Output
from lca.accounting.services import LifeCycleService

logger = logging.getLogger("django")


def download_image(url):
    response = requests.get(url)
    if response.status_code == 200:
        return io.BytesIO(response.content)
    else:
        raise HttpError(500, "模型图片下载失败")


def convert_to_pdf(docx_path, pdf_path):
    try:
        pdf_options = "writer_pdf_Export:SelectPdfVersion=1.5:EmbedStandardFonts=true"
        subprocess.run(
            [
                "libreoffice",
                "--headless",
                "--nologo",
                "--norestore",
                "--nofirststartwizard",
                "--convert-to",
                f"pdf:{pdf_options}",
                "--outdir",
                pdf_path,
                docx_path,
            ],
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
        )
        logger.info(f"转换成功: {pdf_path}")
    except subprocess.CalledProcessError as e:
        logger.error(f"转换失败: {e.stderr.decode()}")
    except Exception as e:
        logger.error(f"转换失败: {e}")


def upload_to_storage(doc_stream, file_name, name):
    path = FileService.get_path()
    ext_name = "pdf"
    object_name = f"{path}/{file_name}.{ext_name}"

    return FileService.upload(object_name, doc_stream, name, doc_stream.getbuffer().nbytes)


def set_cover_table(cover_table, name):
    cover_table_cell = cover_table.cell(0, 1)
    cover_table_cell.text = name

    # 获取单元格的段落对象
    paragraph = cover_table_cell.paragraphs[0]
    # 设置水平居中
    paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

    # 设置垂直居中
    cover_table_cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER


def set_info_table(info_table, name, image_url):
    info_table_cell = info_table.cell(0, 1)
    info_table_cell.text = name
    # 获取单元格的段落对象
    paragraph = info_table_cell.paragraphs[0]

    # 设置水平居中
    paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

    # 设置垂直居中
    info_table_cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER

    image_table_cell = info_table.cell(3, 1)
    # 添加图片
    if image_url:
        image_stream = download_image(image_url)
        image_table_cell.add_paragraph().add_run().add_picture(image_stream, width=image_table_cell.width)


def set_function_table(function_table, functional_unit):
    if functional_unit:
        function_table_cell = function_table.cell(0, 0)
        info = f"以 {functional_unit} 为功能单位或声明单位。"
        function_table_cell.text = info
        # 获取单元格的段落对象
        paragraph = function_table_cell.paragraphs[0]

        # 设置水平居中
        paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT


def set_boundary_table(system_boundary_table, boundary):
    boundary_table_cell = system_boundary_table.cell(0, 0)
    inputs = "☑　原材料获取阶段"
    crafts = "☑　生产阶段"
    distribution = "☑　运输(交付)阶段"
    usage_maintenance = "☑　使用阶段"
    eol_stage = "☑　生命末期阶段"

    text = f"{inputs}　{crafts}　{distribution}"
    if boundary == BOUNDARIES.CRADLE_GRAVE:
        text += f"　{usage_maintenance}　{eol_stage}"

    boundary_table_cell.text = text

    # 获取单元格的段落对象
    paragraph = boundary_table_cell.paragraphs[0]

    # 设置水平居中
    paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT


def set_rule_table(rule_table, rule):
    if rule:
        rule_table_cell = rule_table.cell(0, 0)
        rule_table_cell.text = f"采用的取舍准则以 {rule} 为依据，"

        # 获取单元格的段落对象
        paragraph = rule_table_cell.paragraphs[0]

        # 设置水平居中
        paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT


def set_time_scope_table(time_scope_table, year):
    time_scope_table_cell = time_scope_table.cell(0, 0)
    time_scope_table_cell.text = f" {year} 年度。"

    # 获取单元格的段落对象
    paragraph = time_scope_table_cell.paragraphs[0]

    # 设置水平居中
    paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT


def set_result_table(result_title_table, result_each_stage_table, model):
    result_title_table_cell = result_title_table.cell(0, 0)
    result_title_table_cell.text = ""
    paragraph = result_title_table_cell.paragraphs[0]
    paragraph.add_run("表  1").bold = True
    run = paragraph.add_run(f"　　{model.name}　　")
    run.underline = True
    run.bold = True
    paragraph.add_run("生命周期碳排放清单说明").bold = True

    # 获取单元格的段落对象
    paragraph = result_title_table_cell.paragraphs[0]
    # 设置水平居中
    paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

    # 设置原材料获取阶段
    inputs_data = model.inputs.all()

    for i, inputs in enumerate(inputs_data):
        if i > 0:
            new_row = result_each_stage_table.add_row()
            result_each_stage_table.cell(1, 0).merge(new_row.cells[0])
            cell = result_each_stage_table.cell(1, 0)
            cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
            paragraph = cell.paragraphs[0]
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

        cell = result_each_stage_table.cell(i + 1, 1)
        cell.text = f"{inputs.name}"
        cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
        paragraph = cell.paragraphs[0]
        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

        cell = result_each_stage_table.cell(i + 1, 2)
        cell.text = f"{inputs.emmission_source.amount}"
        cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
        paragraph = cell.paragraphs[0]
        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

        cell = result_each_stage_table.cell(i + 1, 3)
        cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
        cell.text = f"{inputs.co2e}"
        paragraph = cell.paragraphs[0]
        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

    # 设置生产阶段
    sep_index = len(result_each_stage_table.rows)
    crafts_data = model.crafts.all()
    stage_name = "生产"

    new_row = result_each_stage_table.add_row()
    new_row.cells[0].text = stage_name
    new_row.cells[0].vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
    paragraph = new_row.cells[0].paragraphs[0]
    paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

    for i, crafts in enumerate(crafts_data):
        if i > 0:
            new_row = result_each_stage_table.add_row()
            result_each_stage_table.cell(sep_index, 0).merge(new_row.cells[0])
            cell = result_each_stage_table.cell(sep_index, 0)
            cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
            paragraph = cell.paragraphs[0]
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

        cell = result_each_stage_table.cell(sep_index + i, 1)
        cell.text = f"{crafts.name}"
        cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
        paragraph = cell.paragraphs[0]
        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

        cell = result_each_stage_table.cell(sep_index + i, 3)
        cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
        cell.text = f"{crafts.co2e}"
        paragraph = cell.paragraphs[0]
        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

    # 设置运输交付阶段
    sep_index = len(result_each_stage_table.rows)
    distribution_data = model.distribution.all()
    stage_name = "运输/交付"

    new_row = result_each_stage_table.add_row()
    new_row.cells[0].text = stage_name
    new_row.cells[0].vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
    paragraph = new_row.cells[0].paragraphs[0]
    paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

    for i, distribution in enumerate(distribution_data):
        if i > 0:
            new_row = result_each_stage_table.add_row()
            result_each_stage_table.cell(sep_index, 0).merge(new_row.cells[0])
            cell = result_each_stage_table.cell(sep_index, 0)
            cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
            paragraph = cell.paragraphs[0]
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

        cell = result_each_stage_table.cell(sep_index + i, 1)
        cell.text = f"{distribution.origin}" + "-" + f"{distribution.terminal}"
        cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
        paragraph = cell.paragraphs[0]
        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

        transports_first = distribution.transports.all().first()
        out_put = Output.objects.filter(
            emission_source=transports_first.emmission_source,
            input__isnull=True,
            process__isnull=True,
        ).all()
        if out_put:
            amount = out_put.first().amount
            cell = result_each_stage_table.cell(sep_index + i, 2)
            cell.text = f"{amount}"
            cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
            paragraph = cell.paragraphs[0]
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

        cell = result_each_stage_table.cell(sep_index + i, 3)
        cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
        cell.text = f"{distribution.co2e}"
        paragraph = cell.paragraphs[0]
        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

    # 设置使用阶段
    sep_index = len(result_each_stage_table.rows)
    usage_maintenance_data = model.usage_maintenance.all()
    stage_name = "使用"

    new_row = result_each_stage_table.add_row()
    new_row.cells[0].text = stage_name
    new_row.cells[0].vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
    paragraph = new_row.cells[0].paragraphs[0]
    paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

    for i, usage_maintenance in enumerate(usage_maintenance_data):
        if i > 0:
            new_row = result_each_stage_table.add_row()
            result_each_stage_table.cell(sep_index, 0).merge(new_row.cells[0])
            cell = result_each_stage_table.cell(sep_index, 0)
            cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
            paragraph = cell.paragraphs[0]
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

        cell = result_each_stage_table.cell(sep_index + i, 1)
        cell.text = f"{usage_maintenance.usage_pathway}"
        cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
        paragraph = cell.paragraphs[0]
        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

        cell = result_each_stage_table.cell(sep_index + i, 3)
        cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
        cell.text = f"{usage_maintenance.co2e}"
        paragraph = cell.paragraphs[0]
        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

    # 设置生命末期阶段
    sep_index = len(result_each_stage_table.rows)
    eol_stage_data = model.eol_stage.all()
    stage_name = "生命末期"

    new_row = result_each_stage_table.add_row()
    new_row.cells[0].text = stage_name
    new_row.cells[0].vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
    paragraph = new_row.cells[0].paragraphs[0]
    paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

    for i, eol_stage in enumerate(eol_stage_data):
        if i > 0:
            new_row = result_each_stage_table.add_row()
            result_each_stage_table.cell(sep_index, 0).merge(new_row.cells[0])
            cell = result_each_stage_table.cell(sep_index, 0)
            cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
            paragraph = cell.paragraphs[0]
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

        cell = result_each_stage_table.cell(sep_index + i, 1)
        cell.text = f"{eol_stage.product.name + '-' + eol_stage.eol_model.name}"
        cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
        paragraph = cell.paragraphs[0]
        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

        cell = result_each_stage_table.cell(sep_index + i, 2)
        cell.text = f"{eol_stage.eol_model.factor}"
        cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
        paragraph = cell.paragraphs[0]
        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

        cell = result_each_stage_table.cell(sep_index + i, 3)
        cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
        cell.text = f"{eol_stage.co2e}"
        paragraph = cell.paragraphs[0]
        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER


def set_result_explain_table(result_explain_table, model, ret):
    result_explain_table_cell = result_explain_table.cell(0, 0)
    result_explain_table_cell.text = ""
    blank = "　　"
    # 获取单元格的段落对象
    paragraph = result_explain_table_cell.paragraphs[0]
    paragraph.add_run(blank).underline = True
    paragraph.add_run("公司(填写产品生产者的全名)生产的")
    paragraph.add_run(f"　　{model.name}　　").underline = True
    paragraph.add_run("，从")
    paragraph.add_run("　　摇篮　　").underline = True
    paragraph.add_run("(填写某生命周期阶段)到")
    paragraph.add_run(f"　　{'大门' if model.boundary == BOUNDARIES.CRADLE_GATE else '坟墓'}　　").underline = True
    paragraph.add_run("(填写某生命周期阶段)生命周期碳足迹为")
    paragraph.add_run(f"　　{ret.co2e}　　").underline = True
    paragraph.add_run("kgCO₂e。")
    paragraph.add_run("各生命周期阶段的温室气体排放情况如表2和图2所示。")
    # 设置左对齐
    paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT


def set_table2_title_each_stage_table(table2_title_table, table2_each_stage_table, model, lca_ret):
    cell = table2_title_table.cell(0, 0)
    cell.text = ""
    paragraph = cell.paragraphs[0]
    paragraph.add_run("表 2 ").bold = True
    run1 = paragraph.add_run(f"　　{model.name}　　")
    run1.underline = True
    run1.bold = True
    paragraph.add_run("生命周期各阶段碳排放情况").bold = True

    # 设置居中齐
    paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

    # 获取各阶段排放情况
    table2_each_stage_table.add_row()
    inputs_row = table2_each_stage_table.rows[-1]

    inputs_row.cells[0].text = ""
    paragraph = inputs_row.cells[0].paragraphs[0]  # 获取或创建段落
    paragraph.add_run("原材料获取")
    inputs_row.cells[1].text = ""
    inputs_row.cells[1].paragraphs[0].add_run(f"{lca_ret.co2e_each_stage[0].co2e}")
    inputs_row.cells[2].text = ""
    inputs_row.cells[2].paragraphs[0].add_run(
        f"{0 if not lca_ret.co2e else round(lca_ret.co2e_each_stage[0].co2e / lca_ret.co2e * 100, 2)}"
    )

    table2_each_stage_table.add_row()
    crafts_row = table2_each_stage_table.rows[-1]
    crafts_row.cells[0].text = ""
    crafts_row.cells[0].paragraphs[0].add_run("生产")
    crafts_row.cells[1].text = ""
    crafts_row.cells[1].paragraphs[0].add_run(f"{lca_ret.co2e_each_stage[1].co2e}")
    crafts_row.cells[2].text = ""
    crafts_row.cells[2].paragraphs[0].add_run(
        f"{0 if not lca_ret.co2e else round(lca_ret.co2e_each_stage[1].co2e / lca_ret.co2e * 100, 2)}"
    )

    table2_each_stage_table.add_row()
    distributions_row = table2_each_stage_table.rows[-1]
    distributions_row.cells[0].text = ""
    distributions_row.cells[0].paragraphs[0].add_run("运输(交付)")
    distributions_row.cells[1].text = ""
    distributions_row.cells[1].paragraphs[0].add_run(f"{lca_ret.co2e_each_stage[2].co2e}")
    distributions_row.cells[2].text = ""
    distributions_row.cells[2].paragraphs[0].add_run(
        f"{0 if not lca_ret.co2e else round(lca_ret.co2e_each_stage[2].co2e / lca_ret.co2e * 100, 2)}"
    )

    table2_each_stage_table.add_row()
    usage_maintenances_row = table2_each_stage_table.rows[-1]
    usage_maintenances_row.cells[0].text = ""
    usage_maintenances_row.cells[0].paragraphs[0].add_run("使用")
    usage_maintenances_row.cells[1].text = ""
    usage_maintenances_row.cells[1].paragraphs[0].add_run(f"{lca_ret.co2e_each_stage[3].co2e}")
    usage_maintenances_row.cells[2].text = ""
    usage_maintenances_row.cells[2].paragraphs[0].add_run(
        f"{0 if not lca_ret.co2e else round(lca_ret.co2e_each_stage[3].co2e / lca_ret.co2e * 100, 2)}"
    )

    table2_each_stage_table.add_row()
    eol_stages_row = table2_each_stage_table.rows[-1]
    eol_stages_row.cells[0].text = ""
    eol_stages_row.cells[0].paragraphs[0].add_run("生命末期")
    eol_stages_row.cells[1].text = ""
    eol_stages_row.cells[1].paragraphs[0].add_run(f"{lca_ret.co2e_each_stage[4].co2e}")
    eol_stages_row.cells[2].text = ""
    eol_stages_row.cells[2].paragraphs[0].add_run(
        f"{0 if not lca_ret.co2e else round(lca_ret.co2e_each_stage[4].co2e / lca_ret.co2e * 100, 2)}"
    )

    table2_each_stage_table.add_row()
    total_row = table2_each_stage_table.rows[-1]
    total_row.cells[0].text = ""
    total_row.cells[0].paragraphs[0].add_run("总计")
    total_row.cells[1].text = ""
    total_row.cells[1].paragraphs[0].add_run(f"{lca_ret.co2e}")
    total_row.cells[2].text = ""
    total_row.cells[2].paragraphs[0].add_run("100")

    for row in table2_each_stage_table.rows:
        for cell in row.cells:
            cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
            paragraph = cell.paragraphs[0]
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER


def set_each_stage_bar_chart(each_stage_bar_chart, model, lca_ret):
    # 设置字体
    # 设置中文字体
    plt.rcParams["font.sans-serif"] = ["WenQuanYi Zen Hei", "SimHei"]
    plt.rcParams["axes.unicode_minus"] = False
    plt.rcParams["xtick.labelsize"] = 9  # X轴刻度字号

    font_path = os.path.join(settings.TTF_ROOT, "simhei.ttf")
    font = FontProperties(fname=font_path, size=9)
    cell = each_stage_bar_chart.cell(0, 0)
    cell.text = ""
    # 准备数据
    stage = [
        "原材料与能源获取阶段",
        "生产线段",
        "分销阶段",
        "使用和维护阶段",
        "EOL阶段",
    ]
    co2e = [
        lca_ret.co2e_each_stage[0].co2e,
        lca_ret.co2e_each_stage[1].co2e,
        lca_ret.co2e_each_stage[2].co2e,
        lca_ret.co2e_each_stage[3].co2e,
        lca_ret.co2e_each_stage[4].co2e,
    ]

    plt.figure(figsize=(6, 4))  # 设置宽度为6英寸，高度为4英寸
    plt.bar(stage, co2e, color="skyblue", width=0.6, edgecolor="black", linewidth=1)

    for bar in plt.bar(stage, co2e):
        height = bar.get_height()
        plt.text(
            bar.get_x() + bar.get_width() / 2.0,
            height,
            f"{int(height)}",
            ha="center",
            va="bottom",
        )

    plt.xlabel("生命周期阶段", fontproperties=font)
    plt.ylabel("碳排放量（kgCO2e）", fontproperties=font)
    plt.grid(axis="y", linestyle="--", alpha=0.7)
    plt.tight_layout()

    # 将图表保存到内存流
    img_stream = io.BytesIO()
    plt.savefig(img_stream, format="png", dpi=800, bbox_inches="tight")
    img_stream.seek(0)  # 重置指针到流的开头
    plt.close()

    cell.add_paragraph()
    paragraph = cell.paragraphs[0]
    paragraph.add_run().add_picture(img_stream, width=Inches(6.0))

    cell.add_paragraph()
    paragraph = cell.paragraphs[1]
    run = paragraph.add_run("注：具体产品生命周期阶段碳排放分布图一般以饼状图或柱形图表示各生命周期阶段的碳排放情况。")
    run.font.name = "黑体"
    run.font.size = Pt(9)
    run.font.color.rgb = RGBColor(128, 128, 128)
    paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT

    cell.add_paragraph()
    paragraph = cell.paragraphs[2]
    paragraph.add_run("\n图2").bold = True
    run1 = paragraph.add_run(f"　{model.name}　")
    run1.underline = True
    run1.bold = True
    paragraph.add_run("各生命周期阶段碳排放分布图（按阶段的柱状图）").bold = True
    paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER


class FileService:
    allowed_extensions = [
        "jpg",
        "jpeg",
        "png",
        "gif",
        "bmp",
        "webp",
        "svg",
        "ico",
        "doc",
        "docx",
        "xls",
        "xlsx",
        "pdf",
    ]

    @staticmethod
    def get_file_name():
        return uuid4().hex

    @staticmethod
    def get_path():
        return time.strftime("%Y%m%d", time.localtime())

    @staticmethod
    def get_file_name_by_md5(file: object):
        # 创建md5对象
        md5_hash = hashlib.md5()
        # 更新md5对象
        md5_hash.update(file.read())
        return md5_hash.hexdigest()

    @staticmethod
    def copy(file: File):
        print("file", file.__dict__)
        ext_name = file.id.split(".")[-1]
        path = FileService.get_path()
        file_name = FileService.get_file_name()
        object_name = "{path}/{file_name}.{ext}".format(path=path, file_name=file_name, ext=ext_name)
        size = file.size
        minio_service.copy_object(file.id, object_name)
        file = File(
            id=object_name,
            name=file.name,
            size=size,
        )
        file.save()
        return file

    @staticmethod
    def download(file_name: str):
        minio_service.get_object(file_name)

    @staticmethod
    def upload_file(filepath: str, name: str = ""):
        ext_name = filepath.split(".")[-1]
        path = FileService.get_path()
        file_name = FileService.get_file_name()
        object_name = "{path}/{file_name}.{ext}".format(path=path, file_name=file_name, ext=ext_name)
        size = os.path.getsize(filepath)
        try:
            minio_service.upload_file(object_name, filepath)
            file = File(
                id=object_name,
                name=name,
                size=size,
            )
            file.save()
            return file
        except Exception as e:
            logger.info("upload-file error: {}", e)
            raise HttpError(400, "上传失败")

    @staticmethod
    def upload(file_name: str, file_stream: object, name: str, file_size: int = 0):
        minio_service.put_object(file_name, file_stream, file_size)
        file = File(
            id=file_name,
            name=name,
            size=file_size if file_size else 0,
        )
        file.save()
        return file

    @staticmethod
    def upload_base64(file_stream: str):
        if file_stream.startswith("http"):
            return file_stream

        ext_name = "jpg"
        if "png" in file_stream:
            ext_name = "png"
        elif "jpg" in file_stream:
            ext_name = "jpg"
        elif "jpeg" in file_stream:
            ext_name = "jpeg"

        file_stream = file_stream.replace("data:image/png;base64,", "")
        file_stream = file_stream.replace("data:image/jpg;base64,", "")
        file_stream = file_stream.replace("data:image/jpeg;base64,", "")
        missing_padding = 4 - len(file_stream) % 4
        if missing_padding:
            file_stream += "=" * missing_padding
        stream = base64.b64decode(file_stream)
        # 创建 BytesIO 流
        stream = io.BytesIO(stream)
        stream_size = stream.getbuffer().nbytes

        file_name = FileService.get_file_name()
        path = FileService.get_path()
        object_name = "{path}/{file_name}.{ext}".format(path=path, file_name=file_name, ext=ext_name)
        minio_service.put_object(object_name, stream, stream_size)

        stream.close()
        return "{}/{}".format(settings.MINIO_BUCKET, object_name)

    @classmethod
    def generate_report(cls, model):
        """
        生成模型的报告
        """
        if model.report is not None:
            raise HttpError(400, "报告已生成")

        template_path = settings.TEMPLATE_ROOT
        template_name = "产品碳足迹报告.docx"
        lca = LifeCycleService.get_lca(model)

        report = Document(os.path.join(template_path, template_name))

        # 填写报告封面
        cover_table = report.tables[0]
        set_cover_table(cover_table, model.name)

        # 填写产品信息
        info_table = report.tables[2]
        image_url = model.image.url if model.image else None
        set_info_table(info_table, model.name, image_url)

        # 填写功能单位
        function_table = report.tables[3]
        set_function_table(function_table, model.functional_unit)

        # 填写系统边界
        system_boundary_table = report.tables[4]
        set_boundary_table(system_boundary_table, model.boundary)

        # 填写取舍规则
        rule_table = report.tables[6]
        set_rule_table(rule_table, model.rule)

        # 填写时间范围
        time_scope_table = report.tables[7]
        set_time_scope_table(time_scope_table, model.year)

        # 填写清单结果及计算
        result_title_table = report.tables[8]
        result_each_stage_table = report.tables[9]
        set_result_table(result_title_table, result_each_stage_table, model)

        # 填写结果说明
        result_explain_table = report.tables[10]
        set_result_explain_table(result_explain_table, model, lca)

        # 填写表2名称及各阶段排放情况
        table2_title_table = report.tables[11]
        table2_each_stage_table = report.tables[12]
        set_table2_title_each_stage_table(table2_title_table, table2_each_stage_table, model, lca)

        # 填写各阶段排放情况柱状图
        each_stage_bar_chart = report.tables[13]
        set_each_stage_bar_chart(each_stage_bar_chart, model, lca)

        report.save(os.path.join(template_path, "report.docx"))
        input_file = template_path + "/report.docx"
        output = template_path + "/report.pdf"

        convert_to_pdf(input_file, template_path)

        try:
            with open(output, "rb") as f:
                pdf_content = f.read()

            pdf_stream = io.BytesIO(pdf_content)
            pdf_stream.seek(0)

            # 上传到存储服务
            file_name = FileService.get_file_name()
            path = FileService.get_path()
            ext_name = "pdf"
            object_name = f"{path}/{file_name}.{ext_name}"
            file = FileService.upload(object_name, pdf_stream, "碳核算报告.pdf", pdf_stream.getbuffer().nbytes)
            print("======================================")
            print("file_name:", file_name)

            model.report_id = file.id
            model.save()

        except FileNotFoundError:
            logger.error(f"错误：文件 {output} 不存在")
        except PermissionError:
            logger.error(f"错误：无权限读取文件 {output}")
        except Exception as e:
            logger.error(f"未知错误: {str(e)}")

    @staticmethod
    def download_file(file: File):
        url = file.url

        filename = file.name

        response = requests.get(url)
        if response.status_code != 200:
            raise HttpError(status_code=500, message="下载失败")

        doc_stream = io.BytesIO(response.content)
        doc_stream.seek(0)

        response = FileResponse(doc_stream)
        filename_utf8 = urllib.parse.quote(filename)
        file_expr = f"filename*=UTF-8''{filename_utf8}"

        response["Content-Disposition"] = f"attachment; {file_expr}"
        return response

    @classmethod
    def download_report(cls, model):
        report_url = model.report.url if model.report else None
        if report_url is None:
            raise HttpError(404, "模型碳足迹报告暂未生成")

        report_file_name = "{}{}".format(model.name, "碳足迹报告.pdf")
        encoded_filename = quote(report_file_name, safe="")  # 移除 safe

        response = requests.get(report_url)
        if response.status_code != 200:
            raise HttpError(status_code=500, message="报告下载失败")

        doc_stream = io.BytesIO(response.content)
        doc_stream.seek(0)

        def chunk_iterator(stream, chunk_size=500 * 1024):
            try:
                stream.seek(0)
                while True:
                    chunk = stream.read(chunk_size)
                    if not chunk:
                        break
                    yield chunk
            finally:
                stream.close()

        streaming_response = StreamingHttpResponse(
            streaming_content=chunk_iterator(doc_stream),  # 分块读取 8KB
            content_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        )
        streaming_response["Content-Disposition"] = f"attachment; filename*=UTF-8''{encoded_filename}"

        return streaming_response

    @staticmethod
    def batch_download(name: str, files: list[str]):
        # 批量下载文件，从 OSS 下载对应文件，然后打包成 zip 文件返回。文件名是 File 的 name 字段
        zip_stream = io.BytesIO()
        with zipfile.ZipFile(zip_stream, "w") as zip_file:
            for file in files:
                file = File.objects.get(pk=file)
                url = file.url
                response = requests.get(url)
                if response.status_code != 200:
                    raise HttpError(status_code=500, message="下载失败")
                zip_file.writestr(file.name, response.content)

        zip_stream.seek(0)

        response = FileResponse(zip_stream)
        filename_utf8 = quote(name, safe="")  # 移除 safe
        response["Content-Disposition"] = f"attachment; filename*=UTF-8''{filename_utf8}"

        return response
