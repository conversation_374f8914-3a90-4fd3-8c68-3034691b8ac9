import csv
from decimal import Decimal

from django.core.management import BaseCommand

from lca.accounting.models import EOLTYPE


class Command(BaseCommand):
    help = "初始化数据"

    def handle(self, *args, **options):
        add_eol_model_factor()


def initial_eol():
    from lca.accounting.models import Product, EndofLifeModel

    products = Product.objects.all()

    eol_type = [EOLTYPE.REUSE]
    eol_name = ["再利用"]
    for product in products:
        for name, eol_type_ in zip(eol_name, eol_type):
            EndofLifeModel.objects.create(product=product, eol_model_type=eol_type_, name=name, factor=-1)


def add_eol_model_factor():
    from lca.accounting.models import EndofLifeModel

    with open("lca/management/commands/data/eol_model.csv", encoding="utf-8") as file:
        reader = csv.DictReader(file)
        for row in reader:
            eol_model = EndofLifeModel.objects.filter(product__name=row["product"])
            if eol_model:
                for eol in eol_model:
                    match eol.eol_model_type:
                        case EOLTYPE.RECYCLE.value:
                            eol.factor = Decimal(row["RECYCLE"])
                        case EOLTYPE.COMPOST.value:
                            eol.factor = Decimal(row["COMPOST"])
                        case EOLTYPE.LANDFILL.value:
                            eol.factor = Decimal(row["LANDFILL"])
                        case EOLTYPE.COMBUST.value:
                            eol.factor = Decimal(row["COMBUST"])
                        case _:
                            eol.factor = Decimal(-1)
                    eol.save()
