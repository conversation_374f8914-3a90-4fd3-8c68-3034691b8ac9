# Generated by Django 5.2.1 on 2025-07-16 08:41

import django.contrib.postgres.fields
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0083_certification_reason_alter_user_certificate_status"),
    ]

    operations = [
        migrations.AlterField(
            model_name="lifecycle",
            name="stage",
            field=models.CharField(
                choices=[
                    ("raw-materials", "原材料获取阶段"),
                    ("production", "生产阶段"),
                    ("use", "使用阶段"),
                    ("distribution", "分销阶段"),
                    ("disposal", "废弃阶段"),
                ],
                default="production",
                max_length=50,
                verbose_name="生命周期阶段",
            ),
        ),
        migrations.AlterField(
            model_name="user",
            name="certificate_status",
            field=models.CharField(
                blank=True,
                choices=[
                    ("none", "未认证"),
                    ("ongoing", "认证中"),
                    ("approved", "认证通过"),
                    ("rejected", "认证未通过"),
                ],
                db_index=True,
                default="none",
                max_length=20,
                null=True,
                verbose_name="认证状态",
            ),
        ),
        migrations.AlterField(
            model_name="usercertification",
            name="status",
            field=models.CharField(
                choices=[
                    ("none", "未认证"),
                    ("ongoing", "认证中"),
                    ("approved", "认证通过"),
                    ("rejected", "认证未通过"),
                ],
                db_index=True,
                default="ongoing",
                max_length=100,
                verbose_name="认证状态",
            ),
        ),
        migrations.CreateModel(
            name="ResourceCenter",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "title",
                    models.CharField(db_comment="内容的标题", max_length=255, verbose_name="标题"),
                ),
                (
                    "summary",
                    models.TextField(
                        blank=True,
                        db_comment="内容摘要，用于列表页展示",
                        null=True,
                        verbose_name="简介",
                    ),
                ),
                (
                    "content",
                    models.TextField(db_comment="内容正文文本", verbose_name="内容"),
                ),
                (
                    "publish_date",
                    models.DateField(
                        db_comment="内容发布的原始日期，格式为YYYY-MM-DD",
                        verbose_name="发布日期",
                    ),
                ),
                (
                    "publish_organization",
                    models.CharField(
                        db_comment="内容的发布单位或机构名称",
                        max_length=255,
                        verbose_name="发布组织",
                    ),
                ),
                (
                    "article_link",
                    models.CharField(
                        blank=True,
                        db_comment="指向外部原文的链接",
                        max_length=255,
                        null=True,
                        verbose_name="文章链接",
                    ),
                ),
                (
                    "category_group",
                    models.CharField(
                        choices=[
                            ("POLICY_NEWS", "政策资讯"),
                            ("CARBON_RESOURCE", "碳资料库"),
                        ],
                        db_comment="用于区分政策资讯或碳资料库",
                        max_length=50,
                        verbose_name="分类组别",
                    ),
                ),
                (
                    "content_type",
                    models.CharField(
                        choices=[
                            ("INFO_ANNOUNCEMENT", "信息公告"),
                            ("DOMESTIC_CARBON_NEWS", "国内碳资讯"),
                            ("INTERNATIONAL_CARBON_NEWS", "国外碳资讯"),
                            ("RESEARCH_REPORT", "研究报告"),
                            ("STANDARD_SPECIFICATION", "标准规范"),
                        ],
                        db_comment="区分内容类型的枚举值",
                        max_length=50,
                        verbose_name="内容类型",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[("DRAFT", "草稿"), ("PUBLISHED", "已发布")],
                        db_comment="内容状态，如草稿或已发布",
                        default="DRAFT",
                        max_length=50,
                        verbose_name="状态",
                    ),
                ),
                (
                    "publish_time",
                    models.DateTimeField(
                        blank=True,
                        db_comment="内容发布时间",
                        null=True,
                        verbose_name="发布时间",
                    ),
                ),
                (
                    "unpublish_time",
                    models.DateTimeField(
                        blank=True,
                        db_comment="内容下架时间，用于临时下架内容",
                        null=True,
                        verbose_name="下架时间",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(auto_now_add=True, db_index=True, verbose_name="创建时间"),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, db_index=True, verbose_name="更新时间"),
                ),
                (
                    "standard_number",
                    models.CharField(
                        blank=True,
                        db_comment="标准的唯一编号，如GB/T 12345-2020",
                        max_length=100,
                        null=True,
                        verbose_name="标准号",
                    ),
                ),
                (
                    "standard_scope",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("INTERNATIONAL", "国际标准"),
                            ("NATIONAL", "国家标准"),
                            ("INDUSTRY", "行业标准"),
                            ("LOCAL", "地方标准"),
                            ("ASSOCIATION", "团体标准"),
                        ],
                        db_comment="标准适用范围描述",
                        max_length=50,
                        null=True,
                        verbose_name="标准范围",
                    ),
                ),
                (
                    "applicable_area",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(
                            db_comment="标准实施的地理或行业范围",
                            max_length=50,
                            verbose_name="适用区域",
                        ),
                        blank=True,
                        db_comment="标准实施的地理或行业范围（多个值）",
                        null=True,
                        size=None,
                    ),
                ),
                (
                    "implementation_status",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("CURRENT", "现行"),
                            ("TRIAL", "试行"),
                            ("DRAFTING", "编制中"),
                        ],
                        db_comment="标准当前状态",
                        max_length=50,
                        null=True,
                        verbose_name="实施状态",
                    ),
                ),
                (
                    "implementation_date",
                    models.DateField(
                        blank=True,
                        db_comment="标准正式实施的日期",
                        null=True,
                        verbose_name="实施日期",
                    ),
                ),
                (
                    "category",
                    models.ForeignKey(
                        db_comment="该内容涉及的主要行业领域",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="information_category",
                        to="lca.category",
                        verbose_name="相关行业",
                    ),
                ),
                (
                    "cover_image",
                    models.ForeignKey(
                        blank=True,
                        db_comment="内容展示用封面图片",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="information_cover_image",
                        to="lca.file",
                        verbose_name="宣传封面",
                    ),
                ),
                (
                    "file_upload",
                    models.ForeignKey(
                        blank=True,
                        db_comment="附加文档，如PDF、Word等",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="information_file_upload",
                        to="lca.file",
                        verbose_name="文件上传",
                    ),
                ),
            ],
            options={
                "verbose_name": "政策信息库",
                "verbose_name_plural": "政策信息库",
                "db_table_comment": "政策信息库，包含信息公告、碳资讯、研究报告、标准规范等内容",
            },
        ),
    ]
