from ninja import Router

from lca.admin.resource_center.controllers.news_and_announcement_controllers import (
    router as news_router,
)
from lca.admin.resource_center.controllers.standard_specification_controllers import (
    router as standard_router,
)
from lca.admin.resource_center.controllers.common_controllers import (
    router as common_controllers,
)

router = Router(tags=["后台-资料库"])
router.add_router("/news-and-announcement", news_router)
router.add_router("/standard-specification", standard_router)
router.add_router("", common_controllers)
