# Generated by Django 5.1.6 on 2025-03-17 08:02

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Category",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="类别名称")),
            ],
        ),
        migrations.CreateModel(
            name="GreenhouseGas",
            fields=[
                (
                    "id",
                    models.CharField(
                        max_length=100,
                        primary_key=True,
                        serialize=False,
                        verbose_name="气体名称",
                    ),
                ),
                ("name", models.CharField(max_length=20, verbose_name="名称")),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False)),
                (
                    "formula",
                    models.CharField(max_length=10, unique=True, verbose_name="分子式"),
                ),
                (
                    "gwp100",
                    models.DecimalField(
                        decimal_places=5,
                        max_digits=20,
                        verbose_name="100 年时间尺度下的全球变暖潜值(GWP)，CO₂=1 的相对值",
                    ),
                ),
                (
                    "gwp_source",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="GWP 数值的来源(如 'IPCC AR5')",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Unit",
            fields=[
                (
                    "id",
                    models.CharField(
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                        verbose_name="单位名称",
                    ),
                ),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("symbol", models.CharField(max_length=50, verbose_name="单位符号")),
                (
                    "conversion_factor",
                    models.DecimalField(
                        blank=True,
                        decimal_places=8,
                        max_digits=18,
                        null=True,
                        verbose_name="转换因子",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Geography",
            fields=[
                (
                    "id",
                    models.CharField(
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                        verbose_name="缩写",
                    ),
                ),
                ("name", models.CharField(max_length=50, verbose_name="地理区域名称")),
                (
                    "coord",
                    models.CharField(blank=True, max_length=50, null=True, verbose_name="经纬度坐标"),
                ),
                (
                    "classification",
                    models.CharField(
                        blank=True,
                        max_length=50,
                        null=True,
                        verbose_name="地理区域分类(如 'Country', 'Province', 'City')",
                    ),
                ),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="children",
                        to="lca.geography",
                        verbose_name="上级地理区域",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="EmissionSource",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200)),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False)),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("energy", "能源"),
                            ("packaging", "包装"),
                            ("natural-resource", "自然资源"),
                            ("transport", "运输"),
                            ("infrastructure", "基础设备设施"),
                            ("recycle-material", "再生原料"),
                            ("material-content", "材料/成分含量"),
                        ],
                        max_length=50,
                        verbose_name="排放源类型",
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=5,
                        max_digits=20,
                        null=True,
                        verbose_name="活动量",
                    ),
                ),
                ("source", models.CharField(max_length=200, verbose_name="数据来源")),
                ("year", models.PositiveSmallIntegerField(blank=True, null=True)),
                ("description", models.TextField(blank=True, null=True)),
                (
                    "create_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "geography",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="lca.geography",
                        verbose_name="适用的地理区域",
                    ),
                ),
                (
                    "unit",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        to="lca.unit",
                        verbose_name="功能单位",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Model",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("name", models.CharField(max_length=100, verbose_name="产品名称")),
                (
                    "functional_unit",
                    models.CharField(max_length=100, verbose_name="功能单位"),
                ),
                ("source", models.CharField(max_length=100, verbose_name="数据来源")),
                (
                    "company_name",
                    models.CharField(blank=True, max_length=100, null=True, verbose_name="公司名称"),
                ),
                (
                    "description",
                    models.CharField(blank=True, max_length=200, null=True, verbose_name="描述"),
                ),
                (
                    "boundary",
                    models.CharField(
                        choices=[
                            ("cradle-gate", "摇篮到大门（从资源开采到产品出厂）"),
                            ("cradle-grave", "摇篮到坟墓（从资源开采到产品废弃）"),
                        ],
                        max_length=50,
                        verbose_name="系统边界",
                    ),
                ),
                ("year", models.IntegerField(verbose_name="年份")),
                (
                    "rule",
                    models.CharField(blank=True, null=True, verbose_name="取舍规则"),
                ),
                (
                    "amount",
                    models.DecimalField(decimal_places=5, max_digits=20, verbose_name="产量"),
                ),
                (
                    "co2e",
                    models.DecimalField(
                        decimal_places=5,
                        default=0,
                        max_digits=20,
                        verbose_name="二氧化碳当量",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "category",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.category",
                        verbose_name="类别",
                    ),
                ),
                (
                    "geography",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.geography",
                        verbose_name="产品产地",
                    ),
                ),
                (
                    "unit",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.unit",
                        verbose_name="单位",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Input",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="原材料名称")),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("energy", "能源"),
                            ("packaging", "包装"),
                            ("natural-resource", "自然资源"),
                            ("transport", "运输"),
                            ("infrastructure", "基础设备设施"),
                            ("recycle-material", "再生原料"),
                            ("material-content", "材料/成分含量"),
                        ],
                        max_length=50,
                        verbose_name="原材料类型",
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(decimal_places=5, max_digits=20, verbose_name="用量"),
                ),
                (
                    "co2e",
                    models.DecimalField(decimal_places=5, max_digits=20, verbose_name="CO2e"),
                ),
                (
                    "create_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "emmission_source",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.emissionsource",
                        verbose_name="排放源",
                    ),
                ),
                (
                    "model",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="inputs",
                        to="lca.model",
                        verbose_name="模型",
                    ),
                ),
                (
                    "unit",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.unit",
                        verbose_name="单位",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Process",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200, verbose_name="过程名称")),
                ("product", models.CharField(max_length=200, verbose_name="产品")),
                (
                    "amount",
                    models.DecimalField(decimal_places=5, max_digits=20, verbose_name="数量"),
                ),
                (
                    "co2e",
                    models.DecimalField(decimal_places=5, max_digits=20, verbose_name="CO2e"),
                ),
                (
                    "model",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="processes",
                        to="lca.model",
                        verbose_name="模型",
                    ),
                ),
                (
                    "unit",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.unit",
                        verbose_name="单位",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Transport",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "gross_weight",
                    models.DecimalField(decimal_places=5, max_digits=20, verbose_name="毛重"),
                ),
                (
                    "origin",
                    models.CharField(max_length=100, null=True, verbose_name="起始地"),
                ),
                (
                    "terminal",
                    models.CharField(max_length=100, null=True, verbose_name="目的地"),
                ),
                (
                    "distance",
                    models.DecimalField(decimal_places=5, max_digits=20, verbose_name="运输距离"),
                ),
                (
                    "create_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "emmission_source",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.emissionsource",
                        verbose_name="排放源",
                    ),
                ),
                (
                    "input",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="transports",
                        to="lca.input",
                        verbose_name="原材料",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Output",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200, verbose_name="输出名称")),
                (
                    "amount",
                    models.DecimalField(decimal_places=5, max_digits=20, verbose_name="数量"),
                ),
                (
                    "type",
                    models.CharField(
                        choices=[("environment", "环境")],
                        default="environment",
                        max_length=50,
                        verbose_name="输出类型",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "emission_source",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="outputs",
                        to="lca.emissionsource",
                    ),
                ),
                (
                    "green_house_gas",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.greenhousegas",
                        verbose_name="温室气体",
                    ),
                ),
                (
                    "input",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="outputs",
                        to="lca.input",
                    ),
                ),
                (
                    "process",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="outputs",
                        to="lca.process",
                    ),
                ),
                (
                    "unit",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.unit",
                        verbose_name="单位",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="EmissionFactor",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=5,
                        max_digits=20,
                        verbose_name="排放因子数值(每单位活动流会排放多少单位此气体)",
                    ),
                ),
                (
                    "reference",
                    models.TextField(
                        blank=True,
                        null=True,
                        verbose_name="排放因子的来源或参考文献(如 IPCC 2006 指南)",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "source",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.emissionsource",
                        verbose_name="关联到具体排放源(如柴油燃烧)",
                    ),
                ),
                (
                    "green_house_gas",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="lca.greenhousegas",
                        verbose_name="温室气体",
                    ),
                ),
                (
                    "unit",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="lca.unit",
                    ),
                ),
            ],
            options={
                "unique_together": {("source", "green_house_gas")},
            },
        ),
    ]
