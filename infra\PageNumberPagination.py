from ninja.pagination import PaginationBase
from ninja import Schema
from pydantic import Field


class PageNumberPagination(PaginationBase):
    """通用分页器，适配page和page_size"""

    class Input(Schema):
        page_size: int = Field(20, gt=0, le=100)
        page: int = Field(1, gt=0)

    def paginate_queryset(self, queryset, pagination: Input, **params):
        return {
            "items": queryset[(pagination.page - 1) * pagination.page_size : (pagination.page) * pagination.page_size],
            "count": queryset.count(),
        }
