from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.permissions import AllowAny

from ztbory_django_shield.core.exceptions import SlideCaptchaSlideVerifyFailError
from ztbory_django_shield.base.shield import slide_captcha_shield, email_shield
from ztbory_django_shield.restframework.serializers import SlideCaptchaVerifyForm


@api_view(["GET"])
@permission_classes([AllowAny])
def slide_captcha(request):
    """生成滑动校验码"""
    captcha = slide_captcha_shield.generate_captcha()
    return Response(
        dict(
            uuid=captcha.uuid,
            foreground=captcha.foreground,
            background=captcha.background,
        )
    )


@api_view(["POST"])
@permission_classes([AllowAny])
def slide_captcha_verify(request: Request):
    """滑动验证码滑动校验"""
    verify = SlideCaptchaVerifyForm(data=request.data)
    if not verify.is_valid():
        raise SlideCaptchaSlideVerifyFailError()
    uuid = verify.data.get("uuid")
    secret = slide_captcha_shield.validate_captcha_for_secret(uuid, verify.data.get("captcha"))
    return Response({"uuid": uuid, "secret": secret})


@api_view(["GET"])
@permission_classes([AllowAny])
def email_reset_password_expiration(request):
    """验证邮件找回密码是否过期"""
    token = request.query_params.get("token")
    email_shield.verify_token_expiration(token)
    return Response(status=status.HTTP_204_NO_CONTENT)
