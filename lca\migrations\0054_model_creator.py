# Generated by Django 5.2.1 on 2025-07-03 06:00

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


def add_super(apps, schema_editor):
    User = apps.get_model("lca", "User")
    User.objects.create(
        **{
            "last_login": "2025-07-03T08:23:09.712Z",
            "is_superuser": True,
            "is_staff": True,
            "is_active": True,
            "date_joined": "2025-07-03T05:58:41.830Z",
            "phone": "18601357885",
            "nickname": "超级管理员",
            "type": "manufacturer",
            "certificate_status": "approved",
            "authority_certification_id": None,
            "manufacturer_certification_id": None,
        }
    )


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0053_alter_user_certificate_status_alter_user_type"),
    ]

    operations = [
        migrations.RunPython(add_super),
        migrations.AddField(
            model_name="model",
            name="creator",
            field=models.ForeignKey(
                default=1,
                on_delete=django.db.models.deletion.CASCADE,
                to=settings.AUTH_USER_MODEL,
                verbose_name="创建者",
            ),
            preserve_default=False,
        ),
    ]
