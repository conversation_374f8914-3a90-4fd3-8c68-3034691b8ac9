# Generated by Django 5.2.1 on 2025-07-09 02:50

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0077_rename_reviewdecision_certificationreviewdecision"),
    ]

    operations = [
        migrations.AddField(
            model_name="supervision",
            name="authority",
            field=models.ForeignKey(
                default=None,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="supervisions",
                to="lca.authority",
                verbose_name="认证机构",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="supervision",
            name="manufacturer",
            field=models.ForeignKey(
                default=None,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="supervisions",
                to="lca.manufacturer",
                verbose_name="生产企业",
            ),
            preserve_default=False,
        ),
    ]
