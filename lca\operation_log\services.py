from datetime import date, timedelta

from django.db.models import QuerySet

from lca.operation_log.models import OperationLog


class OperationLogService:
    @staticmethod
    def list_operation_log(
        operator_nickname: str | None = None,
        start_time: date | None = None,
        end_time: date | None = None,
    ) -> QuerySet:
        """
        获取操作日志列表
        """
        query = OperationLog.objects.all()
        filter_ = {}

        if operator_nickname:
            filter_["operator_nickname__icontains"] = operator_nickname
        if start_time:
            filter_["operation_time__gte"] = start_time
        if end_time:
            end_time = end_time + timedelta(days=1)
            filter_["operation_time__lt"] = end_time
        if filter_:
            query = query.filter(**filter_)
        return query
