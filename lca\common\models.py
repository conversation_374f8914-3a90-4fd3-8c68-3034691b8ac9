from django.db import models


class REGION_LEVEL(models.TextChoices):
    """
    地理区域表层级
    """

    PROVINCE = "province", "省"
    CITY = "city", "市"
    COUNTY = "county", "县"


class Region(models.Model):
    """
    地理区域表
    """

    name = models.CharField(max_length=50, verbose_name="地理区域名称")
    parent = models.ForeignKey(
        "self",
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        verbose_name="上级地理区域",
        related_name="children",
    )
    level = models.CharField(
        max_length=50,
        null=False,
        blank=True,
        choices=REGION_LEVEL.choices,
        verbose_name="区域级别",
    )

    def __str__(self):
        return f"{self.name}"
