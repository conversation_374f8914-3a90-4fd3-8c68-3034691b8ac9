class ShieldError(Exception):
    ErrCode = "ShieldError"
    ErrMessage = "Shield Error"
    ErrHttpStatusCode = 400

    def __init__(self, detail=None, http_status_code=None):
        self.err_code = self.ErrCode
        self.err_message = detail or self.ErrMessage

        self.http_status_code = http_status_code or self.ErrHttpStatusCode
        self.detail = self.err_message


class PasswordUnSafeError(ShieldError):
    """密码不符合要求"""

    ErrCode = "PasswordUnSafeError"
    ErrMessage = "请按密码填写要求提供密码."


class PasswordBanedError(ShieldError):
    """账户已禁用"""

    ErrCode = "PasswordBanedError"
    ErrMessage = "账号已被锁定，请稍后再试."


class PasswordTwoFactorRequiredError(ShieldError):
    """需要双因子验证"""

    ErrCode = "PasswordTwoFactorRequiredError"
    ErrMessage = "请进行辅助验证后重新登录."


class PasswordLoginFailError(ShieldError):
    """登录失败"""

    ErrCode = "PasswordLoginFailError"
    ErrMessage = "登录失败."


class SlideCaptchaNotFoundError(ShieldError):
    """校验码无效"""

    ErrCode = "SlideCaptchaNotFoundError"
    ErrMessage = "无效的验证码."


class SlideCaptchaSlideVerifyFailError(ShieldError):
    """校验码滑动验证无效"""

    ErrCode = "SlideCaptchaSlideVerifyFailError"
    ErrMessage = "验证失败."


class EmailSendFailError(ShieldError):
    """邮件发送失败"""

    ErrCode = "EmailSendFailError"
    ErrMessage = "邮件发送失败，请稍后重试."


class EmailTokenNotFoundError(ShieldError):
    """链接已过期"""

    ErrCode = "EmailTokenNotFoundError"
    ErrMessage = "链接已失效，请重新申请找回密码."


class CryptorNotAvailableError(ShieldError):
    """Sm2未配置"""

    ErrCode = "CryptorNotAvailableError"
    ErrMessage = "加密公钥/私钥未配置."


class CryptorEncryptError(ShieldError):
    """Sm2加密失败"""

    ErrCode = "CryptorEncryptError"
    ErrMessage = "加密失败."


class CryptorDecryptError(ShieldError):
    """Sm2解密失败"""

    ErrCode = "CryptorDecryptError"
    ErrMessage = "解密失败."
