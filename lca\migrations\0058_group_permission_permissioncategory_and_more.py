# Generated by Django 5.2.1 on 2025-07-03 13:51

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0057_alter_usercertification_user"),
    ]

    operations = [
        migrations.CreateModel(
            name="Group",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="名称")),
                (
                    "is_active",
                    models.BooleanField(db_index=True, default=True, verbose_name="是否有效"),
                ),
                (
                    "create_time",
                    models.DateTimeField(auto_now_add=True, db_index=True, verbose_name="创建时间"),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, db_index=True, verbose_name="更新时间"),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Permission",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="名称")),
                (
                    "codename",
                    models.CharField(max_length=100, unique=True, verbose_name="代码"),
                ),
                (
                    "create_time",
                    models.DateTimeField(auto_now_add=True, db_index=True, verbose_name="创建时间"),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, db_index=True, verbose_name="更新时间"),
                ),
            ],
        ),
        migrations.CreateModel(
            name="PermissionCategory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="名称")),
            ],
        ),
        migrations.AlterField(
            model_name="user",
            name="groups",
            field=models.ManyToManyField(related_name="groups", to="lca.group", verbose_name="角色"),
        ),
        migrations.AddField(
            model_name="group",
            name="permissions",
            field=models.ManyToManyField(related_name="groups", to="lca.permission", verbose_name="权限"),
        ),
        migrations.AddField(
            model_name="permission",
            name="category",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="permissions",
                to="lca.permissioncategory",
                verbose_name="权限分类",
            ),
        ),
    ]
