# Generated by Django 5.2.1 on 2025-07-07 07:33

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0067_certification_acceptance_review_status_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="authority",
            name="user",
            field=models.OneToOneField(
                default=1,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="authority",
                to=settings.AUTH_USER_MODEL,
                verbose_name="用户",
            ),
            preserve_default=False,
        ),
    ]
