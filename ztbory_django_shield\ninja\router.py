from ninja import Router, Body

from ztbory_django_shield.base.shield import slide_captcha_shield
from ztbory_django_shield.ninja.schema import (
    SlideCaptchaOut,
    SlideCaptchaVerifyPayload,
    SlideCaptchaVerifyOut,
)

shield_router = Router(tags=["智盾"])


@shield_router.get(
    "/slide-captcha",
    auth=None,
    response=SlideCaptchaOut,
    summary="生成滑动校验码",
    operation_id="shieldGenSlideCaptcha",
)
def slide_captcha(request):
    return slide_captcha_shield.generate_captcha()


@shield_router.post(
    "/slide-captcha/verify",
    auth=None,
    response=SlideCaptchaVerifyOut,
    summary="滑动验证码滑动校验",
    operation_id="shieldVerifySlideCaptcha",
)
def slide_captcha_verify(
    request,
    payload: SlideCaptchaVerifyPayload = Body(..., description="滑动验证码滑动校验表单"),
):
    secret = slide_captcha_shield.validate_captcha_for_secret(payload.uuid, payload.captcha)
    return SlideCaptchaVerifyOut(uuid=payload.uuid, secret=secret)
