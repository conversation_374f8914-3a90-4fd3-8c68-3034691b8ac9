from typing import Optional, Union
from ninja import Schema
from pydantic import Field, model_validator
from datetime import date, datetime
from ninja.errors import HttpError
from lca.accounting.models import BOUNDARIES
from lca.accounting.schema import (
    CategorySchema,
    GeographySchema,
    ModelSchema,
    UnitSchema,
)
from lca.certification.models import (
    CERTIFICATION_APPLICATION_STATUS,
    CERTIFICATION_LIFE_CYCLES,
    CERTIFICATION_STATUS,
    CERTIFICATION_STEP_STATUS,
    CERTIFICATION_STEPS,
    SUPERVISION_STATUS,
)
from lca.common.schema import RegionSchema
from lca.file.schema import FileOut


class AuthoritySchema(Schema):
    """认证机构"""

    id: int = Field(..., title="id")
    name: str = Field(..., title="机构名称")
    description: str = Field(..., title="机构描述")
    address: str = Field(..., title="机构地址")
    logo: FileOut | None = Field(..., title="机构图标")


class AuthorityRowSchema(Schema):
    """认证机构"""

    id: int = Field(..., title="id")
    name: str = Field(..., title="机构名称")
    description: str = Field(..., title="机构描述")
    address: str = Field(..., title="机构地址")


class CertificationModelSchema(Schema):
    id: int = Field(..., title="id")
    name: str = Field(..., title="产品名称")
    functional_unit: str = Field(..., title="功能单位")
    specs: str = Field(..., title="型号")
    boundary: str = Field(..., title="系统边界")
    unit: UnitSchema = Field(..., title="单位")
    image: Optional[FileOut] = Field(None, title="产品图片")
    category: CategorySchema = Field(..., title="产品类别")


class InspectorSchema(Schema):
    name: str = Field(..., title="姓名")
    position: str = Field(..., title="职务")
    qualification: str = Field(..., title="资质")


class ReviewerSchema(Schema):
    name: str = Field(..., title="姓名")
    position: str = Field(..., title="职务")
    qualification: Optional[str] = Field(None, title="资质")
    specialty: str = Field(..., title="专业领域")


class CertificationPlanInSchema(Schema):
    """产品碳足迹标识认证申请表单"""

    certifying_unit: str = Field(..., title="认证单元", max_length=50)
    purpose: str = Field(..., title="产品碳足迹标识认证的目的", max_length=50)
    product_type: str = Field(..., title="产品种类", max_length=50)
    cert_standard: str = Field(..., title="认证依据标准", max_length=50)
    data_time_range: str = Field(..., title="数据时间边界", max_length=50)
    field_inspection_requirement: Optional[str] = Field(None, title="现场检查要求", max_length=500)
    inspectors: list[InspectorSchema] = Field(..., title="检查组成员")
    reviewers: list[ReviewerSchema] = Field(..., title="技术评审组成员")
    accompanying_inspectors: Optional[str] = Field(None, title="检查组同行人员", max_length=50)
    progress_schedule: Optional[str] = Field(None, title="进度安排", max_length=500)


class CertificationPlanSchema(CertificationPlanInSchema):
    file: Optional[FileOut] = Field(None, title="认证方案")


class DocumentReviewBaseSchema(Schema):
    model_accepted: bool = Field(..., title="文件评审-生命周期模型")
    material_accepted: bool = Field(..., title="文件评审-认证材料")


class DocucmentReviewInSchema(DocumentReviewBaseSchema):
    """文件评审"""

    model_file_id: Optional[str] = Field(None, title="生命周期模型审批文件")
    material_file_id: Optional[str] = Field(None, title="认证材料审批文件")


class DocumentReviewSchema(DocumentReviewBaseSchema):
    model_file: Optional[FileOut] = Field(None, title="生命周期模型审批文件")
    material_file: Optional[FileOut] = Field(None, title="认证材料审批文件")


class NonconformityItem(Schema):
    """不符合项列表"""

    name: str = Field(..., title="名称", max_length=100)
    item: str = Field(..., title="检查项", max_length=100)
    reason: str = Field(..., title="不符合原因", max_length=255)
    correction: str = Field(..., title="整改建议", max_length=255)


class OnSiteInspectionReportBaseSchema(Schema):
    principal: str = Field(..., title="认证委托人", max_length=50)
    principal_address: str = Field(..., title="认证委托人地址", max_length=50)
    manufacturer: str = Field(..., title="生产企业名称", max_length=50)
    manufacturer_address: str = Field(..., title="生产企业地址", max_length=50)
    producer: str = Field(..., title="生产者（制造商）名称", max_length=50)
    producer_address: str = Field(..., title="生产者（制造商）地址", max_length=50)

    purpose: Optional[str] = Field(None, title="现场检查的目的", max_length=500)
    basis: Optional[str] = Field(None, title="现场检查的依据", max_length=500)
    scope: Optional[str] = Field(None, title="现场检查的范围", max_length=500)
    capability_accepted: bool = Field(..., title="企业保证能力检查")

    name_file_accepted: bool = Field(..., title="认证产品的名称、型号、生产企业及相关标识与申请文件或证书是否一致")
    design_file_accepted: bool = Field(
        ...,
        title="认证产品的设计、关键件、能源和资源、生产工艺、交付及储存、使用、回收与处置等适用环节碳足迹数据和信息与申请文件是否一致",
    )
    design_product_accepted: bool = Field(
        ...,
        title="认证产品的设计、关键件、能源和资源、生产工艺、交付及储存、使用、回收与处置等环节与所确认产品是否一致",
    )

    process_description: Optional[str] = Field(None, title="现场检查过程的描述", max_length=500)
    nonconformities: Optional[list[NonconformityItem]] = Field(..., title="不符合项详情及整改建")

    result: Optional[str] = Field(None, title="现场检查结果", max_length=500)


class OnSiteInspectionReportInSchema(OnSiteInspectionReportBaseSchema):
    """现场检查记录表单"""

    # 如果有 bool 选择了否，需要填写不符合项详情及整改建议
    @model_validator(mode="after")
    def validate_nonconformities(self):
        if (
            self.capability_accepted is False
            or self.name_file_accepted is False
            or self.design_file_accepted is False
            or self.design_product_accepted is False
        ):
            if not self.nonconformities:
                raise ValueError("当检查不通过时，必须有整改建议")
        return self


class OnSiteInspectionReportSchema(OnSiteInspectionReportInSchema):
    file: Optional[FileOut] = Field(None, title="现场检查报告")


class InitialInspectionItem(Schema):
    """初核数据核查"""

    data_name: str = Field(..., title="数据名称", max_length=100)
    unit: str = Field(..., title="单位", max_length=100)
    source: str = Field(..., title="来源", max_length=100)
    monitoring_method: str = Field(..., title="监测方法与频次", max_length=100)
    recording_frequency: str = Field(..., title="记录频次", max_length=100)
    monitoring_equipment_verification: str = Field(..., title="监测设备校验", max_length=100)
    data_missing_handling: str = Field(..., title="数据缺失处理", max_length=100)
    cross_check: str = Field(..., title="交叉校验", max_length=100)
    conclusion: str = Field(..., title="结论", max_length=500)


class InitialInspection(Schema):
    """初核数据核查"""

    life_cycle: CERTIFICATION_LIFE_CYCLES = Field(..., title="生命周期阶段")
    items: list[InitialInspectionItem] = Field(..., title="数据核查项")


class FollowUpInspectionItem(Schema):
    """次核数据核查成员"""

    name: str = Field(..., title="名称", max_length=100)
    item: str = Field(..., title="数据项目", max_length=100)
    time: str = Field(..., title="时间", max_length=100)
    region: str = Field(..., title="地区", max_length=100)
    data_type: str = Field(..., title="数据类型", max_length=100)
    source: str = Field(..., title="来源", max_length=100)
    conclusion: str = Field(..., title="结论", max_length=500)


class FollowUpInspection(Schema):
    life_cycle: CERTIFICATION_LIFE_CYCLES = Field(..., title="生命周期阶段")
    items: list[FollowUpInspectionItem] = Field(..., title="数据核查项")


class ProductQuantificationItem(Schema):
    """产品碳足迹量化项"""

    name: str = Field(..., title="名称", max_length=100)
    co2e: float = Field(..., title="碳足迹")
    percent: float = Field(..., title="百分比")


class ProductQuantification(Schema):
    """产品碳足迹量化项"""

    life_cycle: CERTIFICATION_LIFE_CYCLES = Field(..., title="生命周期阶段")
    co2e: float = Field(None, title="碳足迹", description="自动计算，用户无需传入")
    percent: float = Field(None, title="百分比", description="自动计算，用户无需传入")
    items: list[ProductQuantificationItem] = Field(..., title="量化项")

    @model_validator(mode="after")
    def calculate_totals(self):
        """自动计算 co2e 和 percent 的总和"""
        if self.items:
            # 计算 co2e 总和，保留5位小数
            total_co2e = sum(float(item.co2e) if isinstance(item.co2e, (str, int, float)) else 0 for item in self.items)
            self.co2e = round(total_co2e, 5)

            # 计算 percent 总和，保留2位小数
            total_percent = sum(
                float(item.percent) if isinstance(item.percent, (str, int, float)) else 0 for item in self.items
            )
            self.percent = round(total_percent, 2)
        else:
            self.co2e = 0.0
            self.percent = 0.0

        return self


class CarbonFootprintVerificationBaseSchema(Schema):
    product_name: str = Field(..., title="产品名称", max_length=100)
    specs: str = Field(..., title="产品型号", max_length=100)
    functional_unit: str = Field(..., title="功能单位", max_length=100)
    boundary: BOUNDARIES = Field(..., title="系统边界", max_length=100)

    client_name: str = Field(..., title="受核查方名称", max_length=100)
    address: str = Field(..., title="受核查方地址", max_length=100)
    credit_code: str = Field(..., title="统一社会信用代码", max_length=100)
    legal_representative: str = Field(..., title="法定代表人", max_length=100)
    verification_date: date = Field(..., title="核查日期")

    initial_inspections: list[InitialInspection] = Field(..., title="初级数据核查")
    followup_inspections: list[FollowUpInspection] = Field(..., title="次级数据核查")

    # 数据获取原则、数据质量
    data_acquisition_principle: Optional[str] = Field(None, title="数据取舍原则", max_length=500)
    data_quality_requirement: Optional[str] = Field(None, title="数据质量要求", max_length=500)
    data_quality_evaluation: Optional[str] = Field(None, title="数据质量评价", max_length=500)

    # 产品碳足迹量化
    quantification_method: Optional[str] = Field(None, title="量化方法", max_length=500)
    product_quantifications: list[ProductQuantification] = Field(..., title="产品碳足迹量化")

    # 核查结论、建议、不确定性分析
    verification_conclusion: Optional[str] = Field(None, title="核查结论", max_length=500)
    verification_recommendation: Optional[str] = Field(None, title="核查建议", max_length=500)
    uncertainty_analysis: Optional[str] = Field(None, title="不确定性分析", max_length=500)
    result: bool = Field(..., title="核查结果")


class CarbonFootprintVerificationInSchema(CarbonFootprintVerificationBaseSchema):
    """碳足迹核查表单"""

    result_file_id: Optional[str] = Field(None, title="核查结果审批文件")

    @model_validator(mode="after")
    def validate_result_file_id(self):
        """如果result是false，result_file_id必填"""
        if not self.result and not self.result_file_id:
            raise ValueError("当核查结果为否定时，必须提供核查结果审批文件")
        return self


class CarbonFootprintVerificationSchema(CarbonFootprintVerificationBaseSchema):
    result_file: Optional[FileOut] = Field(None, title="核查结果审批文件")


# VERIFICATION_REPORT_ISSUANCE
class VerificationReportIssuanceInSchema(Schema):
    """出具核查报告表单"""

    report_file_id: str = Field(..., title="核查报告文件")


class VerificationReportIssuanceSchema(Schema):
    report_file: Optional[FileOut] = Field(None, title="核查报告文件")


class ReviewDecisionInSchema(Schema):
    """复合与认证决定表单"""

    result: bool = Field(..., title="认证决定")
    result_file_id: Optional[str] = Field(None, title="审批文件")


class ReviewDecisionSchema(Schema):
    result: bool = Field(..., title="认证决定")
    result_file: Optional[FileOut] = Field(None, title="审批文件")


# CERTIFICATE_ISSUANCE
class CertificateIssuanceInSchema(Schema):
    """发放证书及标识表单"""

    certificate_file_id: str = Field(..., title="证书文件")


class CertificateIssuanceSchema(Schema):
    certificate_file: Optional[FileOut] = Field(None, title="证书文件")


class CertificationStepInfoSchema(Schema):
    model: "CertificationModelSchema" = Field(..., title="生命周期模型")
    application_status: CERTIFICATION_APPLICATION_STATUS = Field(..., title="受理状态")
    certification_status: Union[CERTIFICATION_STATUS, None] = Field(None, title="认证状态")
    supervision_status: Union[SUPERVISION_STATUS, None] = Field(None, title="监督管理状态")
    unit: Optional[UnitSchema] = Field(None, title="单位")
    application_finish_time: datetime | None = Field(..., title="受理完成时间")
    certification_finish_time: datetime | None = Field(..., title="认证完成时间")
    create_time: datetime = Field(..., title="创建时间")

    submission_status: Optional[CERTIFICATION_STEP_STATUS] = Field(None, title="发起认证申请状态")
    acceptance_review_status: Optional[CERTIFICATION_STEP_STATUS] = Field(None, title="认证受理评审状态")
    plan_status: Optional[CERTIFICATION_STEP_STATUS] = Field(None, title="制定认证方案状态")
    document_review_status: Optional[CERTIFICATION_STEP_STATUS] = Field(None, title="开展文件评审状态")
    onsite_inspection_status: Optional[CERTIFICATION_STEP_STATUS] = Field(None, title="开展现场检查状态")
    carbon_footprint_verification_status: Optional[CERTIFICATION_STEP_STATUS] = Field(None, title="碳足迹核查状态")
    verification_report_issuance_status: Optional[CERTIFICATION_STEP_STATUS] = Field(None, title="出具核查报告状态")
    review_decision_status: Optional[CERTIFICATION_STEP_STATUS] = Field(None, title="复合与认证决定状态")
    certificate_issuance_status: Optional[CERTIFICATION_STEP_STATUS] = Field(None, title="发放证书及标识状态")

    plan: Optional[CertificationPlanSchema] = Field(None, title="认证方案详情")
    document_review: Optional[DocumentReviewSchema] = Field(None, title="文件评审详情")
    onsite_inspection: Optional[OnSiteInspectionReportSchema] = Field(None, title="现场检查详情")
    carbon_footprint_verification: Optional[CarbonFootprintVerificationSchema] = Field(None, title="碳足迹核查详情")
    verification_report_issuance: Optional[VerificationReportIssuanceSchema] = Field(None, title="出具核查报告详情")
    review_decision: Optional[ReviewDecisionSchema] = Field(None, title="复合与认证决定详情")
    certificate_issuance: Optional[CertificateIssuanceSchema] = Field(None, title="发放证书及标识详情")
    supervision: Optional["SupervisionSchema"] = Field(None, title="监督管理详情", alias="last_supervision")


class CertificationBaseSchema(Schema):
    category: CategorySchema = Field(..., title="产品类别")
    principal: str = Field(..., title="委托人", max_length=100)
    principal_address: str = Field(..., title="委托人地址", max_length=100)
    manufacturer_name: str = Field(..., title="生产者（制造商）名称", max_length=100)
    manufacturer_address: str = Field(..., title="生产者（制造商）名称地址", max_length=100)
    producer_name: str = Field(..., title="生产企业名称", max_length=100)
    producer_address: str = Field(..., title="生产企业地址", max_length=100)
    contact_name: str = Field(..., title="联系人", max_length=100)
    contact_phone: str = Field(..., title="联系人电话", max_length=100)
    summary: str = Field(..., title="企业概况", max_length=500)
    product_name: str = Field(..., title="产品名称", max_length=100)
    product_description: str = Field(..., title="产品介绍", max_length=500)
    product_standard: Optional[str] = Field(None, title="产品执行标准", max_length=255)
    product_performance: Optional[str] = Field(None, title="产品性能指标", max_length=255)
    product_function: Optional[str] = Field(None, title="产品功能", max_length=500)
    production_process: Optional[str] = Field(None, title="生产工艺", max_length=500)
    reason: Optional[str] = Field(None, title="认证申请被拒绝的原因", max_length=500)
    day_left: Optional[int] = Field(None, title="距离认证结束的天数")

    authority: AuthorityRowSchema = Field(..., title="认证机构")
    application_status: CERTIFICATION_APPLICATION_STATUS = Field(..., title="受理状态")
    certification_status: Union[CERTIFICATION_STATUS, None] = Field(None, title="认证状态")
    supervision_status: Union[SUPERVISION_STATUS, None] = Field(None, title="监督管理状态")

    application_finish_time: datetime | None = Field(..., title="受理完成时间")
    certification_finish_time: datetime | None = Field(..., title="认证完成时间")
    certification_start_time: datetime | None = Field(..., title="认证开始时间")
    create_time: datetime = Field(..., title="创建时间")
    can_supervision: bool = Field(..., title="是否可以去监督")


class CertificationRowSchema(CertificationBaseSchema):
    """
    认证
    """

    id: int = Field(..., title="id")


class CertificationApplyRejectInSchema(Schema):
    reason: str = Field(..., title="拒绝原因", max_length=500)


class CertificationSchema(CertificationBaseSchema):
    """
    认证
    """

    authority: AuthoritySchema = Field(..., title="认证机构")
    model: ModelSchema = Field(..., title="生命周期模型")
    image: Optional[FileOut] = Field(..., title="产品图片")
    business_license: FileOut = Field(..., title="营业执照")
    agency_relationship: FileOut | None = Field(None, title="委托关系")
    letter_of_authorization: FileOut = Field(..., title="委托书")
    intellectual_property: FileOut | None = Field(None, title="知识产权关系")
    process_flows: list[FileOut] = Field(..., title="工艺流程图")
    organizational_chart: FileOut = Field(..., title="生产企业组织机构图")
    equipments: list[FileOut] = Field(..., title="主要生产、计量设备清单")
    bom: FileOut = Field(..., title="原辅材料清单")
    labels: list[FileOut] = Field(..., title="标识认证数据和信息采集清单")
    plans: list[FileOut] = Field(..., title="产品降碳方案或计划")
    capabilities: list[FileOut] = Field(..., title="保证能力相关管理文件或目录")
    others: list[FileOut] = Field(..., title="其它")
    application_finish_time: datetime | None = Field(..., title="受理完成时间")
    certification_finish_time: datetime | None = Field(..., title="认证完成时间")
    create_time: datetime = Field(..., title="创建时间")

    submission_status: Optional[CERTIFICATION_STEP_STATUS] = Field(None, title="发起认证申请状态")
    acceptance_review_status: Optional[CERTIFICATION_STEP_STATUS] = Field(None, title="认证受理评审状态")
    plan_status: Optional[CERTIFICATION_STEP_STATUS] = Field(None, title="制定认证方案状态")
    document_review_status: Optional[CERTIFICATION_STEP_STATUS] = Field(None, title="开展文件评审状态")
    onsite_inspection_status: Optional[CERTIFICATION_STEP_STATUS] = Field(None, title="开展现场检查状态")
    carbon_footprint_verification_status: Optional[CERTIFICATION_STEP_STATUS] = Field(None, title="碳足迹核查状态")
    verification_report_issuance_status: Optional[CERTIFICATION_STEP_STATUS] = Field(None, title="出具核查报告状态")
    review_decision_status: Optional[CERTIFICATION_STEP_STATUS] = Field(None, title="复合与认证决定状态")
    certificate_issuance_status: Optional[CERTIFICATION_STEP_STATUS] = Field(None, title="发放证书及标识状态")


class CertificationNextInSchema(Schema):
    current_step: CERTIFICATION_STEPS = Field(..., title="当前步骤")


class DocumentReviewFileSchema(Schema):
    model_file: Optional[FileOut] = Field(None, title="生命周期模型审批文件")
    material_file: Optional[FileOut] = Field(None, title="认证材料审批文件")


class OnSiteInspectionReportFileSchema(Schema):
    file: Optional[FileOut] = Field(None, title="现场检查报告")


class CarbonFootprintVerificationFileSchema(Schema):
    report_file: Optional[FileOut] = Field(None, title="碳足迹核查报告")


class VerificationReportIssuanceFileSchema(Schema):
    report_file: Optional[FileOut] = Field(None, title="核查报告文件")


class CertificateIssuanceFileSchema(Schema):
    certificate_file: Optional[FileOut] = Field(None, title="证书文件")


class SupervisionFileSchema(Schema):
    result_file: Optional[FileOut] = Field(None, title="监督决定审批文件")


class CertificationManufactureRowSchema(Schema):
    """企业能看到的认证"""

    id: int = Field(..., title="id")
    authority: AuthorityRowSchema = Field(..., title="认证机构")
    principal: str = Field(..., title="委托人", max_length=100)
    principal_address: str = Field(..., title="委托人地址", max_length=100)
    manufacturer_name: str = Field(..., title="生产者（制造商）名称", max_length=100)
    manufacturer_address: str = Field(..., title="生产者（制造商）名称地址", max_length=100)
    producer_name: str = Field(..., title="生产企业名称", max_length=100)
    producer_address: str = Field(..., title="生产企业地址", max_length=100)
    contact_name: str = Field(..., title="联系人", max_length=100)
    contact_phone: str = Field(..., title="联系人电话", max_length=100)
    summary: str = Field(..., title="企业概况", max_length=500)
    product_name: str = Field(..., title="产品名称", max_length=100)
    product_description: str = Field(..., title="产品介绍", max_length=500)
    product_standard: Optional[str] = Field(None, title="产品执行标准", max_length=255)
    product_performance: Optional[str] = Field(None, title="产品性能指标", max_length=255)
    product_function: Optional[str] = Field(None, title="产品功能", max_length=500)
    production_process: Optional[str] = Field(None, title="生产工艺", max_length=500)
    reason: Optional[str] = Field(None, title="认证申请被拒绝的原因", max_length=500)
    day_left: Optional[int] = Field(None, title="距离认证结束的天数")

    authority: AuthorityRowSchema = Field(..., title="认证机构")
    application_status: CERTIFICATION_APPLICATION_STATUS = Field(..., title="受理状态")
    certification_status: Union[CERTIFICATION_STATUS, None] = Field(None, title="认证状态")
    supervision_status: Union[SUPERVISION_STATUS, None] = Field(None, title="监督管理状态")

    application_finish_time: datetime | None = Field(..., title="受理完成时间")
    certification_finish_time: datetime | None = Field(..., title="认证完成时间")
    certification_start_time: datetime | None = Field(..., title="认证开始时间")
    create_time: datetime = Field(..., title="创建时间")


class CertificationManufacturerSchema(CertificationRowSchema):
    """企业能看到的认证"""

    document_review: Optional[DocumentReviewFileSchema] = Field(None, title="文件评审")
    onsite_inspection: Optional[OnSiteInspectionReportFileSchema] = Field(None, title="现场检查")
    carbon_footprint_verification: Optional[CarbonFootprintVerificationFileSchema] = Field(None, title="碳足迹核查")
    verification_report_issuance: Optional[VerificationReportIssuanceFileSchema] = Field(None, title="出具核查报告")
    certificate_issuance: Optional[CertificateIssuanceFileSchema] = Field(None, title="证书")
    supervision: Optional[SupervisionFileSchema] = Field(None, title="监督管理", alias="last_supervision")
    create_time: datetime = Field(..., title="创建时间")

    image: Optional[FileOut] = Field(..., title="产品图片")
    business_license: FileOut = Field(..., title="营业执照")
    agency_relationship: FileOut | None = Field(None, title="委托关系")
    letter_of_authorization: FileOut = Field(..., title="委托书")
    intellectual_property: FileOut | None = Field(None, title="知识产权关系")
    process_flows: list[FileOut] = Field(..., title="工艺流程图")
    organizational_chart: FileOut = Field(..., title="生产企业组织机构图")
    equipments: list[FileOut] = Field(..., title="主要生产、计量设备清单")
    bom: FileOut = Field(..., title="原辅材料清单")
    labels: list[FileOut] = Field(..., title="标识认证数据和信息采集清单")
    plans: list[FileOut] = Field(..., title="产品降碳方案或计划")
    capabilities: list[FileOut] = Field(..., title="保证能力相关管理文件或目录")
    others: list[FileOut] = Field(..., title="其它")
    application_finish_time: datetime | None = Field(..., title="受理完成时间")
    certification_finish_time: datetime | None = Field(..., title="认证完成时间")
    create_time: datetime = Field(..., title="创建时间")

    submission_status: Optional[CERTIFICATION_STEP_STATUS] = Field(None, title="发起认证申请状态")
    acceptance_review_status: Optional[CERTIFICATION_STEP_STATUS] = Field(None, title="认证受理评审状态")
    plan_status: Optional[CERTIFICATION_STEP_STATUS] = Field(None, title="制定认证方案状态")
    document_review_status: Optional[CERTIFICATION_STEP_STATUS] = Field(None, title="开展文件评审状态")
    onsite_inspection_status: Optional[CERTIFICATION_STEP_STATUS] = Field(None, title="开展现场检查状态")
    carbon_footprint_verification_status: Optional[CERTIFICATION_STEP_STATUS] = Field(None, title="碳足迹核查状态")
    verification_report_issuance_status: Optional[CERTIFICATION_STEP_STATUS] = Field(None, title="出具核查报告状态")
    review_decision_status: Optional[CERTIFICATION_STEP_STATUS] = Field(None, title="复合与认证决定状态")
    certificate_issuance_status: Optional[CERTIFICATION_STEP_STATUS] = Field(None, title="发放证书及标识状态")
    supervision_status: Optional[CERTIFICATION_STEP_STATUS] = Field(None, title="监督管理状态")


class CertificationInSchema(Schema):
    """提交认证"""

    authority_id: int = Field(..., title="认证机构")
    model_id: int = Field(..., title="模型id")
    principal: str = Field(..., title="委托方名称", max_length=100)
    principal_address: str = Field(..., title="委托人地址", max_length=100)
    manufacturer_name: str = Field(..., title="生产者（制造商）名称", max_length=100)
    manufacturer_address: str = Field(..., title="生产者（制造商）名称地址", max_length=100)
    producer_name: str = Field(..., title="生产企业名称", max_length=100)
    producer_address: str = Field(..., title="生产企业地址", max_length=100)
    contact_name: str = Field(..., title="联系人", max_length=100)
    contact_phone: str = Field(..., title="联系人电话", max_length=100)
    category_id: str = Field(..., title="产品类别")
    summary: str = Field(..., title="企业概况", max_length=500)
    product_name: str = Field(..., title="产品名称", max_length=100)
    product_description: str = Field(..., title="产品介绍", max_length=500)
    product_standard: Optional[str] = Field(None, title="产品执行标准", max_length=255)
    product_performance: Optional[str] = Field(None, title="产品性能指标", max_length=255)
    product_function: Optional[str] = Field(None, title="产品功能", max_length=500)
    production_process: Optional[str] = Field(None, title="产品生产工艺", max_length=500)
    image_id: Optional[str] = Field(None, title="产品图片")
    business_license_id: str = Field(..., title="营业执照复印件")
    agency_relationship_id: str | None = Field(None, title="委托关系证明/合同证明")
    letter_of_authorization_id: str = Field(..., title="认证委托书")
    intellectual_property_id: str | None = Field(None, title="OEM/ODM 的知识产权关系")
    process_flow_ids: list[str] = Field(..., title="产品工艺流程图")
    organizational_chart_id: str = Field(..., title="生产企业组织机构图")
    equipment_ids: list[str] = Field(..., title="主要生产、计量设备清单")
    bom_id: str = Field(..., title="原辅材料清单")
    label_ids: list[str] = Field(..., title="标识认证数据和信息采集清单")
    plan_ids: list[str] = Field(..., title="产品降碳方案或计划")
    capabilitie_ids: list[str] = Field(..., title="保证能力相关管理文件或目录")
    other_ids: list[str] = Field(..., title="其它")


class SupervisionBaseSchema(Schema):
    issue_severe_quality_problem: Optional[bool] = Field(None, title="是否出现严重质量问题")
    product_certification_quality_doubt: Optional[bool] = Field(None, title="是否对碳足迹量化提出质疑")
    the_same: Optional[bool] = Field(None, title="标准符合性或产品一致性的")

    enterprise_capability_supervision: Optional[bool] = Field(None, title="企业保证能力监督检查")
    product_consistency_supervision: Optional[bool] = Field(None, title="产品一致性监督检查")
    product_footprint_verification: Optional[bool] = Field(None, title="产品碳足迹核查")
    carbon_reduction_plan_supervision: Optional[bool] = Field(None, title="降碳计划实施监督检查")
    product_footprint_decline_supervision: Optional[bool] = Field(None, title="产品碳足迹量化是否较上一次核查有所下降")
    previous_review_issue_supervision: Optional[bool] = Field(None, title="上次评价整改措施监督")

    supervision_result_evaluation: Optional[str] = Field(None, title="监督结果的评价", max_length=500)
    start_time: Optional[datetime] = Field(None, title="开始监督时间")
    end_time: Optional[datetime] = Field(None, title="结束监督时间")
    status: Optional[SUPERVISION_STATUS] = Field(None, title="监督状态")

    result: Optional[bool] = Field(None, title="监督决定")


class SupervisionInSchema(Schema):
    issue_severe_quality_problem: bool = Field(..., title="是否出现严重质量问题")
    product_certification_quality_doubt: bool = Field(..., title="是否对碳足迹量化提出质疑")
    the_same: bool = Field(..., title="标准符合性或产品一致性的")

    enterprise_capability_supervision: bool = Field(..., title="企业保证能力监督检查")
    product_consistency_supervision: bool = Field(..., title="产品一致性监督检查")
    product_footprint_verification: bool = Field(..., title="产品碳足迹核查")
    carbon_reduction_plan_supervision: bool = Field(..., title="降碳计划实施监督检查")
    product_footprint_decline_supervision: bool = Field(..., title="产品碳足迹量化是否较上一次核查有所下降")
    previous_review_issue_supervision: bool = Field(..., title="上次评价整改措施监督")
    supervision_result_evaluation: str = Field(..., title="监督结果的评价", max_length=500)
    result: bool = Field(..., title="监督决定")
    result_file_id: Optional[str] = Field(None, title="监督决定审批文件")

    @model_validator(mode="after")
    def validate_result_file_id(self):
        """如果result是false，result_file_id必填"""
        b = (
            self.issue_severe_quality_problem is False
            and self.product_certification_quality_doubt is False
            and self.the_same is False
            and self.enterprise_capability_supervision is True
            and self.product_consistency_supervision is True
            and self.product_footprint_verification is True
            and self.carbon_reduction_plan_supervision is True
            and self.product_footprint_decline_supervision is True
            and self.previous_review_issue_supervision is True
        )
        if b != self.result:
            raise HttpError(422, "监督结果和监督内容不匹配")
        if not self.result and not self.result_file_id:
            raise HttpError(422, "当监督结果为否定时，必须提供监督决定审批文件")
        return self


class SupervisionRowSchema(SupervisionBaseSchema):
    """监督管理"""

    id: int = Field(..., title="id")
    certification: CertificationRowSchema = Field(..., title="认证")


class SupervisionSchema(SupervisionBaseSchema):
    id: int = Field(..., title="id")
    result_file: Optional[FileOut] = Field(None, title="监督决定审批文件")


class CertificationGovmentChartItemSchema(Schema):
    name: str = Field(..., title="名称")
    count: int = Field(..., title="数量")


class OntimeItemSchem(Schema):
    """实时数据"""

    geography: GeographySchema = Field(..., title="地区")
    product: str = Field(..., title="产品")
    count: int = Field(..., title="通过数量")
    percent: float = Field(..., title="通过比例")


class CertificationRegionAuthorityItemSchema(Schema):
    authority: AuthorityRowSchema = Field(..., title="认证机构")
    product: str = Field(..., title="产品")
    count: int = Field(..., title="数量")


class CertificationGovmentNumSchema(Schema):
    total: int = Field(..., title="因子总数")
    category: int = Field(..., title="覆盖行业数")
    geography: int = Field(..., title="涵盖地区数")


class CertificationGovmentSchema(Schema):
    """认证管理政府数据"""

    category: list[CertificationGovmentChartItemSchema] = Field(..., title="按行业分类柱")
    year: list[CertificationGovmentChartItemSchema] = Field(..., title="年度数据收录增长趋势")
    geography: list[OntimeItemSchem] = Field(..., title="按地区")
    nums: CertificationGovmentNumSchema = Field(..., title="数量")


class PageNumberPaginationRegion(Schema):
    """带地区提示的分页"""

    items: list[AuthoritySchema]
    count: int
    product: str
    regions: list[RegionSchema]


class CertificationIndexSchema(Schema):
    """认证首页"""

    manufacturer_num: int = Field(..., title="获证企业")
    product_num: int = Field(..., title="产品数")


class CertificationAuthorityIndexSchema(Schema):
    """认证机构首页"""

    # 受理管理数量 认证管理数量 监督管理数量
    application_num: int = Field(..., title="认证申请数量")
    certification_num: int = Field(..., title="认证数量")
    supervision_num: int = Field(..., title="监督管理数量")
    this_month_application_num: int = Field(..., title="本月申请")
    ongoing_certification_num: int = Field(..., title="认证中")
    issued_certificate_num: int = Field(..., title="发证数量")
    total_application_num: int = Field(..., title="累计申请")


class CopyModelFileSchema(Schema):
    bom: FileOut | None = Field(..., title="原辅材料清单")
    process_flows: list[FileOut] = Field(..., title="工艺流程图")
