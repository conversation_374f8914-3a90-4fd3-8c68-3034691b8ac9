# Generated by Django 5.2.1 on 2025-07-11 01:45

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0081_certification_last_supervision"),
    ]

    operations = [
        migrations.AddField(
            model_name="group",
            name="is_government",
            field=models.BooleanField(db_index=True, default=False, verbose_name="是否政府部门"),
        ),
        migrations.AddField(
            model_name="group",
            name="is_predefined",
            field=models.BooleanField(db_index=True, default=False, verbose_name="是否预定义"),
        ),
        migrations.AlterField(
            model_name="authority",
            name="description",
            field=models.TextField(default="", max_length=500, verbose_name="机构描述"),
        ),
        migrations.AlterField(
            model_name="certification",
            name="product_description",
            field=models.TextField(max_length=500, verbose_name="产品介绍"),
        ),
        migrations.AlterField(
            model_name="certification",
            name="product_function",
            field=models.CharField(blank=True, max_length=500, null=True, verbose_name="产品功能"),
        ),
        migrations.AlterField(
            model_name="certification",
            name="production_process",
            field=models.TextField(blank=True, max_length=500, null=True, verbose_name="产品生产工艺"),
        ),
        migrations.AlterField(
            model_name="certification",
            name="summary",
            field=models.TextField(max_length=500, verbose_name="企业概况"),
        ),
        migrations.AlterField(
            model_name="certificationcarbonfootprintverification",
            name="data_acquisition_principle",
            field=models.TextField(max_length=500, null=True, verbose_name="数据取舍原则"),
        ),
        migrations.AlterField(
            model_name="certificationcarbonfootprintverification",
            name="data_quality_evaluation",
            field=models.TextField(max_length=500, null=True, verbose_name="数据质量评价"),
        ),
        migrations.AlterField(
            model_name="certificationcarbonfootprintverification",
            name="data_quality_requirement",
            field=models.TextField(max_length=500, null=True, verbose_name="数据质量要求"),
        ),
        migrations.AlterField(
            model_name="certificationcarbonfootprintverification",
            name="quantification_method",
            field=models.TextField(max_length=500, null=True, verbose_name="量化方法"),
        ),
        migrations.AlterField(
            model_name="certificationcarbonfootprintverification",
            name="uncertainty_analysis",
            field=models.TextField(max_length=500, null=True, verbose_name="不确定性分析"),
        ),
        migrations.AlterField(
            model_name="certificationcarbonfootprintverification",
            name="verification_conclusion",
            field=models.TextField(max_length=500, null=True, verbose_name="核查结论"),
        ),
        migrations.AlterField(
            model_name="certificationcarbonfootprintverification",
            name="verification_recommendation",
            field=models.TextField(max_length=500, null=True, verbose_name="核查建议"),
        ),
        migrations.AlterField(
            model_name="certificationonsiteinspection",
            name="basis",
            field=models.CharField(max_length=500, null=True, verbose_name="现场检查的依据"),
        ),
        migrations.AlterField(
            model_name="certificationonsiteinspection",
            name="process_description",
            field=models.TextField(max_length=500, null=True, verbose_name="现场检查过程的描述"),
        ),
        migrations.AlterField(
            model_name="certificationonsiteinspection",
            name="purpose",
            field=models.CharField(max_length=500, null=True, verbose_name="现场检查的目的"),
        ),
        migrations.AlterField(
            model_name="certificationonsiteinspection",
            name="result",
            field=models.TextField(max_length=500, null=True, verbose_name="现场检查结果"),
        ),
        migrations.AlterField(
            model_name="certificationonsiteinspection",
            name="scope",
            field=models.CharField(max_length=500, null=True, verbose_name="现场检查的范围"),
        ),
        migrations.AlterField(
            model_name="certificationplan",
            name="accompanying_inspectors",
            field=models.TextField(max_length=500, null=True, verbose_name="检查组同行人员"),
        ),
        migrations.AlterField(
            model_name="certificationplan",
            name="field_inspection_requirement",
            field=models.TextField(max_length=500, null=True, verbose_name="现场检查要求"),
        ),
        migrations.AlterField(
            model_name="certificationplan",
            name="progress_schedule",
            field=models.TextField(max_length=500, null=True, verbose_name="进度安排"),
        ),
        migrations.AlterField(
            model_name="manufacturer",
            name="description",
            field=models.TextField(max_length=500, verbose_name="企业主营业务简介"),
        ),
        migrations.AlterField(
            model_name="manufacturer",
            name="products",
            field=models.CharField(max_length=100, verbose_name="拟认证产品"),
        ),
        migrations.AlterField(
            model_name="supervision",
            name="supervision_result_evaluation",
            field=models.TextField(max_length=500, null=True, verbose_name="监督结果的评价"),
        ),
    ]
