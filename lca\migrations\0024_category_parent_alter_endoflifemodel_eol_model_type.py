# Generated by Django 5.1.7 on 2025-04-23 09:17

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0023_emissionsource_average_trans_distance"),
    ]

    operations = [
        migrations.AddField(
            model_name="category",
            name="parent",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="children",
                to="lca.category",
                verbose_name="上级",
            ),
        ),
        migrations.AlterField(
            model_name="endoflifemodel",
            name="eol_model_type",
            field=models.CharField(
                choices=[
                    ("RECYCLE", "回收"),
                    ("REUSE", "再利用"),
                    ("ENERGY_RECOVERY", "能量回收"),
                    ("COMPOST", "堆肥"),
                    ("LANDFILL", "填埋"),
                    ("COMBUST", "焚烧"),
                    ("BIODEGRADATION", "生物降解"),
                    ("OTHER", "其他"),
                ],
                max_length=100,
                verbose_name="类型",
            ),
        ),
    ]
