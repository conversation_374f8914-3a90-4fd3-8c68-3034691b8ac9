from datetime import datetime
from typing import Optional
from ninja import Schema
from pydantic import Field

from infra.types import Code, Phone
from lca.accounting.models import CERTIFICATE_PRODUCT
from lca.accounting.schema import GeographySchema
from lca.file.schema import FileOut
from lca.users.models import PERMISSION_TYPES, USER_CERTIFICATION_STATUS, USER_TYPES


class ManufacturerCertificationSchema(Schema):
    id: int = Field(..., title="id")
    name: str = Field(..., title="企业名称")
    code: str = Field(..., title="统一社会信用代码")
    geography: GeographySchema = Field(..., title="地区")
    contact_name: str = Field(..., title="联系人")
    contact_phone: str = Field(..., title="联系人手机")
    contact_email: str = Field(..., title="联系人邮箱")
    address: str = Field(..., title="企业地址")
    description: str = Field(..., title="企业主营业务简介")
    products: str = Field(..., title="拟认证产品")
    website: str = Field(None, title="企业官网")
    license: FileOut = Field(..., title="营业执照")


class AuthorityCertificationSchema(Schema):
    name: str = Field(..., title="认证机构名称")
    code: str = Field(..., title="统一社会信用代码")
    geography: GeographySchema = Field(..., title="机构地区")
    address: str = Field(..., title="机构详细地址")
    approval_number: str = Field(..., title="机构批准号")
    description: str = Field(..., title="机构简介")
    products: list[CERTIFICATE_PRODUCT] = Field(..., title="认证产品")
    contact_name: str = Field(..., title="联系人名称")
    contact_phone: str = Field(..., title="联系人手机")
    contact_email: str = Field(..., title="联系人邮箱")
    approval_license: FileOut = Field(..., title="批准证书")
    sign: FileOut = Field(..., title="电子签章")
    logo: FileOut = Field(..., title="LOGO")
    license: FileOut = Field(..., title="营业执照")


class GroupSchema(Schema):
    id: int = Field(..., title="id")
    name: str = Field(..., title="名称")
    description: str = Field(..., title="描述")
    is_active: bool = Field(..., title="是否有效")
    is_predefined: bool = Field(..., title="是否预定义")
    creator: "UserRowSchema" = Field(..., title="创建者")
    create_time: datetime = Field(..., title="创建时间")
    update_time: datetime = Field(..., title="更新时间")


class GroupSimpleSchema(Schema):
    id: int = Field(..., title="id")
    name: str = Field(..., title="名称")
    creator: "UserRowSchema" = Field(..., title="创建者")


class GroupWithCountSchema(GroupSchema):
    user_count: int = Field(..., title="用户数量")


class GroupInSchema(Schema):
    name: str = Field(..., title="名称")
    description: str = Field(..., title="描述")
    is_active: bool = Field(..., title="是否有效")


class PermissionSchema(Schema):
    id: int = Field(..., title="id")
    name: str = Field(..., title="名称")
    order: int = Field(..., title="排序小的在前")
    codename: str = Field(..., title="代码")
    parent_id: Optional[int] = Field(None, title="上级id")
    type: PERMISSION_TYPES = Field(..., title="类型")


class GroupDetailSchema(GroupSchema):
    id: int = Field(..., title="id")
    permissions: list[int] = Field(..., title="权限", alias="permission_ids")


class UserMySchema(Schema):
    id: int = Field(..., title="ID")
    phone: str = Field(..., title="手机号")
    is_active: bool = Field(..., title="是否有效")
    is_superuser: bool = Field(..., title="是否为超级管理员")
    is_staff: bool = Field(..., title="是否为员工")
    nickname: str = Field(..., title="昵称")
    type: USER_TYPES | None = Field(..., title="用户类型")
    certificate_status: USER_CERTIFICATION_STATUS = Field(..., title="认证状态")
    authority_certification: AuthorityCertificationSchema | None = Field(..., title="认证机构认证信息")
    manufacturer_certification: ManufacturerCertificationSchema | None = Field(..., title="企业认证信息")
    groups: list[GroupSimpleSchema] = Field(..., title="角色")
    description: str = Field(..., title="描述")
    permissions: list[str] = Field(..., title="权限")
    create_time: datetime = Field(..., title="创建时间")
    update_time: datetime = Field(..., title="更新时间")


class CreateUserSchema(Schema):
    nickname: str = Field(..., title="昵称")
    phone: str = Field(..., title="手机号")
    is_active: bool = Field(..., title="是否有效")
    groups: list[int] = Field(..., title="角色")
    description: str = Field(None, title="描述")


class UserRowMoreSchema(Schema):
    id: int = Field(..., title="id")
    phone: str = Field(..., title="手机号")
    nickname: str = Field(..., title="昵称")
    is_staff: bool = Field(..., title="是否为员工")
    type: USER_TYPES | None = Field(..., title="用户类型")
    certificate_status: USER_CERTIFICATION_STATUS = Field(..., title="认证状态")
    groups: list[GroupSchema] = Field(..., title="角色")
    is_superuser: bool = Field(..., title="是否为超级管理员")
    is_active: bool = Field(..., title="是否有效")
    create_time: datetime = Field(..., title="创建时间")
    update_time: datetime = Field(..., title="更新时间")


class UserRowSchema(Schema):
    """列表页用到的用户信息"""

    id: int = Field(..., title="id")
    phone: str = Field(..., title="手机号")
    nickname: str = Field(..., title="昵称")


class UserCertificationSchema(Schema):
    id: int = Field(..., title="id")
    user: UserRowSchema = Field(..., title="用户")
    status: USER_CERTIFICATION_STATUS = Field(..., title="认证状态")
    type: USER_TYPES = Field(..., title="用户类型")
    reason: Optional[str] = Field(None, title="拒绝原因")
    manufacturer_certification: Optional[ManufacturerCertificationSchema] = Field(None, title="企业认证信息")
    authority_certification: Optional[AuthorityCertificationSchema] = Field(None, title="认证机构认证信息")
    operator: Optional[UserRowSchema] = Field(None, title="审批人")
    finish_time: Optional[datetime] = Field(None, title="认证完成时间")
    create_time: datetime = Field(..., title="创建时间")
    update_time: datetime = Field(..., title="更新时间")


class ManufacturerCertificationInSchema(Schema):
    name: str = Field(..., title="企业名称")
    code: str = Field(..., title="统一社会信用代码")
    geography_id: str = Field(..., title="地区")
    contact_name: str = Field(..., title="联系人")
    contact_phone: str = Field(..., title="联系人手机")
    contact_email: str = Field(..., title="联系人邮箱")
    address: str = Field(..., title="企业地址")
    products: str = Field(..., title="拟认证产品")
    description: str = Field(..., title="企业主营业务简介")
    website: str = Field(..., title="企业官网")
    license_id: str = Field(..., title="营业执照")


class AuthorityCertificationInSchema(Schema):
    name: str = Field(..., title="认证机构名称")
    code: str = Field(..., title="统一社会信用代码")
    geography_id: str = Field(..., title="地区")
    address: str = Field(..., title="机构详细地址")
    approval_number: str = Field(..., title="机构批准号")
    products: list[CERTIFICATE_PRODUCT] = Field(..., title="认证产品")
    description: str = Field(..., title="机构简介")
    contact_name: str = Field(..., title="联系人名称")
    contact_phone: str = Field(..., title="联系人手机")
    contact_email: str = Field(..., title="联系人邮箱")
    approval_license_id: str = Field(..., title="批准证书")
    license_id: str = Field(..., title="营业执照")
    sign_id: str = Field(..., title="电子签章")
    logo_id: str = Field(..., title="LOGO")


class UserCertificationInSchema(Schema):
    type: USER_TYPES = Field(..., title="用户类型")
    manufacturer_certification: Optional[ManufacturerCertificationInSchema] = Field(None, title="企业认证信息")
    authority_certification: Optional[AuthorityCertificationInSchema] = Field(None, title="认证机构认证信息")


class UserCertificateRejectInSchema(Schema):
    reason: str = Field(..., title="拒绝原因")


class SendSMSINSchema(Schema):
    uuid: str = Field(..., title="验证码")
    secret: str = Field(..., title="滑块密钥")
    phone: Phone = Field(..., title="手机号")


class LoginSchema(Schema):
    phone: Phone = Field(..., title="手机号")
    code: Code = Field(..., title="验证码")


class ShieldSchema(Schema):
    background: str = Field(..., title="背景图片 base64")
    foreground: str = Field(..., title="前景图片 base64")
    uuid: str = Field(..., title="滑块uuid")


class ShieldVerifyInSchema(Schema):
    uuid: str = Field(..., title="滑块uuid")
    captcha: float = Field(..., title="滑块位置")


class ShieldVerifySchema(Schema):
    uuid: str = Field(..., title="滑块uuid")
    secret: str = Field(..., title="密钥")


class RegisterInSchema(Schema):
    nickname: str = Field(..., title="昵称")
    phone: Phone = Field(..., title="手机号")
    code: Code = Field(..., title="验证码")


class UpdateUserSchema(Schema):
    nickname: str = Field(..., title="昵称")
