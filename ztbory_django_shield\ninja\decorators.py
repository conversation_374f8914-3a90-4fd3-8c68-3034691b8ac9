import functools

from pydantic import BaseModel

from ztbory_django_shield.base.core import Shield
from ztbory_django_shield.core.schemas import ShieldLoginSchema
from ztbory_django_shield.core.utils import get_client_ip


def shield_login_decorator(with_cryptor=False):
    """双因子登录装饰器

    with_cryptor: True
        用户名会进行sm2解密后进行检测

    示例：
        @router.post("", auth=None, response={201: Token})
        @shield_login_decorator
        def login(
            request,
            login: NinjaSchemaModel = Body(..., description="用户名密码"),
            # other_params: str = Query(None, description="其他参数"),
        ):
            your_login_logic

    应用先决条件:
        1. login(登录表单)需要占据第二个参数位置
        2. login(NinjaSchemaModel)必须包含username, captcha_uuid, captcha 三个字段
        3. your_login_logic, 登录成功返回接口返回内容， 登录失败返回None
    """

    def decorator(func):
        @functools.wraps(func)
        def wrapper(request, login: type(BaseModel), **kwargs):
            data = ShieldLoginSchema(
                username=getattr(login, "username", None),
                ip=get_client_ip(request),
                captcha_uuid=getattr(login, "captcha_uuid", None),
                captcha=getattr(login, "captcha", None),
            )
            if with_cryptor:
                data.username = Shield.decrypt(data.username)

            return Shield.login(data=data, login_func=func, login_func_args=((request, login), kwargs))

        return wrapper

    return decorator
