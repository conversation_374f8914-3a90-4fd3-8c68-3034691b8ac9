import logging

from ninja import File, Router, UploadedFile
from ninja.errors import HttpError
from lca.accounting.models import Model
from lca.file.schema import FileOut
from lca.file.service import FileService
from lca.file.models import File as FileModel


logger = logging.getLogger("file")

router = Router(tags=["文件管理"])


@router.post("", summary="上传文件", response=FileOut)
def upload(request, file: UploadedFile = File(...)):
    ext_name = file.name.split(".")[-1]
    path = FileService.get_path()
    file_name = FileService.get_file_name()
    object_name = "{path}/{file_name}.{ext}".format(path=path, file_name=file_name, ext=ext_name)

    try:
        file = FileService.upload(object_name, file.file, file.name, file.size)

        return file
    except Exception as e:
        logger.info("upload-file error: {}", e)
        raise HttpError(400, "上传失败")


@router.get("download", summary="下载文件")
def download(request, id: str):
    file = FileModel.objects.get(pk=id)
    return FileService.download_file(file)


@router.get("/model/{id}/generate-report", summary="生成报告")
def generate_report(request, id: int):
    try:
        model = Model.objects.get(pk=id)
        return FileService.generate_report(model)
    except Model.DoesNotExist:
        raise HttpError(404, "模型产品不存在")
    except HttpError:
        raise HttpError(400, "报告已生成")
    except Exception as e:
        logger.error("generate-report error: {}", e)
        raise HttpError(400, "生成报告失败，请稍后再试")


@router.get("/model/{id}/download-report", summary="下载报告", operation_id="downloadReport")
def download_report(request, id: int):
    try:
        model = Model.objects.get(pk=id)
        return FileService.download_report(model)
    except Model.DoesNotExist:
        raise HttpError(404, "模型产品不存在")
