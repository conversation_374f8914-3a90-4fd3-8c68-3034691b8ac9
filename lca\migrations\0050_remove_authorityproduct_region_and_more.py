# Generated by Django 5.2.1 on 2025-06-27 07:56

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0049_formula_remove_process_craft_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="authorityproduct",
            name="region",
        ),
        migrations.AlterField(
            model_name="authority",
            name="avatar",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="lca.file",
                verbose_name="机构图标",
            ),
        ),
        migrations.AlterField(
            model_name="authorityproduct",
            name="authority",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to="lca.authority",
                verbose_name="认证机构",
            ),
        ),
        migrations.AlterField(
            model_name="certification",
            name="agency_relationship",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="certification_agency_relationship",
                to="lca.file",
                verbose_name="委托关系",
            ),
        ),
        migrations.AlterField(
            model_name="certification",
            name="bom",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="certification_bom",
                to="lca.file",
                verbose_name="原辅材料清单",
            ),
        ),
        migrations.AlterField(
            model_name="certification",
            name="business_license",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="certification_business_license",
                to="lca.file",
                verbose_name="营业执照",
            ),
        ),
        migrations.AlterField(
            model_name="certification",
            name="intellectual_property",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="certification_intellectual_property",
                to="lca.file",
                verbose_name="知识产权关系",
            ),
        ),
        migrations.AlterField(
            model_name="certification",
            name="letter_of_authorization",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="certification_letter_of_authorization",
                to="lca.file",
                verbose_name="委托书",
            ),
        ),
        migrations.AlterField(
            model_name="certification",
            name="organizational_chart",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="certification_organizational_chart",
                to="lca.file",
                verbose_name="生产企业组织机构图",
            ),
        ),
        migrations.AlterField(
            model_name="certificationinfo",
            name="certificate",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="certification_certificate",
                to="lca.file",
                verbose_name="证书",
            ),
        ),
        migrations.AlterField(
            model_name="certificationinfo",
            name="file_material_reject_report",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="certification_file_material_reject_report",
                to="lca.file",
                verbose_name="文件评审-认证材料-不通过报告",
            ),
        ),
        migrations.AlterField(
            model_name="certificationinfo",
            name="file_model_reject_report",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="certification_file_model_reject_report",
                to="lca.file",
                verbose_name="文件评审-生命周期模型-不通过报告",
            ),
        ),
        migrations.AlterField(
            model_name="certificationinfo",
            name="footprint_reject_report",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="certification_footprint_reject_report",
                to="lca.file",
                verbose_name="碳足迹核查-不通过报告",
            ),
        ),
        migrations.AlterField(
            model_name="certificationinfo",
            name="footprint_report",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="certification_footprint_report",
                to="lca.file",
                verbose_name="碳足迹核查报告",
            ),
        ),
        migrations.AlterField(
            model_name="certificationinfo",
            name="footprint_report2",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="certification_footprint_report2",
                to="lca.file",
                verbose_name="碳足迹复合报告",
            ),
        ),
        migrations.AlterField(
            model_name="certificationinfo",
            name="on_site_reject_report",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="certification_on_site_reject_report",
                to="lca.file",
                verbose_name="现场检查-不通过报告",
            ),
        ),
        migrations.AlterField(
            model_name="certificationinfo",
            name="on_site_report",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="certification_on_site_report",
                to="lca.file",
                verbose_name="现场检查报告",
            ),
        ),
        migrations.AlterField(
            model_name="certificationinfo",
            name="on_site_report2",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="certification_on_site_report2",
                to="lca.file",
                verbose_name="现场检查报告",
            ),
        ),
        migrations.AlterField(
            model_name="certificationinfo",
            name="plan",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="certification_info_plan",
                to="lca.file",
                verbose_name="认证方案",
            ),
        ),
        migrations.AlterField(
            model_name="certificationinfo",
            name="reject_report",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="certification_reject_report",
                to="lca.file",
                verbose_name="不通过的报告",
            ),
        ),
        migrations.AlterField(
            model_name="emissionsource",
            name="type",
            field=models.CharField(
                choices=[
                    ("raw-material", "原辅料"),
                    ("energy", "能源"),
                    ("transport", "运输"),
                ],
                max_length=50,
                verbose_name="排放源类型",
            ),
        ),
        migrations.AlterField(
            model_name="emissionsourcedataset",
            name="flow_chart",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="lca.file",
                verbose_name="工艺流程图",
            ),
        ),
        migrations.AlterField(
            model_name="input",
            name="type",
            field=models.CharField(
                choices=[
                    ("raw-material", "原辅料"),
                    ("energy", "能源"),
                    ("transport", "运输"),
                ],
                max_length=50,
                verbose_name="原材料类型",
            ),
        ),
        migrations.CreateModel(
            name="RegionProduct",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "product",
                    models.CharField(
                        choices=[
                            ("cement", "通用硅酸盐水泥"),
                            ("steel-sheet", "钢铁-板材"),
                            ("steel-wire-rod", "钢铁-线材"),
                            ("photovoltaic", "光伏组件"),
                            ("electric_vehicle", "轻型电动汽车"),
                            ("power_battery", "动力电池"),
                            ("glass", "平板玻璃"),
                            ("electrolytic_aluminum", "电解铝"),
                            ("ethylene", "乙烯"),
                            ("synthetic_ammonia", "合成氨"),
                        ],
                        db_index=True,
                        max_length=100,
                        verbose_name="产品名称",
                    ),
                ),
                (
                    "region",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.region",
                        verbose_name="区域",
                    ),
                ),
            ],
        ),
    ]
