# For more information, please refer to https://aka.ms/vscode-docker-python
FROM harbor.ztbory.com/sys/python:3.12-bullseye-carbon
#FROM  python:3.12-bullseye

EXPOSE 8000

# Keeps Python from generating .pyc files in the container
ENV PYTHONDONTWRITEBYTECODE=1

# Turns off buffering for easier container logging
ENV PYTHONUNBUFFERED=1
RUN apt-get update && apt-get install -y pandoc
# Install pip requirements
COPY requirements.txt .
# RUN python -m pip install -r requirements.txt -i http://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com
RUN python -m pip install -r requirements.txt

WORKDIR /app
COPY . /app

# 安装必要的依赖
#RUN apt-get update --fix-missing && \
#    apt-get install -y --no-install-recommends \
#        ca-certificates \
#        curl \
#        gnupg \
#    && apt-get install -y \
#        libreoffice \
#        unoconv \
#        fonts-wqy-zenhei \
#    && rm -rf /var/lib/apt/lists/*

# During debugging, this entry point will be overridden. For more information, please refer to https://aka.ms/vscode-docker-python-debug
# CMD ["gunicorn", "--bind", "0.0.0.0:8000", "config.wsgi"]
CMD ["gunicorn"]
