# Generated by Django 5.2.1 on 2025-06-05 09:24

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0045_flow_type"),
    ]

    operations = [
        migrations.RenameField(
            model_name="emissionsourcedataset",
            old_name="emmission_source",
            new_name="emission_source",
        ),
        migrations.RenameField(
            model_name="emissionsourcemanagement",
            old_name="emmission_source",
            new_name="emission_source",
        ),
        migrations.RenameField(
            model_name="emissionsourceproduct",
            old_name="emmission_source",
            new_name="emission_source",
        ),
        migrations.AlterField(
            model_name="emissionsourcedataset",
            name="boundary",
            field=models.CharField(
                choices=[
                    ("cradle-gate", "从摇篮到大门"),
                    ("cradle-grave", "从摇篮到坟墓"),
                ],
                max_length=50,
                verbose_name="系统边界",
            ),
        ),
        migrations.AlterField(
            model_name="flow",
            name="type",
            field=models.CharField(
                choices=[
                    ("elementary", "基本流"),
                    ("intermediate", "中间流"),
                    ("product", "产品流"),
                    ("co-product", "共产品流"),
                    ("waste", "废物流"),
                    ("resource", "资源流"),
                ],
                max_length=20,
                verbose_name="流类型",
            ),
        ),
        migrations.AlterField(
            model_name="model",
            name="boundary",
            field=models.CharField(
                choices=[
                    ("cradle-gate", "从摇篮到大门"),
                    ("cradle-grave", "从摇篮到坟墓"),
                ],
                max_length=50,
                verbose_name="系统边界",
            ),
        ),
    ]
