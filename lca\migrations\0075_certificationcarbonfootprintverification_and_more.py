# Generated by Django 5.2.1 on 2025-07-07 14:09

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0074_certificationonsiteinspection"),
    ]

    operations = [
        migrations.CreateModel(
            name="CertificationCarbonFootprintVerification",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "product_name",
                    models.CharField(max_length=100, verbose_name="产品名称"),
                ),
                ("specs", models.Char<PERSON>ield(max_length=100, verbose_name="产品型号")),
                (
                    "functional_unit",
                    models.Char<PERSON>ield(max_length=100, verbose_name="功能单位"),
                ),
                ("boundary", models.Char<PERSON>ield(max_length=100, verbose_name="系统边界")),
                (
                    "client_name",
                    models.Char<PERSON>ield(max_length=100, verbose_name="受核查方名称"),
                ),
                (
                    "address",
                    models.Char<PERSON>ield(max_length=100, verbose_name="受核查方地址"),
                ),
                (
                    "credit_code",
                    models.CharField(max_length=100, verbose_name="统一社会信用代码"),
                ),
                (
                    "legal_representative",
                    models.CharField(max_length=100, verbose_name="法定代表人"),
                ),
                ("verification_date", models.DateField(verbose_name="核查日期")),
                ("initial_inspections", models.JSONField(verbose_name="初级数据核查")),
                ("followup_inspections", models.JSONField(verbose_name="次级数据核查")),
                (
                    "data_acquisition_principle",
                    models.TextField(null=True, verbose_name="数据获取原则"),
                ),
                (
                    "data_quality_requirement",
                    models.TextField(null=True, verbose_name="数据质量要求"),
                ),
                (
                    "data_quality_evaluation",
                    models.TextField(null=True, verbose_name="数据质量评价"),
                ),
                (
                    "quantification_method",
                    models.TextField(null=True, verbose_name="量化方法"),
                ),
                (
                    "product_quantifications",
                    models.JSONField(verbose_name="产品碳足迹量化"),
                ),
                (
                    "verification_conclusion",
                    models.TextField(null=True, verbose_name="核查结论"),
                ),
                (
                    "verification_recommendation",
                    models.TextField(null=True, verbose_name="核查建议"),
                ),
                (
                    "uncertainty_analysis",
                    models.TextField(null=True, verbose_name="不确定性分析"),
                ),
                ("result", models.BooleanField(null=True, verbose_name="核查结果")),
                (
                    "create_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "certification",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="carbon_footprint_verification",
                        to="lca.certification",
                        verbose_name="认证记录",
                    ),
                ),
                (
                    "result_file",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="carbon_footprint_verification_result_file",
                        to="lca.file",
                        verbose_name="核查结果审批文件",
                    ),
                ),
            ],
        ),
        migrations.DeleteModel(
            name="CarbonFootprintVerification",
        ),
    ]
