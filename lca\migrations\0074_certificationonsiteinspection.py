# Generated by Django 5.2.1 on 2025-07-07 12:53

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0073_certificationdocumentreview"),
    ]

    operations = [
        migrations.CreateModel(
            name="CertificationOnsiteInspection",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("principal", models.CharField(max_length=100, verbose_name="委托人")),
                (
                    "principal_address",
                    models.CharField(max_length=100, verbose_name="委托人地址"),
                ),
                (
                    "manufacturer",
                    models.Char<PERSON>ield(max_length=100, verbose_name="生产企业名称"),
                ),
                (
                    "manufacturer_address",
                    models.CharField(max_length=100, verbose_name="生产企业地址"),
                ),
                (
                    "producer",
                    models.Char<PERSON><PERSON>(max_length=100, verbose_name="生产者（制造商）名称"),
                ),
                (
                    "producer_address",
                    models.<PERSON>r<PERSON><PERSON>(max_length=100, verbose_name="生产者（制造商）地址"),
                ),
                (
                    "purpose",
                    models.CharField(max_length=100, null=True, verbose_name="现场检查的目的"),
                ),
                (
                    "basis",
                    models.CharField(max_length=100, null=True, verbose_name="现场检查的依据"),
                ),
                (
                    "scope",
                    models.CharField(max_length=100, null=True, verbose_name="现场检查的范围"),
                ),
                (
                    "capability_accepted",
                    models.BooleanField(null=True, verbose_name="企业保证能力检查"),
                ),
                (
                    "name_file_accepted",
                    models.BooleanField(
                        null=True,
                        verbose_name="认证产品的名称、型号、生产企业及相关标识与申请文件或证书是否一致",
                    ),
                ),
                (
                    "design_file_accepted",
                    models.BooleanField(
                        null=True,
                        verbose_name="认证产品的设计、关键件、能源和资源、生产工艺、交付及储存、使用、回收与处置等适用环节碳足迹数据和信息与申请文件是否一致",
                    ),
                ),
                (
                    "design_product_accepted",
                    models.BooleanField(
                        null=True,
                        verbose_name="认证产品的设计、关键件、能源和资源、生产工艺、交付及储存、使用、回收与处置等环节与所确认产品是否一致",
                    ),
                ),
                (
                    "process_description",
                    models.TextField(null=True, verbose_name="现场检查过程的描述"),
                ),
                (
                    "nonconformities",
                    models.JSONField(null=True, verbose_name="不符合项详情及整改建议"),
                ),
                ("result", models.TextField(null=True, verbose_name="现场检查结果")),
                (
                    "create_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "certification",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="onsite_inspection",
                        to="lca.certification",
                        verbose_name="认证记录",
                    ),
                ),
                (
                    "file",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="onsite_inspection_file",
                        to="lca.file",
                        verbose_name="现场检查报告",
                    ),
                ),
            ],
        ),
    ]
