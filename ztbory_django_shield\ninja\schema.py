from ninja import Schema
from pydantic import Field


class SlideCaptchaOut(Schema):
    """滑动校验码数据返回模型"""

    uuid: str = Field(..., title="滑动验证码标识")
    foreground: str = Field(..., title="滑动验证码前置图片 base64格式")
    background: str = Field(..., title="滑动验证码后置图片 base64格式")


class SlideCaptchaVerifyPayload(Schema):
    """滑动校验码滑动校验表单"""

    uuid: str = Field(..., title="滑动验证码标识")
    captcha: float = Field(..., title="滑动像素值")


class SlideCaptchaVerifyOut(Schema):
    """滑动密钥校验表单"""

    uuid: str = Field(..., title="滑动验证码标识")
    secret: str = Field(..., title="滑动验证码提交校验码")
