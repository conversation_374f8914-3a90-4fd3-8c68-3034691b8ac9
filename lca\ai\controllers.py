from ninja import Router
from infra.schemas import successful
from lca.accounting.models import Category, Model
from lca.accounting.schema import GuessInfoInSchema, GuessInfoSchema
from lca.ai.schema import FileAISchema, GoalScopeSchema
from lca.ai.services import AI
from lca.file.models import File
import pandas as pd
from io import StringIO
from django.http import StreamingHttpResponse

router = Router(tags=["大模型"])


@router.post(
    "name-to-category-unit",
    summary="从名字推断类别和功能单位",
    response=GuessInfoSchema | None,
)
def get_category_unit_by_name(request, data: GuessInfoInSchema):
    text = """
产品名称,功能单位,所属行业
光伏组件,标称功率为1kWp的地面用晶体硅光伏组件,电气机械及器材
动力电池,向车辆提供 1 千瓦时（kWh）的能量,电气机械及器材
轻型电动汽车,一辆轻型电动汽车生命周期内提供1km的运输服务,交通运输设备
通用硅酸盐水泥,1吨通用硅酸盐水泥,非金属矿物制品
平板玻璃,1kg平板玻璃原片,非金属矿物制品
电解铝,生产1吨电解铝,有色金属冶炼及压延产品
钢铁-板材,1吨钢铁板材,黑色金属冶炼及压延产品
钢铁-线材,1吨钢铁线材,黑色金属冶炼及压延产品
乙烯,生产1吨石脑油制乙烯,化学原料及化学制品
合成氨,生产1吨煤基合成氨,化学原料及化学制品
"""
    df = pd.read_csv(StringIO(text))
    row = df[df["产品名称"] == data.name].head(1)
    if len(row) == 0:
        return None

    row = row.to_dict(orient="records")[0]
    category = Category.objects.get(name=row["所属行业"])
    return dict(
        name=data.name,
        category=category,
        functional_unit=row["功能单位"],
    )


@router.post(
    "generate-life-circle/models/{id}",
    summary="BOM 生成模型生命周期",
    operation_id="generateModelLifeCycleByAI",
)
def generate_model_by_ai(request, id: int, data: FileAISchema):
    model = Model.objects.get(pk=id)
    file = File.objects.get(pk=data.file_id)
    return AI.generate_life_cycle_by_bom(model, file)


@router.get(
    "generate-analysis-report/models/{id}",
    summary="生成评估报告",
    operation_id="generateAnalysisReport",
)
def generate_analysis_report(request, id: int):
    model = Model.objects.get(pk=id)
    response = StreamingHttpResponse(AI.generate_analysis_report(model), content_type="text/event-stream")
    response["Cache-Control"] = "no-cache"
    return response


@router.post(
    "generate-goal-scope",
    summary="目标与范围提取",
    response=GoalScopeSchema,
    operation_id="generateGoalSndScope",
)
def generate_goal_scope_by_ai(request, data: FileAISchema):
    file = File.objects.get(pk=data.file_id)
    d = AI.generate_goal_scope(file)
    return d


@router.post(
    "input-quality-control/{id}",
    summary="输入质量控制",
    response=str,
    operation_id="inputQualityControlForModel",
)
@successful()
def input_quality_control_for_model(request, id: int):
    model = Model.objects.get(pk=id)
    return AI.input_quality_control(model)


@router.post(
    "craft-quality-control/{id}",
    summary="工艺质量控制",
    response=str,
    operation_id="craftQualityControlForModel",
)
@successful()
def craft_quality_control_for_model(request, id: int):
    model = Model.objects.get(pk=id)
    return AI.craft_quality_control(model)


@router.post(
    "distribution-quality-control/{id}",
    summary="分销质量控制",
    response=str,
    operation_id="distributionQualityControlForModel",
)
@successful()
def distribution_quality_control_for_model(request, id: int):
    model = Model.objects.get(pk=id)
    return AI.distribution_quality_control(model)
