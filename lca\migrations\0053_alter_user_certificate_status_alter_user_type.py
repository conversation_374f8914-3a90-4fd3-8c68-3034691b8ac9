# Generated by Django 5.2.1 on 2025-07-03 05:59

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0052_alter_user_phone"),
    ]

    operations = [
        migrations.AlterField(
            model_name="user",
            name="certificate_status",
            field=models.CharField(
                blank=True,
                choices=[
                    ("none", "未认证"),
                    ("ongoing", "认证中"),
                    ("approved", "已通过"),
                    ("rejected", "未通过"),
                ],
                db_index=True,
                default="ongoing",
                max_length=20,
                null=True,
                verbose_name="认证状态",
            ),
        ),
        migrations.AlterField(
            model_name="user",
            name="type",
            field=models.CharField(
                blank=True,
                choices=[
                    ("manufacturer", "企业"),
                    ("authority", "认证机构"),
                    ("government", "政府"),
                ],
                db_index=True,
                max_length=20,
                null=True,
                verbose_name="用户类型",
            ),
        ),
    ]
