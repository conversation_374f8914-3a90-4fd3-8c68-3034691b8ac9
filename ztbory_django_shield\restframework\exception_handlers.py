from django.http import JsonResponse

from ztbory_django_shield.core.exceptions import ShieldError

from rest_framework.views import exception_handler


def shield_error_handler(exc, context):
    """自定义错误处理"""
    if isinstance(exc, ShieldError):
        return JsonResponse(
            {"detail": exc.detail, "error_code": exc.err_code},
            status=exc.http_status_code,
        )

    return exception_handler(exc, context)
