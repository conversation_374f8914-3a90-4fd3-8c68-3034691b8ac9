import functools

from ztbory_django_shield.base.core import Shield
from ztbory_django_shield.core.schemas import ShieldLoginSchema
from ztbory_django_shield.core.utils import get_client_ip


def shield_login_decorator(with_class_self=True, with_cryptor=False):
    """双因子登录装饰器

    with_cryptor: True
        用户名会进行sm2解密后进行检测

    示例-类视图：
        class ShieldAuthToken(APIView):

            @shield_login_decorator(with_class_self=True)
            def post(self, request, *args, **kwargs):
                your_login_logic

    示例-方法视图：
        @shield_login_decorator(with_class_self=False)
        def login(request, *args, **kwargs):
            your_login_logic

    应用先决条件:
        1. 表单使用form-data提交数据，必须包含username, captcha_uuid, captcha 三个字段
        2. your_login_logic, 登录成功返回接口返回内容， 登录失败返回None
    """

    def decorator(func):
        @functools.wraps(func)
        def class_wrapper(self, request, *args, **kwargs):
            login = request.data.copy()
            data = ShieldLoginSchema(
                username=login.get("username", None),
                ip=get_client_ip(request),
                captcha_uuid=login.get("captcha_uuid", None),
                captcha=login.get("captcha", None),
            )
            if with_cryptor:
                data.username = Shield.decrypt(data.username)

            return Shield.login(
                data=data,
                login_func=func,
                login_func_args=((self, request, *args), kwargs),
            )

        @functools.wraps(func)
        def func_wrapper(request, *args, **kwargs):
            login = request.data.copy()
            data = ShieldLoginSchema(
                username=login.get("username", None),
                ip=get_client_ip(request),
                captcha_uuid=login.get("captcha_uuid", None),
                captcha=login.get("captcha", None),
            )
            if with_cryptor:
                data.username = Shield.decrypt(data.username)

            return Shield.login(data=data, login_func=func, login_func_args=((request, *args), kwargs))

        if with_class_self:
            return class_wrapper
        else:
            return func_wrapper

    return decorator
