import datetime
import os
from email.utils import formataddr

from django.conf import settings
from django.core.cache import cache
from django.core.mail import send_mail
from django.template import Template, Context

from ztbory_django_shield.core.email.options import Options
from ztbory_django_shield.core.exceptions import (
    EmailTokenNotFoundError,
    EmailSendFailError,
)
from ztbory_django_shield.core.utils import random_uuid, seconds_to_tips


class EmailForgetPassword:
    def __init__(self, username, email):
        self.username = username
        self.email = email


class EmailShield:
    def __init__(self, options: Options = Options()):
        self.options = options

    def _gen_cache_key_(self, uuid: str) -> str:
        """生成邮箱重置密码缓存key"""
        return f"Shield:EmailResetPassword:{uuid}"

    def _gen_email_content_(
        self,
        username: str,
        email: str,
        token: str,
        logo_url: str = None,
        reset_password_url: str = None,
    ) -> str:
        """生成邮件内容"""
        if logo_url is None:
            logo_url = self.options.logo_url
        if reset_password_url is None:
            reset_password_url = self.options.reset_password_url
        elif "?" in reset_password_url:
            reset_password_url = reset_password_url + "&token={token}"
        else:
            reset_password_url = reset_password_url + "?token={token}"

        with open(
            os.path.join(os.path.dirname(__file__), "templates", "forget-password.html"),
            encoding="utf-8",
        ) as f:
            template = Template(f.read())
            content = template.render(
                context=Context(
                    {
                        "logo_url": logo_url,
                        "username": username,
                        "email": email,
                        "reset_password_url": reset_password_url.format(token=token),
                        "expire_duration": seconds_to_tips(self.options.expire_in_seconds),
                        "now": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    }
                )
            )
            return content

    def send_forget_password_email(
        self,
        username: str,
        email: str,
        logo_url: str = None,
        reset_password_url: str = None,
    ) -> int:
        """发送忘记密码邮件"""
        token = random_uuid()
        cache.set(
            self._gen_cache_key_(token),
            dict(username=username, email=email),
            self.options.expire_in_seconds,
        )

        success_count = send_mail(
            subject="找回密码",
            message="找回密码",
            from_email=formataddr((getattr(settings, "EMAIL_SENDER_NAME", None), settings.EMAIL_HOST_USER)),
            recipient_list=[email],
            html_message=self._gen_email_content_(username, email, token, logo_url, reset_password_url),
            fail_silently=True,
        )
        if success_count == 0:
            raise EmailSendFailError()
        return self.options.expire_in_seconds

    def verify_token_expiration(self, token: str) -> int:
        """校验找回密码令牌有效性，并返回有效期(s)"""
        cache_value = cache.get(self._gen_cache_key_(token))
        if not cache_value:
            raise EmailTokenNotFoundError()
        return cache.ttl(token)

    def gain_token_info(self, token: str) -> EmailForgetPassword:
        """根据找回密码令牌获取用户名 及 邮箱"""
        cache_value = cache.get(self._gen_cache_key_(token))
        if not cache_value:
            raise EmailTokenNotFoundError()

        cache.delete(self._gen_cache_key_(token))
        return EmailForgetPassword(username=cache_value["username"], email=cache_value["email"])
