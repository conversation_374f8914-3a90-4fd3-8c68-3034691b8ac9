from django.core.cache import cache

from ztbory_django_shield.core.exceptions import (
    PasswordBanedError,
    PasswordLoginFailError,
)
from ztbory_django_shield.core.password.options import Options
from ztbory_django_shield.core.password.utils import (
    validate_regex,
    validate_continuous,
    validate_contains,
)
from ztbory_django_shield.core.utils import seconds_to_tips


class PasswordShield:
    def __init__(self, options: Options = Options()):
        self.options = options
        self.cache = cache

    def is_password_safely(self, username: str, password: str, is_raise_exception: bool = False) -> bool:
        """返回密码是否符合要求"""
        regex_valid = validate_regex(password, self.options.complex_regex)
        if is_raise_exception and not regex_valid:
            raise PasswordBanedError(detail=self.options.complex_tips)

        continuous_valid = validate_continuous(password)
        if is_raise_exception and not continuous_valid:
            raise PasswordBanedError(detail="密码中不应出现连续数字/字母, 请重设密码。")

        username_valid = validate_contains(password, username)
        if is_raise_exception and not username_valid:
            raise PasswordBanedError(
                detail=f"密码中不应包含用户名。请重新设置一个安全的密码，{self.options.complex_tips}"
            )

        return all([regex_valid, continuous_valid, username_valid])

    def valid_password(self, username: str, password: str):
        """校验密码是否符合要求"""
        self.is_password_safely(username, password, is_raise_exception=True)

    def _gen_cache_key_(self, username: str, ip: str) -> str:
        if self.options.incorrect_ban_with_ip:
            return f"Shield:PasswordIncorrect:{username}@{ip}"
        return f"Shield:PasswordIncorrect:{username}"

    def clean_incorrect_username(self, username: str):
        self.cache.delete_pattern(f"Shield:PasswordIncorrect:{username}*")

    def gain_is_need_two_factor(self, username: str, ip: str) -> bool:
        """判断是否需要进行双因子校验
        True: 需要双因子校验
        False: 不需要双因子校验
        """
        incorrect_times = self.cache.get(self._gen_cache_key_(username, ip)) or 0
        incorrect_trigger_two_factor_times = self.options.incorrect_trigger_two_factor_times
        incorrect_trigger_tow_factor_managers = self.options.incorrect_trigger_tow_factor_managers
        if username in incorrect_trigger_tow_factor_managers:
            return True
        if incorrect_trigger_two_factor_times >= 0:
            return incorrect_times >= incorrect_trigger_two_factor_times
        return False

    def do_before_login(self, username: str, ip: str):
        """登录前验证是否允许登录"""

        incorrect_times = self.cache.get(self._gen_cache_key_(username, ip)) or 0
        if incorrect_times >= self.options.incorrect_max_times:
            raise PasswordBanedError(
                detail=self.options.incorrect_baned_tips_template.format(
                    incorrect_ban_duration=seconds_to_tips(self.options.incorrect_ban_seconds)
                )
            )

    def do_after_login(self, username: str, ip: str, login_result: bool):
        """登录后记录登录结果"""

        cache_key = self._gen_cache_key_(username, ip)
        incorrect_times = self.cache.get(cache_key)
        if login_result and incorrect_times:
            self.cache.delete(cache_key)
        if not login_result:
            incorrect_times = (incorrect_times or 0) + 1
            self.cache.set(cache_key, incorrect_times, self.options.incorrect_ban_seconds)
        if not login_result:
            raise PasswordLoginFailError(
                detail=self.options.incorrect_tips_template.format(
                    incorrect_max_times=self.options.incorrect_max_times,
                    incorrect_times=incorrect_times,
                    incorrect_ban_duration=seconds_to_tips(self.options.incorrect_ban_seconds),
                )
            )
