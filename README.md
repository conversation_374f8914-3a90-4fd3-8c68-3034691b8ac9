# LCA 碳足迹管理系统

## 项目简介

LCA（Life Cycle Assessment）碳足迹管理系统是一个基于 Django 的企业级碳核算与认证管理平台。系统提供完整的产品生命周期碳排放评估、碳认证管理、AI 辅助分析等功能，帮助企业进行碳足迹管理和碳中和目标实现。

## 核心功能

### 🧮 碳核算模块 (lca.accounting)
- **模型管理**: 创建和管理产品碳足迹评估模型
- **生命周期评估**: 支持从原料获取到废物处理的全生命周期评估
- **过程管理**: 管理生产过程中的各个环节和工艺流程
- **输入输出管理**: 记录和计算各阶段的物料输入和排放输出
- **碳排放计算**: 自动计算 CO2 当量排放量
- **分配方法**: 支持多种分配方法进行碳排放分摊

### 🏆 碳认证模块 (lca.certification)
- **认证机构管理**: 管理认证机构信息和产品认证
- **制造商认证**: 企业碳认证申请和管理
- **认证产品管理**: 已认证产品的展示和查询
- **认证流程**: 完整的认证申请、审核、发证流程

### 👥 用户管理模块 (lca.users)
- **用户注册登录**: 支持手机号验证码登录
- **权限管理**: 基于角色的权限控制系统
- **用户认证**: 用户身份认证和企业认证
- **短信验证**: 集成阿里云短信服务

### 🤖 AI 辅助模块 (lca.ai)
- **BOM 表解析**: AI 自动解析 BOM 表生成生命周期模型
- **质量控制**: 输入、工艺、分销环节的 AI 质量控制
- **报告生成**: AI 自动生成碳足迹评估报告
- **目标范围分析**: AI 辅助确定评估目标和范围

### 📊 数据库模块 (lca.database)
- **排放因子库**: 标准化的排放因子数据库
- **流管理**: 基本流、中间流、产品流等流类型管理
- **数据集管理**: 排放源数据集的维护和查询

### 📁 文件管理模块 (lca.file)
- **文件上传**: 支持多种文件格式上传
- **文档管理**: 生产报告、BOM 表、采购合同等文档管理
- **MinIO 集成**: 基于 MinIO 的对象存储服务

### 🔧 通用模块 (lca.common)
- **地理位置**: 地区信息管理
- **分类服务**: 产品分类和层级管理
- **短信服务**: 阿里云短信集成

## 技术架构

### 后端技术栈
- **框架**: Django 5.2+
- **API**: Django Ninja (类似 FastAPI 的高性能 API 框架)
- **数据库**: PostgreSQL (生产环境) / SQLite (测试环境)
- **缓存**: Redis (会话存储和缓存)
- **对象存储**: MinIO
- **任务队列**: 支持异步任务处理
- **认证**: 自定义认证后端 + 滑块验证

### 核心依赖
- `django-ninja`: 高性能 API 框架
- `django-cors-headers`: 跨域请求支持
- `django-redis`: Redis 缓存集成
- `psycopg2`: PostgreSQL 数据库驱动
- `minio`: 对象存储客户端
- `pandas`: 数据处理和分析
- `requests`: HTTP 请求库
- `alibabacloud-dysmsapi20170525`: 阿里云短信服务

### 安全特性
- **SM2 加密**: 国密 SM2 算法支持
- **滑块验证**: 防机器人验证
- **权限控制**: 基于角色的访问控制
- **数据加密**: 敏感数据加密存储

## 项目结构

```
lca/
├── config/                 # Django 配置
│   ├── settings.py        # 项目设置
│   ├── urls.py           # 路由配置
│   └── wsgi.py           # WSGI 配置
├── lca/                   # 主应用
│   ├── accounting/        # 碳核算模块
│   ├── certification/     # 碳认证模块
│   ├── users/            # 用户管理模块
│   ├── ai/               # AI 辅助模块
│   ├── database/         # 数据库模块
│   ├── file/             # 文件管理模块
│   ├── common/           # 通用模块
│   └── admin/            # 管理后台
├── infra/                # 基础设施
│   ├── authentication.py # 认证后端
│   ├── permissions.py    # 权限控制
│   ├── pagination.py     # 分页组件
│   └── utils.py          # 工具函数
├── ztbory_django_shield/ # 安全防护组件
├── templates/            # 模板文件
├── static/              # 静态文件
├── tests/               # 测试文件
├── manage.py            # Django 管理脚本
├── requirements.txt     # 依赖列表
├── pyproject.toml       # 项目配置
├── Dockerfile           # Docker 配置
└── gunicorn.conf.py     # Gunicorn 配置
```

## 环境要求

- Python 3.12+
- PostgreSQL 12+
- Redis 6+
- MinIO (对象存储)

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd lca

# 创建虚拟环境
uv sync
.venv\Scripts\activate

```

### 2. 环境配置

创建 `.env` 文件并配置以下环境变量：

```env
# Django 配置
SECRET_KEY=your-secret-key
DEBUG=True

# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=your-password

# Redis 配置
REDIS_SERVICE_HOST=localhost
REDIS_SERVICE_PORT=6379
REDIS_PASSWORD=your-redis-password

# MinIO 配置
OSS_ENDPOINT=localhost:9000
OSS_BUCKET=lca-bucket
OSS_ACCESS_KEY_ID=your-access-key
OSS_SECRET_ACCESS_KEY=your-secret-key
OSS_PREFIX=lca/
OSS_INTERNAL_PREFIX=internal/

# 阿里云短信配置
ALIBABA_CLOUD_SMS_CODE_TEMPLATE=SMS_template_id

# AI 服务配置 (Dify)
DIFY_API_URL=https://api.dify.ai/v1
DIFY_API_KEY=your-dify-api-key
DIFY_BOM_API_KEY=your-bom-api-key
DIFY_INPUT_CONTROLL_API_KEY=your-input-control-key
DIFY_CRAFT_CONTROLL_API_KEY=your-craft-control-key
DIFY_DISTRIBUTION_CONTROLL_API_KEY=your-distribution-control-key
DIFY_GOAL_SCOPE_API_KEY=your-goal-scope-key
DIFY_ANALYSIS_REPORT_API_KEY=your-analysis-report-key
```

### 3. 数据库初始化

```bash
# 数据库迁移
python manage.py makemigrations
python manage.py migrate

# 创建超级用户
python manage.py createsuperuser
```

### 4. 运行项目

#### 开发环境
```bash
python manage.py runserver
```

#### 生产环境
```bash
# 使用 Gunicorn
gunicorn
```

### 5. 国际化和多语言支持
1. 设置`LOCALE_PATHS`，语言包存放在 `lca/locale` 目录下，设置`LANGUAGE_CODE`为默认语言
2. 配置文件中添加了中间件
    ```python
    MIDDLEWARE = [
        'django.middleware.locale.LocaleMiddleware',
    ]
    ```
3. 使用 `django-admin makemessages` 命令生成语言包，使用 `django-admin compilemessages` 命令编译语言包
    ```bash
    python manage.py makemessages -l en
    # 或者
    python manage.py makemessages -l en -e py --ignore=.venv/*
    
    python manage.py compilemessages
    ```
4. 前端传递语言
   1. Headers 里面增加 `Accept-Language`，例如`Accept-Language: en`、`Accept-Language: zh`
   2. 通过 URL 路由传语言。例如`/zh/account/`、`/en/account/`、`/zh-hans/account/`，需要配置`i18n_patterns`
   3. 通过 Cookie 传语言。例如 cookie 设置为 `django_language=en; path=/`、`django_language=zh; path=/`

## API 模块说明

### 用户模块 (/user)
- `POST /user/login` - 用户登录
- `POST /user/register` - 用户注册
- `POST /user/send-sms-code` - 发送短信验证码
- `POST /user/logout` - 用户登出

### 碳核算模块 (/accounting)
- `GET /accounting/models` - 获取模型列表
- `POST /accounting/models` - 创建新模型
- `GET /accounting/models/{id}` - 获取模型详情
- `POST /accounting/processes` - 创建过程
- `POST /accounting/processes/{id}/calculate` - 运行碳排放计算

### 碳认证模块 (/certification)
- `GET /certification` - 认证首页数据
- `GET /certification/authority-admin` - 认证机构管理
- `GET /certification/manufacturer-admin` - 制造商管理

### AI 模块 (/ai)
- `POST /ai/generate-life-circle/models/{id}` - AI 生成生命周期
- `GET /ai/generate-analysis-report/models/{id}` - 生成评估报告

### 数据库模块 (/database)
- 排放因子数据查询和管理

### 文件模块 (/file)
- 文件上传和管理服务
