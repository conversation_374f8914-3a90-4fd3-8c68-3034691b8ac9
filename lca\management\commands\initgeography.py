import csv
from django.core.management import BaseCommand
from lca.accounting.models import Geography


class Command(BaseCommand):
    help = "初始化地理区域表"

    def handle(self, *args, **options):
        initial_geography()


def initial_geography():
    with open("lca/management/commands/data/geography.csv") as file:
        reader = csv.DictReader(file)
        for row in reader:
            print(row["name"])
            if row["parent"]:
                parent_id = row["parent"]
            else:
                parent_id = None
            object = Geography(
                id=row["short_name"],
                name=row["name"],
                parent_id=parent_id,
                coord=f"{row['longitude']},{row['latitude']}" if row["longitude"] and row["latitude"] else None,
                classification=row["classification"],
            )
            object.save()
