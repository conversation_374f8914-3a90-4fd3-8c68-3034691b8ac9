from pydantic import GetCoreSchemaHandler
from pydantic_core import core_schema
import re
from ninja.errors import HttpError


class StrType(str):
    @classmethod
    def __get_pydantic_core_schema__(cls, source_type, handler: GetCoreSchemaHandler):
        return core_schema.no_info_after_validator_function(cls.validate, core_schema.str_schema())

    @classmethod
    def validate(cls, value: str) -> str:
        pass


class Phone(StrType):
    """手机号"""

    @classmethod
    def validate(cls, value: str) -> str:
        phone_regex = re.compile(r"^1[3-9]\d{9}$")  # 中国大陆手机号码格式
        if not isinstance(value, str) or not phone_regex.match(value):
            raise HttpError(422, "Invalid phone number format")
        return value


class Code(StrType):
    """验证码"""

    @classmethod
    def validate(cls, value):
        phone_regex = re.compile(r"^\d{6}$")
        if not isinstance(value, str) or not phone_regex.match(value):
            raise HttpError(422, "Invalid code number format")
        return value
