# Generated by Django 5.2.1 on 2025-07-24 08:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('lca', '0099_alter_emissionsourcedataset_source_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='emissionsourceapplication',
            name='source',
            field=models.JSONField(max_length=255, verbose_name='数据来源'),
        ),
        migrations.AlterField(
            model_name='emissionsourcemanagementapplication',
            name='copyright',
            field=models.JSONField(max_length=255, null=True, verbose_name='版权信息'),
        ),
        migrations.AlterField(
            model_name='emissionsourcemanagementapplication',
            name='generate_contact',
            field=models.JSONField(max_length=255, verbose_name='数据生成联系人'),
        ),
        migrations.AlterField(
            model_name='emissionsourcemanagementapplication',
            name='inputer_contact',
            field=models.JSONField(blank=True, max_length=255, null=True, verbose_name='数据录入者'),
        ),
        migrations.AlterField(
            model_name='emissionsourcemanagementapplication',
            name='license_type',
            field=models.JSONField(max_length=255, null=True, verbose_name='许可类型'),
        ),
        migrations.AlterField(
            model_name='emissionsourcemanagementapplication',
            name='owener',
            field=models.JSONField(blank=True, max_length=255, null=True, verbose_name='数据拥有者'),
        ),
        migrations.AlterField(
            model_name='emissionsourcemanagementapplication',
            name='publish',
            field=models.JSONField(max_length=255, null=True, verbose_name='发布信息'),
        ),
        migrations.AlterField(
            model_name='emissionsourcemanagementapplication',
            name='view',
            field=models.JSONField(max_length=255, null=True, verbose_name='访问权限信息'),
        ),
    ]
