from django.core.management import BaseCommand
from lca.accounting.models import CERTIFICATE_PRODUCT
from lca.common.models import Region
import pandas as pd
from django.db import transaction
from lca.certification.models import Authority, AuthorityProduct, RegionProduct


class Command(BaseCommand):
    help = "初始化认证公司"

    def handle(self, *args, **options):
        run()


def run():
    df = pd.read_csv("lca/management/commands/data/authority.csv")
    with transaction.atomic():
        for index, row in df.iterrows():
            authority = get_authority(row)
            AuthorityProduct.objects.filter(authority=authority).delete()
            for product_name in row["试点产品"].split("、"):
                product = product_name_to_code(product_name)
                AuthorityProduct.objects.create(authority=authority, product=product)
        df = pd.read_csv("lca/management/commands/data/region-product.csv")
        RegionProduct.objects.filter(product=product).delete()
        for index, row in df.iterrows():
            product = product_name_to_code(row["产品"])
            for region_name in row["可申请认证地区"].split("、"):
                region = get_region(region_name)
                print(authority, product, region)
                RegionProduct.objects.create(region=region, product=product)


def product_name_to_code(product_name):
    for code, name in CERTIFICATE_PRODUCT.choices:
        if name == product_name:
            return code
    raise ValueError(f"product_name {product_name} not found")


def get_region(region_name):
    return Region.objects.get(name=region_name)


def get_authority(row):
    try:
        authority = Authority.objects.get(code=row["认证公司代码"])
    except Authority.DoesNotExist:
        authority = Authority.objects.create(
            code=row["认证公司代码"],
            name=row["认证公司"],
            address="暂未提供",
            description=row["认证公司"],
        )
    return authority
