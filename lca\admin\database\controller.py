from ninja import Query, Router
from infra.PageNumberPagination import PageNumberPagination
from infra.decorators import permission_required
from ninja.pagination import paginate
from infra.filter import filter
from lca.accounting.schema import EmmissionSourceSchema, FlowSchema
from lca.database.models import DATABASE_IMPORT_STATUS, EMISSION_SOURCE_STATUS, FLOW_TYPES, Database, DatabaseImportTask, EmissionSourceApplication
from lca.database.schema import (
    DatabaseImportTaskSchema,
    DatabaseInSchema,
    DatabaseSchema,
    EmissionSourceAdminRowSchema,
    EmissionSourceDetailAdminSchema,
    EmissionSourceInSchema,
    EmissionSourceRejectInSchema,
    EmmissionSourceDetailSchema,
    FlowInSchema,
)
from lca.database.services import DatabaseService
from lca.file.models import File
from lca.file.schema import FileOut

router = Router(tags=["后台-数据库"])


@router.get("/", summary="获取数据库列表", response=list[DatabaseSchema], operation_id="adminGetDatabaseList")
@paginate(PageNumberPagination)
@permission_required("DataManage:ThirdDB")
def get_database_list(
    request,
    name: str = Query(None, title="名称"),
):
    return filter(
        Database.objects.order_by("-create_time"),
        status=DATABASE_IMPORT_STATUS.SUCCESS,
        name__icontains=name,
    )


@router.post("/", summary="创建数据库", response=DatabaseImportTaskSchema, operation_id="adminCreateDatabase")
@permission_required("DataManage:ThirdDB:ImportDB")
def create_database(request, data: DatabaseInSchema):
    file = File.objects.get(pk=data.file_id)
    return DatabaseService.import_database(data.name, file)


@router.post("/{id}/export", summary="导出数据库", response=FileOut, operation_id="adminExportDatabase")
@permission_required("DataManage:ThirdDB:Export")
def export_database(request, id: int):
    database = Database.objects.get(pk=id)
    return DatabaseService.export_database(database)


@router.get(
    "/task",
    summary="获取导入任务列表",
    response=list[DatabaseImportTaskSchema],
    operation_id="adminGetDatabaseTaskList",
)
@paginate(PageNumberPagination)
@permission_required("DataManage:ThirdDB:TaskList")
def get_database_task_list(request):
    return filter(
        DatabaseImportTask.objects.order_by("-create_time"),
    )


@router.get(
    "emission-source",
    summary="获取本地因子列表",
    response=list[EmissionSourceAdminRowSchema],
    operation_id="adminGetEmissionSourceList",
)
@paginate(PageNumberPagination)
@permission_required("DataManage:LocalFactor")
def get_emission_source_list(
    request,
    name: str = Query(None, title="名称"),
    year: int = Query(None, title="年份"),
    geography_id: str = Query(None, title="地理ID"),
):
    return filter(
        EmissionSourceApplication.objects.order_by("-create_time"),
        name__icontains=name,
        year=year,
        geography_id=geography_id,
        # status=EMISSION_SOURCE_STATUS.APPROVED,
    )


@router.get(
    "emission-source/{id}",
    summary="获取本地因子详情",
    response=EmissionSourceDetailAdminSchema,
    operation_id="adminGetEmissionSource",
)
@permission_required("DataManage:LocalFactor:View")
def get_emission_source(request, id: int):
    item = EmissionSourceApplication.objects.get(pk=id)
    print(item.inputs.all())
    return item


@router.post(
    "emission-source",
    summary="创建本地因子",
    response=EmissionSourceAdminRowSchema,
    operation_id="adminCreateEmissionSource",
)
@permission_required("DataManage:LocalFactor:Upload")
def create_emission_source(request, data: EmissionSourceInSchema):
    return DatabaseService.create_emission_source_application(request.user, data)


@router.post("emission-source/{id}/delete", summary="删除本地因子", operation_id="adminDeleteEmissionSource")
@permission_required("DataManage:LocalFactor:Delete")
def delete_emission_source(request, id: int):
    pass


@router.post(
    "emission-source/{id}/update",
    summary="更新本地因子",
    response=EmmissionSourceDetailSchema,
    operation_id="adminUpdateEmissionSource",
)
@permission_required("DataManage:LocalFactor:Edit")
def update_emission_source(request, id: int, data: EmissionSourceInSchema):
    pass


@router.get(
    "approval",
    summary="获取待审批因子列表",
    response=list[EmissionSourceAdminRowSchema],
    operation_id="adminGetApprovalEmissionSourceList",
)
@paginate(PageNumberPagination)
@permission_required("DataReview:Approve")
def get_approval_emission_source_list(request):
    pass


@router.get(
    "approval/logs",
    summary="获取审批因子日志列表",
    response=list[EmissionSourceAdminRowSchema],
    operation_id="adminGetApprovalEmissionSourceLogs",
)
@paginate(PageNumberPagination)
@permission_required("DataReview:Record")
def get_approval_emission_source_logs(request):
    pass


@router.get(
    "approval/{id}",
    summary="获取审批因子详情",
    response=EmissionSourceDetailAdminSchema,
    operation_id="adminGetApprovalEmissionSourceDetail",
)
@permission_required("DataReview:Approve")
def get_approval_emission_source_detail(request, id: int):
    pass


@router.post(
    "approval/{id}/accept",
    summary="通过审批因子",
    response=EmissionSourceDetailAdminSchema,
    operation_id="adminAcceptApprovalEmissionSource",
)
@permission_required("DataReview:Approve")
def accept_approval_emission_source(request, id: int):
    pass


@router.post(
    "approval/{id}/reject",
    summary="拒绝审批因子",
    response=EmissionSourceDetailAdminSchema,
    operation_id="adminRejectApprovalEmissionSource",
)
@permission_required("DataReview:Approve")
def reject_approval_emission_source(request, id: int, data: EmissionSourceRejectInSchema):
    pass


@router.post("flow", summary="创建流", response=FlowSchema, operation_id="adminCreateFlow")
@permission_required("DataManage:LocalFactor")
def create_flow(request, data: FlowInSchema):
    pass


@router.get("flow", summary="获取流列表", response=list[FlowSchema], operation_id="adminGetFlowList")
@paginate(PageNumberPagination)
@permission_required("DataManage:LocalFactor")
def get_flow_list(request, type: FLOW_TYPES = Query(None, title="流类型"), name: str = Query(None, title="名称")):
    pass
