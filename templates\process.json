[
  {
    "processDataSet": {
      "@xmlns": "http://lca.jrc.it/ILCD/Process",
      "@version": "1.1",
      "exchanges": {
        "exchange": [
          {% for input in inputs %}
          {
            "meanAmount": "{{input.amount}}",
            "generalComment": [
              {
                "#text": "{{input.flow.name}}",
                "@xml:lang": "zh"
              }
            ],
            "resultingAmount": "{{input.amount}}",
            "exchangeDirection": "Input",
            "@dataSetInternalID": "0",
            "referenceToFlowDataSet": {
              "@uri": "../flows/{{input.flow.uuid}}.xml",
              "@type": "flow data set",
              "@refObjectId": "{{input.flow.uuid}}",
              "common:shortDescription": {
                "#text": "{{input.flow.name}}",
                "@xml:lang": "zh"
              }
            },
            "dataDerivationTypeStatus": "Missing unimportant"
          },
          {% endfor %}
          {% for input in outputs %}
          {
            "meanAmount": "{{input.amount}}",
            "generalComment": [
              {
                "#text": "{{input.flow.name}}",
                "@xml:lang": "zh"
              }
            ],
            "resultingAmount": "{{input.amount}}",
            "exchangeDirection": "Input",
            "@dataSetInternalID": "0",
            "referenceToFlowDataSet": {
              "@uri": "../flows/{{input.flow.uuid}}.xml",
              "@type": "flow data set",
              "@refObjectId": "{{input.flow.uuid}}",
              "common:shortDescription": {
                "#text": "{{input.flow.name}}",
                "@xml:lang": "zh"
              }
            },
            "dataDerivationTypeStatus": "Missing unimportant"
          }{% if not forloop.last %},{% endif %}
          {% endfor %}
        ]
      },
      "@locations": "../ILCDLocations.xml",
      "@xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance",
      "@xmlns:common": "http://lca.jrc.it/ILCD/Common",
      "processInformation": {
        "geography": {
          "locationOfOperationSupplyOrProduction": {
            "@location": "{{dataset.geography_id}}"
          }
        },
        "technology": {
          "technologicalApplicability": [
            {
              "#text": "{{dataset.usage}}",
              "@xml:lang": "zh"
            }
          ]
        },
        "dataSetInformation": {
          "name": {
            "baseName": [
              {
                "#text": "{{dataset.name}}",
                "@xml:lang": "zh"
              }
            ]{% comment %},
            "mixAndLocationTypes": [
              {
                "#text": "生产混合，在工厂",
                "@xml:lang": "zh"
              }
            ],
            "treatmentStandardsRoutes": [
              {
                "#text": "Cutting, polishing, and shaping",
                "@xml:lang": "en"
              },
              {
                "#text": "切割、打磨和成型",
                "@xml:lang": "zh"
              }{% endcomment %}
            ]
          },
          "common:UUID": "{{process.uuid}}",
          "common:generalComment": [
            {
              "#text": "{{dataset.technical_description}}",
              "@xml:lang": "zh"
            }
          ],
          "classificationInformation": {
            "common:classification": {
              "common:class": [
                {% for cat in cats %}
                {
                  "#text": "{{cat.name}}",
                  "@level": "{{forloop.counter0}}",
                  "@classId": "{{cat.id}}"
                }{% if not forloop.last %},{% endif %}
                {% endfor %}
              ]
            }
          }
        },
        "quantitativeReference": {
          "@type": "Reference flow(s)",
          "referenceToReferenceFlow": "0"
        }
      },
      "@xsi:schemaLocation": "http://lca.jrc.it/ILCD/Process ../../schemas/ILCD_ProcessDataSet.xsd",
      "modellingAndValidation": {
        "validation": {
          "review": {
            "@type": "Dependent internal review",
            "common:reviewDetails": [
              {
                "#text": "清单： 内部审查通过几个迭代步骤完成，涉及原始数据验证、原始数据记录、代表性、完整性、与 ISO 14040 和 14044 有关的建模的一致性。",
                "@xml:lang": "zh"
              }
            ],
            "common:referenceToNameOfReviewerAndInstitution": {
              "@uri": "../contacts/f4b4c314-8c4c-4c83-968f-5b3c7724f6a8.xml",
              "@type": "contact data set",
              "@refObjectId": "f4b4c314-8c4c-4c83-968f-5b3c7724f6a8",
              "common:shortDescription": [
                {
                  "#text": "{{management.approve_contact}}",
                  "@xml:lang": "zh"
                }
              ]
            }
          }
        },
        "LCIMethodAndAllocation": {
          "typeOfDataSet": "Unit process, single operation",
          "deviationsFromLCIMethodPrinciple": [
            {
              "#text": "None",
              "@xml:lang": "en"
            },
            {
              "#text": "无",
              "@xml:lang": "zh"
            }
          ],
          "deviationsFromModellingConstants": [
            {
              "#text": "None",
              "@xml:lang": "en"
            },
            {
              "#text": "无",
              "@xml:lang": "zh"
            }
          ]
        },
        "dataSourcesTreatmentAndRepresentativeness": {
          "referenceToDataSource": {
            "@uri": "../sources/d389b7ba-1e37-441e-8d51-b0a90223359f.xml",
            "@type": "source data set",
            "@refObjectId": "d389b7ba-1e37-441e-8d51-b0a90223359f",
            "common:shortDescription": [
              {
                "#text": "{{dataset.data_treatment}}",
                "@xml:lang": "zh"
              }
            ]
          }
        }
      },
      "administrativeInformation": {
        "dataEntryBy": {
          "common:timeStamp": "{{create_time}}"
        },
        "dataGenerator": {
          "common:referenceToPersonOrEntityGeneratingTheDataSet": {
            "@uri": "../contacts/5b7a579f-14f3-4823-9068-a74efb5b1c79.xml",
            "@type": "contact data set",
            "@version": "01.00.000",
            "@refObjectId": "5b7a579f-14f3-4823-9068-a74efb5b1c79",
            "common:shortDescription": [
              {
                "#text": "{{management.generate_contact}}, {{management.generate_contact_detail}}",
                "@xml:lang": "zh"
              }
            ]
          }
        },
        "publicationAndOwnership": {
          "common:copyright": "{{management.copyright}}",
          "common:licenseType": "{{management.license_type}}",
          "common:dataSetVersion": "{{management.generate_version}}",
          "common:dateOfLastRevision": "{{last_update}}",
          "common:referenceToOwnershipOfDataSet": {
            "@uri": "../contacts/f4b4c314-8c4c-4c83-968f-5b3c7724f6a8.xml",
            "@type": "contact data set",
            "@refObjectId": "f4b4c314-8c4c-4c83-968f-5b3c7724f6a8",
            "common:shortDescription": [
              {
                "#text": "{{management.owener}}",
                "@xml:lang": "zh"
              }
            ]
          }
        }
      }
    }
  }
]