# Generated by Django 5.2.1 on 2025-06-05 07:35

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0041_flow_emissionsourcedataset_emissionsourcemanagement_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="emissionsourceinput",
            name="emission_source",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="einputs",
                to="lca.emissionsource",
                verbose_name="排放源",
            ),
        ),
        migrations.AlterField(
            model_name="emissionsourceinput",
            name="flow",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="lca.flow",
                verbose_name="流",
            ),
        ),
        migrations.CreateModel(
            name="EmissionSourceOutput",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(decimal_places=5, max_digits=20, verbose_name="数量"),
                ),
                (
                    "description",
                    models.CharField(max_length=255, null=True, verbose_name="描述"),
                ),
                (
                    "emission_source",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="eoutputs",
                        to="lca.emissionsource",
                        verbose_name="排放源",
                    ),
                ),
                (
                    "flow",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.flow",
                        verbose_name="流",
                    ),
                ),
                (
                    "unit",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.unit",
                        verbose_name="单位",
                    ),
                ),
            ],
        ),
        migrations.DeleteModel(
            name="EmmissionSourceInput",
        ),
    ]
