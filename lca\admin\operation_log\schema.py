from datetime import datetime

from ninja import Schema
from pydantic import Field

from lca.operation_log.models import OperationType


class OperationLogOutSchema(Schema):
    id: int = Field(..., title="日志ID")
    operation_type: OperationType = Field(..., title="操作类型")
    content: str = Field(..., title="操作内容")
    operation_time: datetime = Field(..., title="操作时间")

    operator_username: str | None = Field(None, title="操作人账号")
    operator_nickname: str | None = Field(None, title="账号用户名")
