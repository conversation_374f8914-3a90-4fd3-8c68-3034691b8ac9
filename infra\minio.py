import logging

from django.conf import settings
from minio import Minio, S3Error
from minio.commonconfig import CopySource
from ninja.errors import HttpError

logger = logging.getLogger("mino")


class MinioService:
    bucket_name = settings.MINIO_BUCKET

    def __init__(self, address, access_key, secret_key):
        # Http连接 将secure设置为False
        self.minioClient = Minio(endpoint=address, access_key=access_key, secret_key=secret_key, secure=False)

    def upload_file(self, file_name, file_path):
        """
        将文件上传到bucket
        :param file_name: 存放到minio桶中的文件名字(相当于对文件进行了重命名，可以与原文件名不同)
                            file_name处可以创建新的目录(文件夹) 例如 /example/file_name
                            相当于在该桶中新建了一个example文件夹 并把文件放在其中
        :param file_path: 本地文件的路径
        """
        try:
            self.minioClient.fput_object(bucket_name=self.bucket_name, object_name=file_name, file_path=file_path)
        except FileNotFoundError as err:
            logger.error("upload_failed: " + str(err))
            raise HttpError(400, "上传文件失败")
        except S3Error as err:
            logger.error("upload_failed:", err)
            raise HttpError(400, "上传文件失败")

    def put_object(self, object_name, stream: object, object_size: int):
        """
        将文件上传到bucket
        :param object_name: 存放到minio桶中的文件名字(相当于对文件进行了重命名，可以与原文件名不同)
                            file_name处可以创建新的目录(文件夹) 例如 /example/file_name
                            相当于在该桶中新建了一个example文件夹 并把文件放在其中
        :param stream: An object having callable read() returning bytes object.
        :param object_size: Data size; -1 for unknown size and set valid part_size.
        """
        try:
            self.minioClient.put_object(
                bucket_name=self.bucket_name,
                object_name=object_name,
                data=stream,
                length=object_size,
            )
        except FileNotFoundError as err:
            logger.error("put_object: " + str(err))
            raise HttpError(400, "上传文件失败")
        except S3Error as err:
            logger.error("put_object:", err)
            raise HttpError(400, "上传文件失败")

    def get_object(self, object_name):
        """
        从bucket下载文件
        :param object_name: 存放在minio桶中文件名字
                            object_name 处可以包含目录(文件夹) 例如 /example/file_name
        """
        try:
            response = self.minioClient.fget_object(bucket_name=self.bucket_name, object_name=object_name)
            # todo 获取数据流 返回数据流

        except FileNotFoundError as err:
            logger.error("download_failed: " + str(err))
            raise HttpError(400, "下载文件失败")
        except S3Error as err:
            logger.error("download_failed:", err)
            raise HttpError(400, "下载文件失败")
        finally:
            response.close()
            response.release_conn()

    def remove_object(self, object_name):
        """
        从bucket删除文件
        :param bucket_name: minio桶名称
        :param object_name: 存放在minio桶中的文件名字
                            object_name处可以包含目录(文件夹) 例如 /example/file_name
        """
        check_bucket = self.minioClient.bucket_exists(self.bucket_name)
        if check_bucket:
            try:
                self.minioClient.remove_object(bucket_name=self.bucket_name, object_name=object_name)
            except FileNotFoundError as err:
                logger.error("remove_failed: " + str(err))
            except S3Error as err:
                logger.error("remove_failed:", err)
                raise HttpError(400, "删除文件失败")

    def copy_object(self, source_object_name, destination_object_name):
        """
        从bucket复制文件
        """
        self.minioClient.copy_object(
            bucket_name=self.bucket_name,
            object_name=destination_object_name,
            source=CopySource(self.bucket_name, source_object_name),
        )


minio_service = MinioService(
    address=settings.MINIO_ENDPOINT,
    access_key=settings.MINIO_ACCESS_KEY,
    secret_key=settings.MINIO_SECRET_KEY,
)
