from django.contrib.auth.models import AbstractUser, AbstractBaseUser
from django.db import models
from lca.accounting.models import CERTIFICATE_PRODUCT
from lca.file.models import File
from django.contrib.postgres.fields import ArrayField
from django.utils.translation import gettext_lazy as _
from django.utils.functional import cached_property


class USER_CERTIFICATION_STATUS(models.TextChoices):
    """认证状态"""

    NONE = "none", "未认证"
    ONGOING = "ongoing", "认证中"
    APPROVED = "approved", "认证通过"
    REJECTED = "rejected", "认证未通过"


class USER_TYPES(models.TextChoices):
    """用户类型"""

    MANUFACTURER = "manufacturer", "企业"
    AUTHORITY = "authority", "认证机构"
    GOVERNMENT = "government", "政府"


class PERMISSION_TYPES(models.TextChoices):
    """权限类型"""

    MENU = "menu", "菜单"
    BUTTON = "button", "按钮"


class Permission(models.Model):
    parent = models.ForeignKey(
        "Permission",
        on_delete=models.CASCADE,
        verbose_name="上级",
        related_name="children",
        null=True,
        blank=True,
    )
    name = models.CharField(max_length=100, verbose_name="名称")
    codename = models.CharField(max_length=100, verbose_name="代码", unique=True)
    type = models.CharField(
        max_length=100,
        verbose_name="类型",
        choices=PERMISSION_TYPES.choices,
        default=PERMISSION_TYPES.MENU.value,
    )
    order = models.PositiveSmallIntegerField(verbose_name="排序，小的在前", default=0)
    create_time = models.DateTimeField(verbose_name="创建时间", auto_now_add=True, db_index=True)
    update_time = models.DateTimeField(verbose_name="更新时间", auto_now=True, db_index=True)


class Group(models.Model):
    name = models.CharField(max_length=100, verbose_name="名称")
    is_active = models.BooleanField(verbose_name="是否有效", default=True, db_index=True)
    description = models.TextField(verbose_name="描述", null=False, blank=True, default="")
    creator = models.ForeignKey("User", on_delete=models.CASCADE, verbose_name="创建者", default=1)
    is_predefined = models.BooleanField(verbose_name="是否预定义", default=False, db_index=True)
    is_government = models.BooleanField(verbose_name="是否政府部门", default=False, db_index=True)
    permissions = models.ManyToManyField(Permission, verbose_name="权限", related_name="groups")
    create_time = models.DateTimeField(verbose_name="创建时间", auto_now_add=True, db_index=True)
    update_time = models.DateTimeField(verbose_name="更新时间", auto_now=True, db_index=True)

    @property
    def permission_ids(self):
        return [permission.id for permission in self.permissions.all()]


class User(AbstractUser):
    USERNAME_FIELD = "phone"
    password = None
    username = None
    first_name = None
    last_name = None
    email = None

    phone = models.CharField(max_length=11, unique=True, verbose_name="手机号")
    nickname = models.CharField(max_length=20, verbose_name="昵称", unique=True)
    type = models.CharField(
        max_length=20,
        choices=USER_TYPES.choices,
        verbose_name="用户类型",
        db_index=True,
        null=True,
        blank=True,
    )
    certificate_status = models.CharField(
        max_length=20,
        choices=USER_CERTIFICATION_STATUS.choices,
        verbose_name="认证状态",
        default=USER_CERTIFICATION_STATUS.NONE.value,
        db_index=True,
        null=True,
        blank=True,
    )
    manufacturer_certification = models.ForeignKey(
        "ManufacturerCertification",
        on_delete=models.SET_NULL,
        verbose_name="企业认证信息",
        null=True,
        blank=True,
        related_name="certification_manufacturer_certification",
    )
    authority_certification = models.ForeignKey(
        "AuthorityCertification",
        on_delete=models.SET_NULL,
        verbose_name="认证机构信息",
        null=True,
        blank=True,
        related_name="certification_authority_certification",
    )
    description = models.TextField(verbose_name="描述", null=False, blank=True, default="")
    groups = models.ManyToManyField(Group, verbose_name="角色", related_name="users")
    create_time = models.DateTimeField(verbose_name="创建时间", auto_now_add=True, db_index=True)
    update_time = models.DateTimeField(verbose_name="更新时间", auto_now=True, db_index=True)

    @cached_property
    def permissions(self):
        permissions = set()
        for group in self.groups.filter(is_active=True).all():
            for permission in group.permissions.all():
                permissions.add(permission.codename)
        return permissions


class UserCertification(models.Model):
    """用户认证信息"""

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        verbose_name="用户",
        db_index=True,
        null=False,
        related_name="user_certification",
    )
    status = models.CharField(
        max_length=100,
        verbose_name="认证状态",
        null=False,
        choices=USER_CERTIFICATION_STATUS.choices,
        db_index=True,
        default=USER_CERTIFICATION_STATUS.ONGOING.value,
    )
    type = models.CharField(
        max_length=100,
        verbose_name="用户类型",
        null=False,
        choices=USER_TYPES.choices,
        db_index=True,
    )
    reason = models.TextField(verbose_name="拒绝原因", null=True)
    manufacturer_certification = models.ForeignKey(
        "ManufacturerCertification",
        on_delete=models.SET_NULL,
        verbose_name="企业认证信息",
        null=True,
        blank=True,
        related_name="user_certification_manufacturer_certification",
    )
    authority_certification = models.ForeignKey(
        "AuthorityCertification",
        on_delete=models.SET_NULL,
        verbose_name="认证机构信息",
        null=True,
        blank=True,
        related_name="user_certification_authority_certification",
    )
    finish_time = models.DateTimeField(verbose_name="认证完成时间", null=True, db_index=True)
    operator = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        verbose_name="操作人",
        null=True,
        blank=True,
        related_name="user_certification_operator",
    )
    create_time = models.DateTimeField(verbose_name="创建时间", auto_now_add=True, db_index=True)
    update_time = models.DateTimeField(verbose_name="更新时间", auto_now=True, db_index=True)


class ManufacturerCertification(models.Model):
    """企业认证信息"""

    name = models.CharField(max_length=100, verbose_name="企业名称", null=False)
    code = models.CharField(max_length=100, verbose_name="统一社会信用代码", null=False)
    geography = models.ForeignKey(
        "lca.Geography",
        on_delete=models.PROTECT,
        verbose_name="地区",
        null=False,
        blank=False,
        related_name="manufacturer_geography",
    )
    contact_name = models.CharField(max_length=100, verbose_name="联系人", null=False)
    contact_phone = models.CharField(max_length=100, verbose_name="联系人手机", null=False)
    contact_email = models.CharField(max_length=100, verbose_name="联系人邮箱", null=False)
    address = models.CharField(max_length=100, verbose_name="企业地址", null=False)
    products = models.CharField(max_length=255, verbose_name="拟认证产品", null=False)
    description = models.TextField(verbose_name="企业主营业务简介", null=False)
    website = models.CharField(max_length=100, verbose_name="企业官网", null=True)
    license = models.ForeignKey(File, on_delete=models.PROTECT, verbose_name="营业执照", null=False)
    create_time = models.DateTimeField(verbose_name="创建时间", auto_now_add=True)
    update_time = models.DateTimeField(verbose_name="更新时间", auto_now=True)


class AuthorityCertification(models.Model):
    """认证机构认证信息"""

    name = models.CharField(max_length=100, verbose_name="认证机构名称", null=False)
    code = models.CharField(max_length=100, verbose_name="统一社会信用代码", null=False)
    geography = models.ForeignKey(
        "lca.Geography",
        on_delete=models.PROTECT,
        verbose_name="机构地区",
        null=False,
        blank=False,
        related_name="authority_geography",
    )
    description = models.TextField(verbose_name="机构描述", null=False, default="", max_length=255)
    address = models.CharField(max_length=100, verbose_name="机构详细地址", null=False)
    approval_number = models.CharField(max_length=100, verbose_name="机构批准号", null=False)
    contact_name = models.CharField(max_length=100, verbose_name="联系人名称", null=False)
    contact_phone = models.CharField(max_length=100, verbose_name="联系人手机", null=False)
    contact_email = models.CharField(max_length=100, verbose_name="联系人邮箱", null=False)
    products = ArrayField(
        models.CharField(max_length=100, choices=CERTIFICATE_PRODUCT.choices),
        verbose_name="认证产品",
        null=False,
    )
    approval_license = models.ForeignKey(
        File,
        on_delete=models.PROTECT,
        verbose_name="批准证书",
        null=False,
        blank=True,
        related_name="authority_approval_license",
    )
    sign = models.ForeignKey(
        File,
        on_delete=models.PROTECT,
        verbose_name="电子签章",
        null=False,
        blank=True,
        related_name="authority_sign",
    )
    logo = models.ForeignKey(
        File,
        on_delete=models.PROTECT,
        verbose_name="LOGO",
        null=False,
        blank=True,
        related_name="authority_logo",
    )
    license = models.ForeignKey(
        File,
        on_delete=models.PROTECT,
        verbose_name="营业执照",
        null=False,
        related_name="authority_license",
    )
    create_time = models.DateTimeField(verbose_name="创建时间", auto_now_add=True)
    update_time = models.DateTimeField(verbose_name="更新时间", auto_now=True)


class AbstractUser(AbstractBaseUser):
    """
    An abstract base class implementing a fully featured User model with
    admin-compliant permissions.

    Username and password are required. Other fields are optional.
    """

    is_staff = models.BooleanField(
        _("staff status"),
        default=False,
        help_text=_("Designates whether the user can log into this admin site."),
    )
    is_active = models.BooleanField(
        _("active"),
        default=True,
        help_text=_(
            "Designates whether this user should be treated as active. Unselect this instead of deleting accounts."
        ),
    )

    EMAIL_FIELD = "email"
    USERNAME_FIELD = "username"
    REQUIRED_FIELDS = ["email"]

    class Meta:
        verbose_name = _("user")
        verbose_name_plural = _("users")
        abstract = True
