from ninja import Router
from lca.accounting.models import (
    ALLOCATION_METHODS,
    BOUNDARIES,
    CERTIFICATE_PRODUCT,
    CUSTOM_FACTOR_SOURCE,
    LIFE_CYCLE_STAGES,
    OUTPUT_TYPES,
    PRODUCT,
    SOURCE_TYPES,
    UNITS,
    Category,
    INPUT_TYPES,
    Geography,
)
from lca.certification.models import (
    CERTIFICATION_APPLICATION_STATUS,
    CERTIFICATION_LIFE_CYCLES,
    CERTIFICATION_STATUS,
    CERTIFICATION_STEP_STATUS,
    CERTIFICATION_STEPS,
    SUPERVISION_STATUS,
)
from lca.common.schema import CommenEnumListSchema, EnumListSchema
from lca.database.models import FLOW_TYPES, LICENSE_TYPES
from lca.resource_center.models import (
    ImplementationStatus,
    InformationStatus,
    ResourceCategoryGroup,
    ResourceType,
    StandardScope,
)
from lca.users.models import USER_CERTIFICATION_STATUS, USER_TYPES
from django.utils.translation import gettext as _

router = Router(tags=["通用"])


@router.get("/enum", summary="类型枚举", response=CommenEnumListSchema)
def list_enum(request):
    out = {}

    out["units"] = [{"label": x, "values": x} for x in UNITS]
    out["boundaries"] = [{"value": x[0], "label": x[1]} for x in BOUNDARIES.choices]
    out["geographies"] = list(Geography.objects.all().values("id", "name", "parent_id"))
    out["categories"] = list(Category.objects.all().values("id", "name", "parent_id"))
    out["emmission_types"] = [{"value": x[0], "label": x[1]} for x in INPUT_TYPES.choices]
    out["output_types"] = [{"value": x[0], "label": x[1]} for x in OUTPUT_TYPES.choices]
    out["flow_types"] = [{"value": x[0], "label": x[1]} for x in FLOW_TYPES.choices]
    out["products"] = [{"value": x[0], "label": x[1]} for x in PRODUCT.choices]
    out["life_cycle_stages"] = [{"value": x[0], "label": x[1]} for x in LIFE_CYCLE_STAGES.choices]
    out["source_types"] = [{"value": x[0], "label": x[1]} for x in SOURCE_TYPES.choices]
    out["custom_factor_sources"] = [{"value": x[0], "label": x[1]} for x in CUSTOM_FACTOR_SOURCE.choices]
    out["allocation_methods"] = [{"value": x[0], "label": x[1]} for x in ALLOCATION_METHODS.choices]
    out["user_certification_status"] = [{"value": x[0], "label": x[1]} for x in USER_CERTIFICATION_STATUS.choices]
    out["user_types"] = [{"value": x[0], "label": x[1]} for x in USER_TYPES.choices]
    out["license_types"] = [{"value": x[0], "label": x[1]} for x in LICENSE_TYPES.choices]

    out["resource_category_groups"] = [{"value": x[0], "label": _(x[1])} for x in ResourceCategoryGroup.choices]
    out["resource_types"] = [{"value": x[0], "label": _(x[1])} for x in ResourceType.choices]
    out["standard_scopes"] = [{"value": x[0], "label": _(x[1])} for x in StandardScope.choices]
    out["implementation_statuses"] = [{"value": x[0], "label": _(x[1])} for x in ImplementationStatus.choices]
    out["information_statuses"] = [{"value": x[0], "label": _(x[1])} for x in InformationStatus.choices]
    return out


@router.get("enum/certification", summary="认证相关类型枚举", response=EnumListSchema)
def list_certification_enum(request):
    out = {}

    out["certification_status"] = [{"value": x[0], "label": x[1]} for x in CERTIFICATION_STATUS.choices]
    out["certification_application_status"] = [
        {"value": x[0], "label": x[1]} for x in CERTIFICATION_APPLICATION_STATUS.choices
    ]
    out["products"] = [{"value": x[0], "label": x[1]} for x in PRODUCT.choices]
    out["certificate_products"] = [{"value": x[0], "label": x[1]} for x in CERTIFICATE_PRODUCT.choices]
    out["certification_life_cycles"] = [{"value": x[0], "label": x[1]} for x in CERTIFICATION_LIFE_CYCLES.choices]
    out["supervision_status"] = [{"value": x[0], "label": x[1]} for x in SUPERVISION_STATUS.choices]
    out["certification_steps"] = [{"value": x[0], "label": x[1]} for x in CERTIFICATION_STEPS.choices]
    out["certification_step_status"] = [{"value": x[0], "label": x[1]} for x in CERTIFICATION_STEP_STATUS.choices]

    return out
