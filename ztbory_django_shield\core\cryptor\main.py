from gmssl import sm2

from ztbory_django_shield.core.cryptor.options import Options
from ztbory_django_shield.core.exceptions import (
    CryptorNotAvailableError,
    CryptorEncryptError,
    CryptorDecryptError,
)


class EmailForgetPassword:
    def __init__(self, username, email):
        self.username = username
        self.email = email


class CryptorShield:
    def __init__(self, options: Options = Options()):
        self.options = options
        self._sm2_cryptor_ = None

    @property
    def cryptor(self):
        if self._sm2_cryptor_ is None and self.options.private_key and self.options.public_key:
            self._sm2_cryptor_ = sm2.CryptSM2(self.options.private_key, self.options.public_key)
        if not self._sm2_cryptor_:
            raise CryptorNotAvailableError()
        return self._sm2_cryptor_

    def encrypt(self, content: str) -> str:
        """加密"""
        try:
            return self.cryptor.encrypt(content.encode("utf-8")).hex()
        except Exception:
            raise CryptorEncryptError()

    def decrypt(self, content: str) -> str:
        """解密"""
        try:
            return self.cryptor.decrypt(bytes.fromhex(content)).decode("utf-8")
        except Exception:
            raise CryptorDecryptError()
