from typing import Optional
from ninja import Schema
from pydantic import Field

from lca.accounting.models import BOUNDARIES
from lca.accounting.schema import CategorySchema, GeographySchema


class GenerateLifeCycleInSchema(Schema):
    """
    生成生命周期
    """

    model_id: int = Field(..., title="模型id")


class FileAISchema(Schema):
    """
    带文件的AI
    """

    file_id: str = Field(..., title="文件")


class GoalScopeSchema(Schema):
    """目标范围"""

    name: Optional[str] = Field(None, title="产品名称")
    functional_unit: Optional[str] = Field(None, title="功能单位")
    category: Optional[CategorySchema] = Field(None, title="类别ID")
    specs: Optional[str] = Field(None, title="型号")
    company_name: Optional[str] | None = Field(None, title="公司名称")
    description: Optional[str] | None = Field(None, title="描述")
    boundary: Optional[BOUNDARIES] = Field(None, title=f"系统边界，可选值{BOUNDARIES.choices}")
    year: Optional[int] = Field(None, title="年份")
    geography: Optional[GeographySchema] = Field(None, title="产品产地")
    rule: Optional[str] | None = Field(None, title="取舍规则")
    amount: Optional[float] = Field(None, title="产量")
    unit: Optional[str] = Field(None, title="产量单位")
