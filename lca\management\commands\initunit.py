from django.core.management import BaseCommand
from lca.accounting.models import Unit
import csv


class Command(BaseCommand):
    help = "初始化单位"

    def handle(self, *args, **options):
        print("UNIT:")
        init_unit()


def init_unit():
    with open("lca/management/commands/data/unit.csv") as file:
        reader = csv.DictReader(file)
        for row in reader:
            print(row["id"])
            Unit.objects.update_or_create(
                id=row["id"],
                defaults=dict(
                    symbol=row["id"],
                    type=row["type"],
                    conversion_factor=row["conversion_factor"],
                ),
            )
