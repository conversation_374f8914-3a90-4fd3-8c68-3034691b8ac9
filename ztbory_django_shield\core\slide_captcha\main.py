from django.core.cache import cache

from ztbory_django_shield.core.exceptions import (
    SlideCaptchaNotFoundError,
    SlideCaptchaSlideVerifyFailError,
)
from ztbory_django_shield.core.slide_captcha.gallery import GalleryShuffler
from ztbory_django_shield.core.slide_captcha.options import Options
from ztbory_django_shield.core.slide_captcha.trimmer import Trimmer
from ztbory_django_shield.core.utils import random_uuid, random_sample


class SlideCaptcha:
    def __init__(self, uuid, foreground, background, point, secret):
        self.uuid = uuid
        self.foreground = foreground
        self.background = background
        self.point = point
        self.secret = secret


class SlideCaptchaShield:
    def __init__(self, options: Options = Options()):
        self.options = options
        self.cache = cache
        self.gallery = options.gallery_instance

    def mount_gallery_instance(self, gallery_instance: GalleryShuffler):
        """重新替换图库"""
        self.gallery = gallery_instance

    def _gen_cache_key_(self, uuid: str) -> str:
        """生成校验码key"""
        return f"Shield:SlideCaptcha:{uuid}"

    def generate_captcha(self) -> SlideCaptcha:
        """生成校验数据"""
        image = self.gallery.choice()
        trimmer = Trimmer(image, width=self.options.width, height=self.options.height)
        uuid = random_uuid()
        cache_value = SlideCaptcha(
            uuid=uuid,
            foreground=trimmer.base64_foreground,
            background=trimmer.base64_background,
            point=trimmer.point,
            secret=random_sample(12),
        )
        print(f"point={trimmer.point}")
        self.cache.set(self._gen_cache_key_(uuid), cache_value, self.options.expire_in_seconds)
        return cache_value

    def validate_captcha_for_secret(self, uuid: str, captcha: float) -> str:
        """滑动校验，成功返回校验密钥，失败会引发校验错误，同时该组校验数据会失效"""
        cache_value = self.cache.get(self._gen_cache_key_(uuid))
        if not cache_value:
            raise SlideCaptchaNotFoundError()

        try:
            x_axis = cache_value.point[0]
            captcha = int(captcha)
            if abs(x_axis - captcha) <= 6:
                return cache_value.secret
            self.cache.delete(self._gen_cache_key_(uuid))
        except Exception:
            pass
        raise SlideCaptchaSlideVerifyFailError()

    def validate_captcha_secret(self, uuid: str, secret: str) -> bool:
        """校验密钥是否正确"""

        # 如果开启DEBUG模式, 验证恒等于True
        if self.options.is_debug and uuid == "ztbory" and secret == "ztbory":
            return True

        cache_value = self.cache.get(self._gen_cache_key_(uuid))
        if not cache_value:
            raise SlideCaptchaNotFoundError()

        try:
            self.cache.delete(self._gen_cache_key_(uuid))
            result = secret == cache_value.secret
            if not result:
                raise SlideCaptchaSlideVerifyFailError()
            return result
        except Exception:
            pass

        raise SlideCaptchaSlideVerifyFailError()

    def gain_captcha(self, uuid: str) -> SlideCaptcha:
        """根据校验码uuid获取secret"""
        cache_value = self.cache.get(self._gen_cache_key_(uuid))
        if not cache_value:
            raise SlideCaptchaNotFoundError()
        return cache_value
