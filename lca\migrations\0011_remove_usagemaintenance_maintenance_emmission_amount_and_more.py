# Generated by Django 5.1.7 on 2025-03-27 07:14

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0010_usagemaintenance"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="usagemaintenance",
            name="maintenance_emmission_amount",
        ),
        migrations.RemoveField(
            model_name="usagemaintenance",
            name="maintenance_type",
        ),
        migrations.RemoveField(
            model_name="usagemaintenance",
            name="maintenance_unit_id",
        ),
        migrations.AlterField(
            model_name="distribution",
            name="co2e",
            field=models.DecimalField(decimal_places=5, default=0, max_digits=20, verbose_name="CO2e"),
        ),
        migrations.AlterField(
            model_name="usagemaintenance",
            name="co2e",
            field=models.DecimalField(decimal_places=5, default=0, max_digits=20, verbose_name="CO2e"),
        ),
        migrations.CreateModel(
            name="UsageMaintenanceItem",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "maintenance_type",
                    models.CharField(blank=True, max_length=100, null=True, verbose_name="维护方式"),
                ),
                (
                    "maintenance_emmission_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=5,
                        max_digits=20,
                        null=True,
                        verbose_name="维护排放量",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "maintenance_unit_id",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="maintenance_unit",
                        to="lca.unit",
                        verbose_name="维护排放量单位",
                    ),
                ),
                (
                    "usage_maintenance",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="maintenance_items",
                        to="lca.usagemaintenance",
                        verbose_name="使用和维护",
                    ),
                ),
            ],
        ),
    ]
