# Generated by Django 5.2.1 on 2025-07-07 02:19

import django.contrib.postgres.fields
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0063_usercertification_operator"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="certification",
            name="info",
        ),
        migrations.RemoveField(
            model_name="authority",
            name="active",
        ),
        migrations.RemoveField(
            model_name="authority",
            name="avatar",
        ),
        migrations.RemoveField(
            model_name="authority",
            name="description",
        ),
        migrations.RemoveField(
            model_name="certification",
            name="product",
        ),
        migrations.RemoveField(
            model_name="manufacturer",
            name="create_time",
        ),
        migrations.RemoveField(
            model_name="manufacturer",
            name="region",
        ),
        migrations.RemoveField(
            model_name="manufacturer",
            name="update_time",
        ),
        migrations.AddField(
            model_name="authority",
            name="approval_license",
            field=models.ForeignKey(
                blank=True,
                null=True,
                default=None,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="authority_approval_license3",
                to="lca.file",
                verbose_name="批准证书",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="authority",
            name="approval_number",
            field=models.CharField(default="test", max_length=100, verbose_name="机构批准号"),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="authority",
            name="contact_email",
            field=models.CharField(default="test", max_length=100, verbose_name="联系人邮箱"),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="authority",
            name="contact_name",
            field=models.CharField(default="test", max_length=100, verbose_name="联系人名称"),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="authority",
            name="contact_phone",
            field=models.CharField(default="test", max_length=100, verbose_name="联系人手机"),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="authority",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                default=django.utils.timezone.now,
                verbose_name="创建时间",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="authority",
            name="geography",
            field=models.ForeignKey(
                default="CN",
                on_delete=django.db.models.deletion.PROTECT,
                related_name="authority_geography3",
                to="lca.geography",
                verbose_name="机构地区",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="authority",
            name="license",
            field=models.ForeignKey(
                blank=True,
                null=True,
                default=None,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="authority_license3",
                to="lca.file",
                verbose_name="营业执照",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="authority",
            name="logo",
            field=models.ForeignKey(
                blank=True,
                null=True,
                default=None,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="authority_logo3",
                to="lca.file",
                verbose_name="LOGO",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="authority",
            name="products",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(
                    choices=[
                        ("lithium_battery", "锂电池"),
                        ("photovoltaic", "光伏产品"),
                        ("steel", "钢铁"),
                        ("textile", "纺织品"),
                        ("electronics", "电子电器"),
                        ("tire", "轮胎"),
                        ("cement", "水泥"),
                        ("electrolytic_aluminum", "电解铝"),
                        ("phosphate_fertilizer", "磷铵"),
                        ("wood_product", "木制品"),
                        ("electric_vehicle", "电动汽车"),
                        ("power_battery", "动力电池"),
                        ("glass", "玻璃"),
                        ("ethylene", "乙烯"),
                        ("synthetic_ammonia", "合成氨"),
                    ],
                    max_length=100,
                ),
                default=[],
                size=None,
                verbose_name="认证产品",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="authority",
            name="sign",
            field=models.ForeignKey(
                blank=True,
                null=True,
                default=None,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="authority_sign3",
                to="lca.file",
                verbose_name="电子签章",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="authority",
            name="update_time",
            field=models.DateTimeField(auto_now=True, verbose_name="更新时间"),
        ),
        migrations.AddField(
            model_name="certification",
            name="contact_name",
            field=models.CharField(default="", max_length=100, verbose_name="联系人"),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="certification",
            name="contact_phone",
            field=models.CharField(default="", max_length=100, verbose_name="联系人手机"),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="certification",
            name="image",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="certification_image",
                to="lca.file",
                verbose_name="产品图片",
            ),
        ),
        migrations.AddField(
            model_name="certification",
            name="manufacturer_address",
            field=models.CharField(default="", max_length=100, verbose_name="生产企业地址"),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="certification",
            name="principal_address",
            field=models.CharField(default="", max_length=100, verbose_name="委托人地址"),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="certification",
            name="producer_address",
            field=models.CharField(default="", max_length=100, verbose_name="生产者（制造商）地址"),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="certification",
            name="producer_name",
            field=models.CharField(default="", max_length=100, verbose_name="生产者（制造商）名称"),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="certification",
            name="product_description",
            field=models.TextField(default="", verbose_name="产品介绍"),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="certification",
            name="product_function",
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name="产品功能"),
        ),
        migrations.AddField(
            model_name="certification",
            name="product_performance",
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name="产品性能指标"),
        ),
        migrations.AddField(
            model_name="certification",
            name="product_standard",
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name="产品执行标准"),
        ),
        migrations.AddField(
            model_name="certification",
            name="summary",
            field=models.TextField(default="", verbose_name="企业概况"),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="certification",
            name="supervision_status",
            field=models.CharField(
                choices=[
                    ("waiting", "待监督"),
                    ("ongoing", "监督中"),
                    ("completed", "监督完成"),
                ],
                db_index=True,
                max_length=100,
                null=True,
                verbose_name="监督管理状态",
            ),
        ),
        migrations.AddField(
            model_name="manufacturer",
            name="address",
            field=models.CharField(default="", max_length=100, verbose_name="企业地址"),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="manufacturer",
            name="code",
            field=models.CharField(default="", max_length=100, verbose_name="统一社会信用代码"),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="manufacturer",
            name="contact_email",
            field=models.CharField(default="", max_length=100, verbose_name="联系人邮箱"),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="manufacturer",
            name="contact_name",
            field=models.CharField(default="", max_length=100, verbose_name="联系人"),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="manufacturer",
            name="contact_phone",
            field=models.CharField(default="", max_length=100, verbose_name="联系人手机"),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="manufacturer",
            name="description",
            field=models.TextField(default="", verbose_name="企业主营业务简介"),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="manufacturer",
            name="geography",
            field=models.ForeignKey(
                default="CN",
                on_delete=django.db.models.deletion.PROTECT,
                related_name="manufacturer_geography3",
                to="lca.geography",
                verbose_name="地区",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="manufacturer",
            name="license",
            field=models.ForeignKey(
                blank=True,
                null=True,
                default=None,
                on_delete=django.db.models.deletion.PROTECT,
                to="lca.file",
                verbose_name="营业执照",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="manufacturer",
            name="products",
            field=models.CharField(default="", max_length=255, verbose_name="拟认证产品"),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="manufacturer",
            name="user",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="manufacturer",
                to=settings.AUTH_USER_MODEL,
                verbose_name="用户",
            ),
        ),
        migrations.AddField(
            model_name="manufacturer",
            name="website",
            field=models.CharField(max_length=100, null=True, verbose_name="企业官网"),
        ),
        migrations.AlterField(
            model_name="authority",
            name="address",
            field=models.CharField(max_length=100, verbose_name="机构详细地址"),
        ),
        migrations.AlterField(
            model_name="authority",
            name="code",
            field=models.CharField(max_length=100, verbose_name="统一社会信用代码"),
        ),
        migrations.AlterField(
            model_name="authority",
            name="name",
            field=models.CharField(max_length=100, verbose_name="认证机构名称"),
        ),
        migrations.AlterField(
            model_name="certification",
            name="authority",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="certification",
                to="lca.authority",
                verbose_name="认证机构",
            ),
        ),
        migrations.AlterField(
            model_name="certification",
            name="manufacturer",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="certification",
                to="lca.manufacturer",
                verbose_name="生产企业",
            ),
        ),
        migrations.AlterField(
            model_name="manufacturer",
            name="name",
            field=models.CharField(max_length=100, verbose_name="企业名称"),
        ),
        migrations.CreateModel(
            name="CarbonFootprintVerification",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "product_name",
                    models.CharField(max_length=100, verbose_name="产品名称"),
                ),
                ("specs", models.CharField(max_length=100, verbose_name="产品型号")),
                (
                    "functional_unit",
                    models.CharField(max_length=100, verbose_name="功能单位"),
                ),
                ("boundary", models.CharField(max_length=100, verbose_name="系统边界")),
                (
                    "client_name",
                    models.CharField(max_length=100, verbose_name="受核查方名称"),
                ),
                (
                    "address",
                    models.CharField(max_length=100, verbose_name="受核查方地址"),
                ),
                (
                    "credit_code",
                    models.CharField(max_length=100, verbose_name="统一社会信用代码"),
                ),
                (
                    "legal_representative",
                    models.CharField(max_length=100, verbose_name="法定代表人"),
                ),
                ("verification_date", models.DateField(verbose_name="核查日期")),
                ("initial_inspections", models.JSONField(verbose_name="初级数据核查")),
                ("followup_inspections", models.JSONField(verbose_name="次级数据核查")),
                (
                    "data_acquisition_principle",
                    models.TextField(null=True, verbose_name="数据获取原则"),
                ),
                (
                    "data_quality_requirement",
                    models.TextField(null=True, verbose_name="数据质量要求"),
                ),
                (
                    "data_quality_evaluation",
                    models.TextField(null=True, verbose_name="数据质量评价"),
                ),
                (
                    "quantification_method",
                    models.TextField(null=True, verbose_name="量化方法"),
                ),
                (
                    "product_quantifications",
                    models.JSONField(verbose_name="产品碳足迹量化"),
                ),
                (
                    "verification_conclusion",
                    models.TextField(null=True, verbose_name="核查结论"),
                ),
                (
                    "verification_recommendation",
                    models.TextField(null=True, verbose_name="核查建议"),
                ),
                (
                    "uncertainty_analysis",
                    models.TextField(null=True, verbose_name="不确定性分析"),
                ),
                ("result", models.BooleanField(null=True, verbose_name="核查结果")),
                (
                    "create_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "certification",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="carbon_footprint_verification",
                        to="lca.certification",
                        verbose_name="认证记录",
                    ),
                ),
                (
                    "result_file",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="carbon_footprint_verification_result_file",
                        to="lca.file",
                        verbose_name="核查结果审批文件",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="CertificateIssuance",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "certificate_file",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="certificate_issuance_certificate_file",
                        to="lca.file",
                        verbose_name="证书文件",
                    ),
                ),
                (
                    "certification",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="certificate_issuance",
                        to="lca.certification",
                        verbose_name="认证记录",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ReviewDecision",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("result", models.BooleanField(null=True, verbose_name="认证决定")),
                (
                    "certification",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="review_decision",
                        to="lca.certification",
                        verbose_name="认证记录",
                    ),
                ),
                (
                    "result_file",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="review_decision_result_file",
                        to="lca.file",
                        verbose_name="认证决定审批文件",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Supervision",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("waiting", "待监督"),
                            ("ongoing", "监督中"),
                            ("completed", "监督完成"),
                        ],
                        db_index=True,
                        max_length=100,
                        verbose_name="监督管理状态",
                    ),
                ),
                (
                    "issue_severe_quality_problem",
                    models.BooleanField(verbose_name="是否出现严重质量问题"),
                ),
                (
                    "product_certification_quality_doubt",
                    models.BooleanField(verbose_name="是否对碳足迹量化提出质疑"),
                ),
                (
                    "the_same",
                    models.BooleanField(verbose_name="标准符合性或产品一致性"),
                ),
                (
                    "enterprise_capability_supervision",
                    models.BooleanField(verbose_name="企业保证能力监督检查"),
                ),
                (
                    "product_consistency_supervision",
                    models.BooleanField(verbose_name="产品一致性监督检查"),
                ),
                (
                    "product_footprint_verification",
                    models.BooleanField(verbose_name="产品碳足迹核查"),
                ),
                (
                    "carbon_reduction_plan_supervision",
                    models.BooleanField(verbose_name="降碳计划实施监督检查"),
                ),
                (
                    "product_footprint_decline_supervision",
                    models.BooleanField(verbose_name="产品碳足迹量化是否较上一次核查有所下降"),
                ),
                (
                    "previous_review_issue_supervision",
                    models.BooleanField(verbose_name="上次评价整改措施监督"),
                ),
                (
                    "supervision_result_evaluation",
                    models.TextField(null=True, verbose_name="监督结果的评价"),
                ),
                ("result", models.BooleanField(null=True, verbose_name="监督决定")),
                (
                    "start_time",
                    models.DateTimeField(db_index=True, null=True, verbose_name="开始监督时间"),
                ),
                (
                    "end_time",
                    models.DateTimeField(db_index=True, null=True, verbose_name="结束监督时间"),
                ),
                (
                    "certification",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="supervisions",
                        to="lca.certification",
                        verbose_name="认证记录",
                    ),
                ),
                (
                    "result_file",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="supervision_result_file",
                        to="lca.file",
                        verbose_name="监督决定审批文件",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="VerificationReportIssuance",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "certification",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="verification_report_issuance",
                        to="lca.certification",
                        verbose_name="认证记录",
                    ),
                ),
                (
                    "report_file",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="verification_report_issuance_report_file",
                        to="lca.file",
                        verbose_name="核查报告文件",
                    ),
                ),
            ],
        ),
        migrations.DeleteModel(
            name="CertificationInfo",
        ),
    ]
