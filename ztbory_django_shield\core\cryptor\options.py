from ztbory_django_shield.core.options import DjangoOptions


class Options(DjangoOptions):
    """找回密码配置"""

    def __init__(
        self,
        private_key: str = "",
        public_key: str = "",
    ):
        """

        :param private_key: 私钥
        :param public_key: 公钥
        """
        self.private_key = private_key
        self.public_key = public_key

        self.load_django_settings("cryptor")
