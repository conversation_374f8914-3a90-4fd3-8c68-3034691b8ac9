from django.db import models
from config import settings


class File(models.Model):
    """
    上传文件
    """

    id = models.CharField(max_length=200, verbose_name="文件路径", primary_key=True)
    name = models.CharField(max_length=200, verbose_name="名称")
    size = models.BigIntegerField(verbose_name="大小")
    production_report = models.ForeignKey(
        "lca.Model",
        related_name="production_reports",
        on_delete=models.CASCADE,
        null=True,
        verbose_name="《工业产销总值及主要产品产量》或《生产统计产量表》",
    )
    bom = models.ForeignKey(
        "lca.Model",
        related_name="boms",
        on_delete=models.CASCADE,
        null=True,
        verbose_name="BOM表",
    )
    purchase_contract = models.ForeignKey(
        "lca.Model",
        related_name="purchase_contracts",
        on_delete=models.CASCADE,
        null=True,
        verbose_name="采购合同",
    )
    water_bill = models.ForeignKey(
        "lca.Model",
        related_name="water_bills",
        on_delete=models.CASCADE,
        null=True,
        verbose_name="水费结算单",
    )
    energy_report = models.ForeignKey(
        "lca.Model",
        related_name="energy_reports",
        on_delete=models.CASCADE,
        null=True,
        verbose_name="《能源购进、消费与库存表》",
    )
    energy_tax = models.ForeignKey(
        "lca.Model",
        related_name="energy_taxs",
        on_delete=models.CASCADE,
        null=True,
        verbose_name="能源购买发票",
    )
    energy_other = models.ForeignKey(
        "lca.Model",
        related_name="energy_others",
        on_delete=models.CASCADE,
        null=True,
        verbose_name="能源其他凭证",
    )
    environment_impact_report = models.ForeignKey(
        "lca.Model",
        related_name="environment_impact_reports",
        on_delete=models.CASCADE,
        null=True,
        verbose_name="环评报告",
    )
    environment_check_report = models.ForeignKey(
        "lca.Model",
        related_name="environment_check_reports",
        on_delete=models.CASCADE,
        null=True,
        verbose_name="环境检测报告",
    )
    outlet_flow_report = models.ForeignKey(
        "lca.Model",
        related_name="outlet_flow_reports",
        on_delete=models.CASCADE,
        null=True,
        verbose_name="排水口检测流量统计表",
    )
    waste_ledger = models.ForeignKey(
        "lca.Model",
        related_name="waste_ledgers",
        on_delete=models.CASCADE,
        null=True,
        verbose_name="废物台账",
    )
    craft_process = models.ForeignKey(
        "lca.Model",
        related_name="craft_processes",
        on_delete=models.CASCADE,
        null=True,
        verbose_name="工艺流程图",
    )
    process_flow = models.ForeignKey(
        "lca.Certification",
        related_name="process_flows",
        on_delete=models.CASCADE,
        null=True,
        verbose_name="产品工艺流程图",
    )
    equipment = models.ForeignKey(
        "lca.Certification",
        related_name="equipments",
        on_delete=models.CASCADE,
        null=True,
        verbose_name="主要生产、计量设备清单",
    )
    label = models.ForeignKey(
        "lca.Certification",
        related_name="labels",
        on_delete=models.CASCADE,
        null=True,
        verbose_name="标识认证数据和信息采集清单",
    )
    carbon_reduction_plan = models.ForeignKey(
        "lca.Certification",
        related_name="plans",
        on_delete=models.CASCADE,
        null=True,
        verbose_name="产品降碳方案或计划",
    )
    capability = models.ForeignKey(
        "lca.Certification",
        related_name="capabilities",
        on_delete=models.CASCADE,
        null=True,
        verbose_name="保证能力相关管理文件或目录",
    )
    other = models.ForeignKey(
        "lca.Certification",
        related_name="others",
        on_delete=models.CASCADE,
        null=True,
        verbose_name="其它",
    )
    create_time = models.DateTimeField(verbose_name="创建时间", auto_now_add=True)
    update_time = models.DateTimeField(verbose_name="更新时间", auto_now=True)

    def __str__(self):
        return f"{self.id}"

    @property
    def url(self):
        return f"{settings.MINIO_PREFIX}{settings.MINIO_BUCKET}/{self.id}"
