from ninja import Query, Router
from infra.PageNumberPagination import PageNumberPagination
from infra.decorators import permission_required
from infra.filter import filter
from ninja.pagination import paginate

from lca.certification.models import Certification
from lca.certification.schema import CertificationRowSchema, CertificationSchema


router = Router(tags=["后台-认证管理"])


@router.get(
    "",
    summary="获取认证列表",
    response=list[CertificationRowSchema],
    operation_id="adminGetCertificationList",
)
@paginate(PageNumberPagination)
@permission_required("CertificationManage")
def get_certification_list(
    request,
    product_name: str | None = Query(None, title="产品名称"),
    category_id: str | None = Query(None, title="所属行业"),
    principal: str | None = Query(None, title="委托人"),
    authority_name: str | None = Query(None, title="认证机构名称"),
    certification_status: str | None = Query(None, title="认证状态"),
):
    return filter(
        Certification.objects.order_by("-create_time"),
        product_name__icontains=product_name,
        category_id=category_id,
        principal__icontains=principal,
        authority__name__icontains=authority_name,
        certification_status=certification_status,
    ).prefetch_related("category", "authority")


@router.get(
    "{id}",
    summary="获取认证详情",
    response=CertificationSchema,
    operation_id="adminGetCertificationDetail",
)
@permission_required("CertificationManage:Detail")
def get_certification_detail(request, id: int):
    item = Certification.objects.get(pk=id)
    return item
