# Generated by Django 5.2.1 on 2025-07-18 08:20

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0085_alter_resourcecenter_category"),
    ]

    operations = [
        migrations.CreateModel(
            name="OperationLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "operation_type",
                    models.CharField(
                        choices=[
                            ("CREATE", "新增"),
                            ("EDIT", "编辑"),
                            ("DELETE", "删除"),
                            ("LOGIN", "登录"),
                            ("LOGOUT", "登出"),
                            ("LOGOFF", "注销"),
                            ("SET_PERMISSIONS", "设置权限"),
                            ("IMPORT_DATABASE", "导入数据库"),
                            ("UPLOAD_FACTOR", "上传因子"),
                            ("REVIEW_FACTOR", "审核因子"),
                            ("SET_PLATFORM_MODEL", "设置平台模型"),
                            ("UNPUBLISH_INFO", "下架信息"),
                            ("PUBLISH_INFO", "发布文章"),
                        ],
                        db_comment="操作类型",
                        max_length=32,
                        verbose_name="操作类型",
                    ),
                ),
                (
                    "operator_username",
                    models.CharField(
                        db_comment="操作人账号",
                        db_index=True,
                        default="",
                        max_length=255,
                        verbose_name="操作人账号",
                    ),
                ),
                (
                    "operator_nickname",
                    models.CharField(
                        db_comment="账号用户名",
                        db_index=True,
                        default="",
                        max_length=255,
                        verbose_name="账号用户名",
                    ),
                ),
                ("content", models.TextField(db_comment="操作内容", verbose_name="操作内容")),
                (
                    "operation_time",
                    models.DateTimeField(db_comment="操作时间", db_index=True, verbose_name="操作时间"),
                ),
                (
                    "create_time",
                    models.DateTimeField(auto_now_add=True, db_comment="创建时间", verbose_name="创建时间"),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, db_comment="更新时间", verbose_name="更新时间"),
                ),
                (
                    "operator",
                    models.ForeignKey(
                        db_comment="操作人",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="操作人",
                    ),
                ),
            ],
            options={
                "verbose_name": "日志记录",
            },
        ),
    ]
