# Generated by Django 5.2.1 on 2025-07-24 07:00

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('lca', '0094_alter_emissionsourcemanagement_approve_contact_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='emissionsourceinputapplication',
            name='flow_type',
            field=models.CharField(choices=[('elementary', '基本流'), ('intermediate', '中间流'), ('product', '产品流'), ('co-product', '共产品流'), ('waste', '废物流'), ('resource', '资源流')], default='elementary', max_length=255, verbose_name='流类型'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='emissionsourceinputapplication',
            name='related_emission_source',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='related_emission_source_applications', to='lca.emissionsource', verbose_name='关联的排放源'),
        ),
    ]
