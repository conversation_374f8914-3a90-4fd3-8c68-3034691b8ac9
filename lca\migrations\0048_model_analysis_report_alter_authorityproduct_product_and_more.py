# Generated by Django 5.2.1 on 2025-06-09 02:17

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0047_alter_emissionsourcedataset_emission_source_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="model",
            name="analysis_report",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="analysisReportModel",
                to="lca.file",
                verbose_name="分析报告",
            ),
        ),
        migrations.AlterField(
            model_name="authorityproduct",
            name="product",
            field=models.CharField(
                choices=[
                    ("cement", "通用硅酸盐水泥"),
                    ("steel-sheet", "钢铁-板材"),
                    ("steel-wire-rod", "钢铁-线材"),
                    ("photovoltaic", "光伏组件"),
                    ("electric_vehicle", "轻型电动汽车"),
                    ("power_battery", "动力电池"),
                    ("glass", "平板玻璃"),
                    ("electrolytic_aluminum", "电解铝"),
                    ("ethylene", "乙烯"),
                    ("synthetic_ammonia", "合成氨"),
                ],
                db_index=True,
                max_length=100,
                verbose_name="产品名称",
            ),
        ),
        migrations.AlterField(
            model_name="certification",
            name="product",
            field=models.CharField(
                choices=[
                    ("cement", "通用硅酸盐水泥"),
                    ("steel-sheet", "钢铁-板材"),
                    ("steel-wire-rod", "钢铁-线材"),
                    ("photovoltaic", "光伏组件"),
                    ("electric_vehicle", "轻型电动汽车"),
                    ("power_battery", "动力电池"),
                    ("glass", "平板玻璃"),
                    ("electrolytic_aluminum", "电解铝"),
                    ("ethylene", "乙烯"),
                    ("synthetic_ammonia", "合成氨"),
                ],
                max_length=100,
                null=True,
                verbose_name="产品",
            ),
        ),
        migrations.AlterField(
            model_name="emissionsourceproduct",
            name="product",
            field=models.CharField(
                choices=[
                    ("cement", "通用硅酸盐水泥"),
                    ("steel-sheet", "钢铁-板材"),
                    ("steel-wire-rod", "钢铁-线材"),
                    ("photovoltaic", "光伏组件"),
                    ("electric_vehicle", "轻型电动汽车"),
                    ("power_battery", "动力电池"),
                    ("glass", "平板玻璃"),
                    ("electrolytic_aluminum", "电解铝"),
                    ("ethylene", "乙烯"),
                    ("synthetic_ammonia", "合成氨"),
                ],
                db_index=True,
                max_length=255,
                verbose_name="产品",
            ),
        ),
        migrations.AlterField(
            model_name="model",
            name="product",
            field=models.CharField(
                choices=[
                    ("cement", "通用硅酸盐水泥"),
                    ("steel-sheet", "钢铁-板材"),
                    ("steel-wire-rod", "钢铁-线材"),
                    ("photovoltaic", "光伏组件"),
                    ("electric_vehicle", "轻型电动汽车"),
                    ("power_battery", "动力电池"),
                    ("glass", "平板玻璃"),
                    ("electrolytic_aluminum", "电解铝"),
                    ("ethylene", "乙烯"),
                    ("synthetic_ammonia", "合成氨"),
                ],
                max_length=100,
                null=True,
                verbose_name="产品",
            ),
        ),
    ]
