from typing import Callable, Any

from ztbory_django_shield.core.email.main import EmailForgetPassword
from ztbory_django_shield.base.shield import (
    password_shield,
    slide_captcha_shield,
    email_shield,
    cryptor_shield,
)
from ztbory_django_shield.core.exceptions import (
    PasswordTwoFactorRequiredError,
    PasswordLoginFailError,
)
from ztbory_django_shield.core.schemas import ShieldLoginSchema
from ztbory_django_shield.core.slide_captcha import GalleryShuffler


class Shield:
    @staticmethod
    def mount_gallery_instance(gallery_instance: GalleryShuffler):
        """重新替换图库, 需要实现GalleryShuffler"""
        slide_captcha_shield.mount_gallery_instance(gallery_instance)

    @staticmethod
    def encrypt(content: str) -> str:
        """sm2加密"""
        return cryptor_shield.encrypt(content)

    @staticmethod
    def decrypt(content: str) -> str:
        """sm2解密"""
        return cryptor_shield.decrypt(content)

    @staticmethod
    def login(
        data: ShieldLoginSchema,
        login_func: Callable[..., Any],
        login_func_args: tuple = (),
    ) -> Any:
        """双因子登录 - 含登录前限制验证及登陆后限制标识更新

        :param data: 登录表单提取登录所需参数
        :param login_func: 自定义登录方法
        :param login_func_args: request, login-登录表单, kwargs-其他自定义参数
        :return:
        """

        Shield.before_login_with_two_factor(data.username, data.ip, data.captcha_uuid, data.captcha)
        args, kwargs = login_func_args
        login_result = login_func(*args, **kwargs)
        Shield.after_login(data.username, data.ip, login_result is not None)
        if login_result is None:
            raise PasswordLoginFailError()
        return login_result

    @staticmethod
    def before_login_with_two_factor(username: str, ip: str, captcha_uuid: str, captcha_secret: str):
        """双因子登录 - 登陆前校验

        :param username: 用户名
        :param ip: 访问者IP
        :param captcha_uuid: 验证码uuid
        :param captcha_secret: 验证码
        :return:
        """
        if password_shield.gain_is_need_two_factor(username=username, ip=ip):
            if not all([captcha_uuid, captcha_secret]):
                raise PasswordTwoFactorRequiredError()
            slide_captcha_shield.validate_captcha_secret(uuid=captcha_uuid, secret=captcha_secret)

        password_shield.do_before_login(username=username, ip=ip)

    @staticmethod
    def before_login_without_two_factor(username: str, ip: str):
        """简单登录 - 登录前校验

        :param username: 用户名
        :param ip: 访问者IP
        :return:
        """
        password_shield.do_before_login(username=username, ip=ip)

    @staticmethod
    def after_login(username: str, ip: str, login_result: bool):
        """简单/双因子登陆后 - 记录标识值, 用于登陆前判断

        :param username: 用户名
        :param ip: 访问者IP
        :param login_result: 是否登录成功
        :return:
        """
        password_shield.do_after_login(username=username, ip=ip, login_result=login_result)

    @staticmethod
    def validate_password(username: str, password: str):
        """密码校验

        :param username: 用户名
        :param password: 密码
        :return:
        """
        password_shield.valid_password(username=username, password=password)

    @staticmethod
    def is_password_safely(username: str, password: str):
        """判断密码是否符合要求

        :param username: 用户名
        :param password: 密码
        :return:
        """
        return password_shield.is_password_safely(username, password)

    @staticmethod
    def send_forget_password_email(
        username: str, email: str, logo_url: str = None, reset_password_url: str = None
    ) -> int:
        """发送密码找回邮件

        :param username: 用户名
        :param email: 邮箱
        :param logo_url: logo地址 https图片
        :param reset_password_url: 重置密码地址
        :return: 有效时长 (s)
        """
        return email_shield.send_forget_password_email(
            username=username,
            email=email,
            logo_url=logo_url,
            reset_password_url=reset_password_url,
        )

    @staticmethod
    def get_info_by_email_token(token: str) -> EmailForgetPassword:
        """获取重置密码的用户及邮箱信息

        :param token: 密码重置校验码
        :return: 账户信息
        """
        return email_shield.gain_token_info(token=token)
