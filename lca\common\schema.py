from __future__ import annotations
from ninja import Schema
from pydantic import Field
from typing import Optional


class UserSchema(Schema):
    id: int
    nickname: str


class EnumSchema(Schema):
    label: str = Field(..., title="标签")
    value: str = Field(..., title="值")


class EnumSchemaS(Schema):
    label: str = Field(..., title="标签")
    values: str = Field(..., title="值")


class EnumWithParent(Schema):
    id: str = Field(..., title="id")
    name: str = Field(..., title="名称")
    parent_id: Optional[str] = Field(None, title="父级id")


class EnumListSchema(Schema):
    certification_status: list[EnumSchema] = Field(..., title="认证状态")
    certification_application_status: list[EnumSchema] = Field(..., title="认证申请状态")
    products: list[EnumSchema] = Field(..., title="产品")
    certificate_products: list[EnumSchema] = Field(..., title="认证用产品")
    certification_life_cycles: list[EnumSchema] = Field(..., title="认证生命周期")
    supervision_status: list[EnumSchema] = Field(..., title="监督管理状态")
    certification_step_status: list[EnumSchema] = Field(..., title="认证步骤状态")
    certification_steps: list[EnumSchema] = Field(..., title="认证步骤")


class RegionSchema(Schema):
    id: int = Field(..., title="id")
    name: str = Field(..., title="名称")
    parent: Optional[RegionSchema] = Field(None, title="父级")


class CommenEnumListSchema(Schema):
    """ """

    units: list[EnumSchemaS] = Field(..., title="单位")
    boundaries: list[EnumSchema] = Field(..., title="系统边界")
    geographies: list[EnumWithParent] = Field(..., title="地理位置")
    categories: list[EnumWithParent] = Field(..., title="类别")
    emmission_types: list[EnumSchema] = Field(..., title="排放类型")
    output_types: list[EnumSchema] = Field(..., title="输出类型")
    flow_types: list[EnumSchema] = Field(..., title="流类型")
    products: list[EnumSchema] = Field(..., title="产品")
    life_cycle_stages: list[EnumSchema] = Field(..., title="生命周期阶段")
    source_types: list[EnumSchema] = Field(..., title="数据来源")
    custom_factor_sources: list[EnumSchema] = Field(..., title="自定义因子来源")
    allocation_methods: list[EnumSchema] = Field(..., title="分配方法")
    user_certification_status: list[EnumSchema] = Field(..., title="用户认证状态")
    user_types: list[EnumSchema] = Field(..., title="用户类型")
    license_types: list[EnumSchema] = Field(..., title="许可类型")
    resource_category_groups: list[EnumSchema] = Field(..., title="资源库大类")
    resource_types: list[EnumSchema] = Field(..., title="资源库类型")
    standard_scopes: list[EnumSchema] = Field(..., title="标准范围")
    implementation_statuses: list[EnumSchema] = Field(..., title="实施状态")
    information_statuses: list[EnumSchema] = Field(..., title="资料库内容状态")
