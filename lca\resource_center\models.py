from django.contrib.postgres.fields import ArrayField
from django.db import models
from django.utils.translation import gettext_lazy as __
from django.utils.translation import gettext as _

from lca.accounting.models import Category
from lca.file.models import File


class ResourceCategoryGroup(models.TextChoices):
    """政策信息大类"""

    POLICY_NEWS = "POLICY_NEWS", _("政策资讯")
    CARBON_RESOURCE = "CARBON_RESOURCE", _("碳资料库")


class ResourceType(models.TextChoices):
    """
    政策信息类型枚举
    """

    INFO_ANNOUNCEMENT = "INFO_ANNOUNCEMENT", _("信息公告")
    DOMESTIC_CARBON_NEWS = "DOMESTIC_CARBON_NEWS", _("国内碳资讯")
    INTERNATIONAL_CARBON_NEWS = "INTERNATIONAL_CARBON_NEWS", _("国外碳资讯")
    RESEARCH_REPORT = "RESEARCH_REPORT", _("研究报告")
    STANDARD_SPECIFICATION = "STANDARD_SPECIFICATION", _("标准规范")


class StandardScope(models.TextChoices):
    """
    标准范围枚举
    """

    INTERNATIONAL = "INTERNATIONAL", _("国际标准")
    NATIONAL = "NATIONAL", _("国家标准")
    INDUSTRY = "INDUSTRY", _("行业标准")
    LOCAL = "LOCAL", _("地方标准")
    ASSOCIATION = "ASSOCIATION", _("团体标准")


class ImplementationStatus(models.TextChoices):
    """
    实施状态枚举
    """

    CURRENT = "CURRENT", _("现行")
    TRIAL = "TRIAL", _("试行")
    DRAFTING = "DRAFTING", _("编制中")


class InformationStatus(models.TextChoices):
    """
    内容状态枚举
    """

    DRAFT = "DRAFT", _("草稿")
    PUBLISHED = "PUBLISHED", _("已发布")


class ResourceCenter(models.Model):
    """
    资源中心，包含以下五种类型的内容：
    - 信息公告
    - 国内碳资讯
    - 国外碳资讯
    - 研究报告
    - 标准规范
    """

    # ----------------------
    # 公共字段（所有类型都使用）
    # ----------------------

    title = models.CharField(
        max_length=255,
        verbose_name=__("标题"),
        db_comment="内容的标题",
    )
    summary = models.TextField(
        blank=True,
        null=True,
        verbose_name=__("简介"),
        db_comment="内容摘要，用于列表页展示",
    )
    content = models.TextField(verbose_name=__("内容"), db_comment="内容正文文本")
    publish_date = models.DateField(verbose_name=__("发布日期"), db_comment="内容发布的原始日期，格式为YYYY-MM-DD")
    publish_organization = models.CharField(
        max_length=255,
        verbose_name=__("发布组织"),
        db_comment="内容的发布单位或机构名称",
    )
    category = models.ForeignKey(
        Category,
        blank=True,
        null=True,
        on_delete=models.PROTECT,
        verbose_name=__("相关行业"),
        db_comment="该内容涉及的主要行业领域",
        related_name="information_category",
    )
    cover_image = models.ForeignKey(
        File,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=__("宣传封面"),
        db_comment="内容展示用封面图片",
        related_name="information_cover_image",
    )
    file_upload = models.ForeignKey(
        File,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=__("文件上传"),
        db_comment="附加文档，如PDF、Word等",
        related_name="information_file_upload",
    )
    article_link = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=__("文章链接"),
        db_comment="指向外部原文的链接",
    )
    category_group = models.CharField(
        max_length=50,
        choices=ResourceCategoryGroup.choices,
        verbose_name=__("分类组别"),
        db_comment="用于区分政策资讯或碳资料库",
    )
    content_type = models.CharField(
        max_length=50,
        choices=ResourceType.choices,
        verbose_name=__("内容类型"),
        db_comment="区分内容类型的枚举值",
    )
    status = models.CharField(
        max_length=50,
        choices=InformationStatus.choices,
        default=InformationStatus.DRAFT,
        verbose_name=__("状态"),
        db_comment="内容状态，如草稿或已发布",
    )
    publish_time = models.DateTimeField(null=True, blank=True, verbose_name=__("发布时间"), db_comment="内容发布时间")
    unpublish_time = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=__("下架时间"),
        db_comment="内容下架时间，用于临时下架内容",
    )
    create_time = models.DateTimeField(verbose_name="创建时间", auto_now_add=True, db_index=True)
    update_time = models.DateTimeField(verbose_name="更新时间", auto_now=True, db_index=True)

    # ----------------------
    # 标准规范专用字段（其他类型为空）
    # ----------------------

    standard_number = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name=__("标准号"),
        db_comment="标准的唯一编号，如GB/T 12345-2020",
    )
    standard_scope = models.CharField(
        max_length=50,
        choices=StandardScope.choices,
        null=True,
        blank=True,
        verbose_name=__("标准范围"),
        db_comment="标准适用范围描述",
    )
    applicable_area = ArrayField(
        models.CharField(
            max_length=50,
            verbose_name=__("适用区域"),
            db_comment="标准实施的地理或行业范围",
        ),
        null=True,
        blank=True,
        db_comment="标准实施的地理或行业范围（多个值）",
    )
    implementation_status = models.CharField(
        max_length=50,
        choices=ImplementationStatus.choices,
        null=True,
        blank=True,
        verbose_name=__("实施状态"),
        db_comment="标准当前状态",
    )
    implementation_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=__("实施日期"),
        db_comment="标准正式实施的日期",
    )

    class Meta:
        verbose_name = __("政策信息库")
        verbose_name_plural = __("政策信息库")
        db_table_comment = "政策信息库，包含信息公告、碳资讯、研究报告、标准规范等内容"
