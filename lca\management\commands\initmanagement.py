from django.core.management import BaseCommand
import csv
from django.db import transaction

from lca.accounting.models import EmissionSource
from lca.database.models import (
    EmissionSourceManagement,
)


class Command(BaseCommand):
    help = "初始管理信息"

    def handle(self, *args, **options):
        run()


def run():
    with open("lca/management/commands/data/management.csv", "r", encoding="utf-8") as file:
        reader = csv.DictReader(file)
        with transaction.atomic():
            for row in reader:
                print(row["因子"])
                emission_source = EmissionSource.objects.get(name=row["因子"])
                item = EmissionSourceManagement.objects.filter(emission_source=emission_source).first()
                data = dict(
                    emission_source=emission_source,
                    generate_version=row["数据生成版本"],
                    generate_contact=row["数据生成联系人"],
                    generate_contact_detail=row["数据生成联系方式"],
                    generate_create_time=row["数据生成时间"],
                    generate_update_time=row["数据生成更新时间"],
                    inputer_contact=row["数据录入者"],
                    input_create_time=row["数据录入时间"],
                    input_contact_detail=row["数据录入联系方式"],
                    approve_contact=row["审核人"],
                    approve_contact_detail=row["审核人联系方式"],
                    approve_create_time=row["审核时间"],
                    update_major_count=row["大修次数"],
                    update_minor_count=row["小修次数"],
                    publish=row["发布信息"],
                    view=row["访问限制"],
                    owener=row["数据集拥有者"],
                    owener_version=row["数据集拥有者版本"],
                    license_type=row["许可类型"],
                    copyright=row["版权"],
                )
                if item is None:
                    item = EmissionSourceManagement()
                for key, value in data.items():
                    setattr(item, key, value)
                item.save()
