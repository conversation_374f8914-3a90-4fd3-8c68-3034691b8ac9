from django.db import models


class BaseModel(models.Model):
    create_time = models.DateTimeField(verbose_name="创建时间", auto_now_add=True)
    update_time = models.DateTimeField(verbose_name="更新时间", auto_now=True)

    def parse_payload_dict(self, payload: dict):
        for key, val in payload.items():
            if hasattr(self, key):
                setattr(self, key, val)

    class Meta:
        abstract = True


class ApprovalStatus(models.IntegerChoices):
    """审核状态"""

    APPROVING = 1, "审核中"
    APPROVED = 2, "审核通过"
    REJECTED = 3, "审核未通过"
