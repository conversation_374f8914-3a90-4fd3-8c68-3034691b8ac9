from typing import List

from ztbory_django_shield.core.options import DjangoOptions


class Options(DjangoOptions):
    """密码防护配置"""

    DEFAULT_REGEX = "(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d!\\\"#$%&'()*+,-./:;<=>?@\\\\\\]\\[^_`{|}~]"

    def __init__(
        self,
        complex_content_regex=DEFAULT_REGEX,
        complex_min_length: int = 8,
        complex_max_length: int = 32,
        complex_tips_template: str = (
            "密码应包含{complex_min_length}-{complex_max_length}位字符，要求同时具备英文大写、小写、阿拉伯数字"
        ),
        incorrect_ban_with_ip: bool = True,
        incorrect_max_times: int = 6,
        incorrect_ban_seconds: int = 60 * 60,
        incorrect_trigger_two_factor_times: int = 0,
        incorrect_trigger_tow_factor_managers: List[str] = None,
        incorrect_tips_template: str = (
            "登录失败, "
            "失败{incorrect_max_times}次账户将会锁定{incorrect_ban_duration}，"
            "目前错误次数：{incorrect_times}."
        ),
        incorrect_baned_tips_template: str = (
            "您的账户因多次密码输入错误已被暂时锁定，{incorrect_ban_duration}后重新登录。"
        ),
    ):
        """

        :param complex_content_regex: 密码正则表达式
        :param complex_min_length: 密码最小长度
        :param complex_max_length: 密码最大长度
        :param complex_tips_template: 密码不符合要求时提示语模板
        :param incorrect_ban_with_ip: 账户锁定是否考虑ip True-用户名+IP False-用户名
        :param incorrect_max_times: 密码输入错误最大次数
        :param incorrect_ban_seconds: 账户锁定时长 (s)
        :param incorrect_trigger_two_factor_times: 触发双因子校验的错误次数
        :param incorrect_trigger_tow_factor_managers: 触发双因子校验的管理员用户名
        :param incorrect_tips_template: 账户登录错误时提示语模板
        :param incorrect_baned_tips_template: 账户登录锁定时提示语模板
        """
        self.complex_min_length = complex_min_length
        self.complex_max_length = complex_max_length
        self.complex_content_regex = complex_content_regex
        self.complex_tips_template = complex_tips_template

        self.incorrect_ban_with_ip = incorrect_ban_with_ip
        self.incorrect_max_times = incorrect_max_times
        self.incorrect_ban_seconds = incorrect_ban_seconds
        self.incorrect_trigger_two_factor_times = incorrect_trigger_two_factor_times
        self.incorrect_trigger_tow_factor_managers = incorrect_trigger_tow_factor_managers or list()
        self.incorrect_tips_template = incorrect_tips_template
        self.incorrect_baned_tips_template = incorrect_baned_tips_template

        self.load_django_settings("password")

    @property
    def complex_regex(self):
        """完整的密码校验正则表达式"""
        regex = self.complex_content_regex
        if regex is None:
            regex = self.DEFAULT_REGEX
        return r"^%s{%d,%d}$" % (
            regex,
            self.complex_min_length,
            self.complex_max_length,
        )

    @property
    def complex_tips(self) -> str:
        """密码不符合要求时提示语"""
        return self.complex_tips_template.format(
            complex_min_length=self.complex_min_length,
            complex_max_length=self.complex_max_length,
        )
