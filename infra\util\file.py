import base64

import magic
import requests
from django.conf import settings

from account.infra.exceptions import BadRequest, ServiceUnavailable


def get_or_upload_file(data: str) -> str:
    """
    如果data是url直接返回，否则当成base64数据解码、上传文件到oss,返回文件url
    Args:
        data: 文件url或base64数据
    """
    if not data:
        return data
    if data.lower().startswith("http"):
        return data
    else:
        try:
            content = base64.b64decode(data)
        except Exception:
            raise BadRequest("无效的base64数据")
        # upload to oss
        mime_types = magic.from_buffer(content, mime=True)
        extension = mime_types.split("/")[-1]
        files = {"files": ("filename." + extension, content)}  # 提供文件扩展名，不然oss不知道什么格式
        resp = requests.post(settings.OSS_HOST, files=files, data={"mkdir": settings.OSS_BUCKET})
        if not resp.ok or (result := resp.json())["code"] != 1:
            raise ServiceUnavailable("图片服务不可用")
        else:
            return result["data"]
