# Generated by Django 5.2.1 on 2025-07-23 05:58

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('lca', '0089_rename_import_status_database_status_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='EmissionSourceApplication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='名称')),
                ('amount', models.DecimalField(decimal_places=5, max_digits=20, verbose_name='数量')),
                ('source', models.CharField(max_length=255, verbose_name='数据来源')),
                ('year', models.CharField(max_length=255, verbose_name='时间代表性')),
                ('reason', models.TextField(blank=True, max_length=500, null=True, verbose_name='审核未通过的原因')),
                ('approve_time', models.DateTimeField(blank=True, null=True, verbose_name='审核时间')),
                ('status', models.CharField(choices=[('ongoing', '审核中'), ('approved', '通过'), ('rejected', '未通过')], default='ongoing', max_length=255, verbose_name='状态')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('approver', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='emission_source_approve_applications', to=settings.AUTH_USER_MODEL, verbose_name='审核者')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='lca.category', verbose_name='类别')),
                ('creator', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='emission_source_applications', to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
                ('emission_source', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='lca.emissionsource', verbose_name='排放源')),
                ('geography', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='lca.geography', verbose_name='适用的地理区域')),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='lca.unit', verbose_name='单位')),
            ],
        ),
        migrations.CreateModel(
            name='EmissionSourceDataSetApplication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ('name', models.CharField(max_length=255, unique=True, verbose_name='名称')),
                ('alias', models.CharField(max_length=255, null=True, verbose_name='别名')),
                ('year', models.CharField(max_length=255, verbose_name='时间代表性')),
                ('amount', models.DecimalField(decimal_places=5, max_digits=20, verbose_name='数量')),
                ('functional_unit', models.CharField(max_length=100, verbose_name='功能单位')),
                ('specs', models.CharField(max_length=100, null=True, verbose_name='产品型号')),
                ('boundary', models.CharField(choices=[('cradle-gate', '从摇篮到大门'), ('cradle-grave', '从摇篮到坟墓')], max_length=50, verbose_name='系统边界')),
                ('technical_description', models.CharField(max_length=255, null=True, verbose_name='技术描述')),
                ('usage', models.CharField(max_length=255, null=True, verbose_name='产品或工艺用途')),
                ('allocation_principles', models.CharField(blank=True, max_length=255, null=True, verbose_name='分配原则')),
                ('model_description', models.CharField(blank=True, max_length=255, null=True, verbose_name='模型描述')),
                ('data_treatment', models.CharField(blank=True, max_length=255, null=True, verbose_name='数据处理')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='lca.category', verbose_name='类别')),
                ('emission_source', models.OneToOneField(blank=True, on_delete=django.db.models.deletion.CASCADE, to='lca.emissionsourceapplication', verbose_name='排放源')),
                ('flow_chart', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='lca.file', verbose_name='工艺流程图')),
                ('geography', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='lca.geography', verbose_name='适用的地理区域')),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='lca.unit', verbose_name='单位')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='EmissionSourceInputOutputApplication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=5, max_digits=20, verbose_name='数量')),
                ('inputs', models.JSONField(verbose_name='输入')),
                ('outputs', models.JSONField(verbose_name='输出')),
                ('emission_source', models.ForeignKey(blank=True, on_delete=django.db.models.deletion.CASCADE, to='lca.emissionsourceapplication', verbose_name='排放源')),
                ('flow', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='lca.flow', verbose_name='流')),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='lca.unit', verbose_name='单位')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='EmissionSourceManagementApplication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('generate_version', models.CharField(max_length=50, verbose_name='数据生成版本')),
                ('generate_contact', models.CharField(max_length=50, verbose_name='数据生成联系人')),
                ('generate_contact_detail', models.CharField(max_length=50, verbose_name='数据生成联系方式')),
                ('generate_create_time', models.DateTimeField(auto_now_add=True, verbose_name='数据生成时间')),
                ('generate_update_time', models.DateTimeField(auto_now=True, verbose_name='数据生成更新时间')),
                ('inputer_contact', models.CharField(blank=True, max_length=50, null=True, verbose_name='数据录入者')),
                ('input_create_time', models.DateTimeField(auto_now_add=True, verbose_name='数据录入时间')),
                ('input_contact_detail', models.CharField(max_length=50, verbose_name='数据录入联系方式')),
                ('approve_contact', models.CharField(blank=True, max_length=50, null=True, verbose_name='数据审核者联系方式')),
                ('approve_contact_detail', models.CharField(max_length=50, verbose_name='数据审核联系方式')),
                ('approve_create_time', models.DateTimeField(auto_now_add=True, verbose_name='数据审核时间')),
                ('update_major_count', models.IntegerField(default=0, verbose_name='大修次数')),
                ('update_minor_count', models.IntegerField(default=0, verbose_name='小修次数')),
                ('publish', models.CharField(max_length=255, null=True, verbose_name='发布信息')),
                ('view', models.CharField(max_length=255, null=True, verbose_name='访问权限信息')),
                ('owener', models.CharField(blank=True, max_length=50, null=True, verbose_name='数据拥有者')),
                ('owener_version', models.CharField(max_length=50, verbose_name='数据拥有者版本')),
                ('license_type', models.CharField(max_length=255, null=True, verbose_name='许可类型')),
                ('copyright', models.CharField(max_length=255, null=True, verbose_name='版权信息')),
                ('emission_source', models.OneToOneField(blank=True, on_delete=django.db.models.deletion.CASCADE, to='lca.emissionsourceapplication', verbose_name='排放源')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='EmissionSourceOutputApplication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=5, max_digits=20, verbose_name='数量')),
                ('description', models.CharField(max_length=255, null=True, verbose_name='描述')),
                ('emission_source', models.ForeignKey(blank=True, on_delete=django.db.models.deletion.CASCADE, to='lca.emissionsourceapplication', verbose_name='排放源')),
                ('flow', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='lca.flow', verbose_name='流')),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='lca.unit', verbose_name='单位')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
