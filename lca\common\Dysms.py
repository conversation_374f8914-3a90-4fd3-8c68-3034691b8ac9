# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.

# from alibabacloud_dysmsapi20170525.client import Client as Dysmsapi20170525Client
# from alibabacloud_credentials.client import Client as CredentialClient
# from alibabacloud_tea_openapi import models as open_api_models
# from alibabacloud_dysmsapi20170525 import models as dysmsapi_20170525_models
# from config import settings
from infra.types import Phone


class Dysms:
    def __init__(self):
        pass

    # def create_client(self) -> Dysmsapi20170525Client:
    #     credential = CredentialClient()
    #     config = open_api_models.Config(
    #         credential=credential
    #     )
    #     # Endpoint 请参考 https://api.aliyun.com/product/Dysmsapi
    #     config.endpoint = 'dysmsapi.aliyuncs.com'
    #     return Dysmsapi20170525Client(config)

    def send_sms(self, phone: Phone, template_code: str, param: dict = None) -> None:
        # TODO
        return
        # client = self.create_client()
        # send_sms_request = dysmsapi_20170525_models.SendSmsRequest(
        #     phone_numbers=phone,
        #     sign_name=settings.ALIBABA_CLOUD_SMS_SIGN,
        #     template_code=template_code,
        #     template_param=json.dumps(param),
        # )
        # response = client.send_sms(send_sms_request)
        # print(response.body)
