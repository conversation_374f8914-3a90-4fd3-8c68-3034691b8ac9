# Generated by Django 5.2.1 on 2025-07-24 08:42

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('lca', '0097_emissionsourceoutputapplication_allocation_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='emissionsourcedataset',
            name='source',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='数据来源'),
        ),
        migrations.AddField(
            model_name='emissionsourcedatasetapplication',
            name='source',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='数据来源'),
        ),
        migrations.AlterField(
            model_name='emissionsourcedatasetapplication',
            name='emission_source',
            field=models.OneToOneField(blank=True, on_delete=django.db.models.deletion.CASCADE, related_name='dataset', to='lca.emissionsourceapplication', verbose_name='排放源'),
        ),
        migrations.AlterField(
            model_name='emissionsourceinputapplication',
            name='emission_source',
            field=models.ForeignKey(blank=True, on_delete=django.db.models.deletion.CASCADE, related_name='inputs', to='lca.emissionsourceapplication', verbose_name='排放源'),
        ),
        migrations.AlterField(
            model_name='emissionsourcemanagementapplication',
            name='emission_source',
            field=models.OneToOneField(blank=True, on_delete=django.db.models.deletion.CASCADE, related_name='management', to='lca.emissionsourceapplication', verbose_name='排放源'),
        ),
        migrations.AlterField(
            model_name='emissionsourceoutputapplication',
            name='emission_source',
            field=models.ForeignKey(blank=True, on_delete=django.db.models.deletion.CASCADE, related_name='outputs', to='lca.emissionsourceapplication', verbose_name='排放源'),
        ),
    ]
