# Generated by Django 5.2.1 on 2025-06-04 06:50

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0040_model_plateform"),
    ]

    operations = [
        migrations.CreateModel(
            name="Flow",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "uuid",
                    models.UUIDField(default=uuid.uuid4, editable=False, unique=True),
                ),
                (
                    "name",
                    models.CharField(max_length=200, unique=True, verbose_name="名称"),
                ),
            ],
        ),
        migrations.CreateModel(
            name="EmissionSourceDataSet",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "uuid",
                    models.UUIDField(default=uuid.uuid4, editable=False, unique=True),
                ),
                (
                    "name",
                    models.CharField(max_length=255, unique=True, verbose_name="名称"),
                ),
                (
                    "alias",
                    models.Char<PERSON>ield(max_length=255, null=True, verbose_name="别名"),
                ),
                ("year", models.CharField(max_length=255, verbose_name="时间代表性")),
                (
                    "amount",
                    models.DecimalField(decimal_places=5, max_digits=20, verbose_name="数量"),
                ),
                (
                    "functional_unit",
                    models.CharField(max_length=100, verbose_name="功能单位"),
                ),
                (
                    "specs",
                    models.CharField(max_length=100, null=True, verbose_name="产品型号"),
                ),
                (
                    "boundary",
                    models.CharField(
                        choices=[
                            ("cradle-gate", "摇篮到大门（从资源开采到产品出厂）"),
                            ("cradle-grave", "摇篮到坟墓（从资源开采到产品废弃）"),
                        ],
                        max_length=50,
                        verbose_name="系统边界",
                    ),
                ),
                (
                    "technical_description",
                    models.CharField(max_length=255, null=True, verbose_name="技术描述"),
                ),
                (
                    "usage",
                    models.CharField(max_length=255, null=True, verbose_name="产品或工艺用途"),
                ),
                (
                    "allocation_principles",
                    models.CharField(blank=True, max_length=255, null=True, verbose_name="分配原则"),
                ),
                (
                    "model_description",
                    models.CharField(blank=True, max_length=255, null=True, verbose_name="模型描述"),
                ),
                (
                    "data_treatment",
                    models.CharField(blank=True, max_length=255, null=True, verbose_name="数据处理"),
                ),
                (
                    "category",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.category",
                        verbose_name="类别",
                    ),
                ),
                (
                    "emmission_source",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.emissionsource",
                        verbose_name="排放源",
                    ),
                ),
                (
                    "flow_chart",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        to="lca.file",
                        verbose_name="工艺流程图",
                    ),
                ),
                (
                    "geography",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.geography",
                        verbose_name="适用的地理区域",
                    ),
                ),
                (
                    "unit",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.unit",
                        verbose_name="单位",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="EmissionSourceManagement",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "generate_version",
                    models.CharField(max_length=50, verbose_name="数据生成版本"),
                ),
                (
                    "generate_contact",
                    models.CharField(max_length=50, verbose_name="数据生成联系人"),
                ),
                (
                    "generate_phone",
                    models.CharField(max_length=50, verbose_name="数据生成联系方式"),
                ),
                (
                    "generate_create_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="数据生成时间"),
                ),
                (
                    "generate_update_time",
                    models.DateTimeField(auto_now=True, verbose_name="数据生成更新时间"),
                ),
                (
                    "inputer_contact",
                    models.CharField(blank=True, max_length=50, null=True, verbose_name="数据录入者"),
                ),
                (
                    "input_create_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="数据录入时间"),
                ),
                (
                    "input_phone",
                    models.CharField(max_length=50, verbose_name="数据录入联系方式"),
                ),
                (
                    "approver_contact",
                    models.CharField(
                        blank=True,
                        max_length=50,
                        null=True,
                        verbose_name="数据审核者联系方式",
                    ),
                ),
                (
                    "approve_create_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="数据审核时间"),
                ),
                (
                    "update_major_count",
                    models.IntegerField(default=0, verbose_name="大修次数"),
                ),
                (
                    "update_minor_count",
                    models.IntegerField(default=0, verbose_name="小修次数"),
                ),
                (
                    "publish",
                    models.CharField(max_length=255, null=True, verbose_name="发布信息"),
                ),
                (
                    "view",
                    models.CharField(max_length=255, null=True, verbose_name="访问权限信息"),
                ),
                (
                    "owener",
                    models.CharField(blank=True, max_length=50, null=True, verbose_name="数据拥有者"),
                ),
                (
                    "owener_version",
                    models.CharField(max_length=50, verbose_name="数据拥有者版本"),
                ),
                (
                    "license_type",
                    models.CharField(max_length=255, null=True, verbose_name="许可类型"),
                ),
                (
                    "copyright",
                    models.CharField(max_length=255, null=True, verbose_name="版权信息"),
                ),
                (
                    "emmission_source",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.emissionsource",
                        verbose_name="排放源",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="EmissionSourceProduct",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "product",
                    models.CharField(
                        choices=[
                            ("cement", "水泥"),
                            ("steel", "钢铁"),
                            ("photovoltaic", "光伏"),
                            ("electric_vehicle", "电动汽车"),
                            ("power_battery", "动力电池"),
                            ("glass", "玻璃"),
                            ("electrolytic_aluminum", "电解铝"),
                            ("ethylene", "乙烯"),
                            ("synthetic_ammonia", "合成氨"),
                        ],
                        db_index=True,
                        max_length=255,
                        verbose_name="产品",
                    ),
                ),
                (
                    "emmission_source",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.emissionsource",
                        verbose_name="排放源",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="EmissionSourceInput",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(decimal_places=5, max_digits=20, verbose_name="数量"),
                ),
                (
                    "emission_source",
                    models.OneToOneField(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.emissionsource",
                        verbose_name="排放源",
                    ),
                ),
                (
                    "related_emission_source",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="related_emission_source",
                        to="lca.emissionsource",
                        verbose_name="关联的排放源",
                    ),
                ),
                (
                    "unit",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.unit",
                        verbose_name="单位",
                    ),
                ),
                (
                    "flow",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="flows",
                        to="lca.flow",
                        verbose_name="流",
                    ),
                ),
            ],
        ),
    ]
