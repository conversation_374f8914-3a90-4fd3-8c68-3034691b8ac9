# Generated by Django 5.2.1 on 2025-05-19 02:51

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0036_emissionsource_co2e_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="certificationinfo",
            name="certification",
        ),
        migrations.AddField(
            model_name="certification",
            name="info",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="lca.certificationinfo",
                verbose_name="认证详情",
            ),
        ),
        migrations.AlterField(
            model_name="file",
            name="process_flow",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="process_flows",
                to="lca.certification",
                verbose_name="产品工艺流程图",
            ),
        ),
    ]
