# Generated by Django 5.2.1 on 2025-05-19 01:33

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0035_remove_manufacturer_geography_manufacturer_region"),
    ]

    operations = [
        migrations.AddField(
            model_name="emissionsource",
            name="co2e",
            field=models.DecimalField(decimal_places=5, default=0, max_digits=20, verbose_name="CO2e"),
        ),
        migrations.AlterField(
            model_name="certification",
            name="application_finish_time",
            field=models.DateTimeField(db_index=True, null=True, verbose_name="受理完成时间"),
        ),
        migrations.AlterField(
            model_name="certification",
            name="certification_finish_time",
            field=models.DateTimeField(db_index=True, null=True, verbose_name="认证完成时间"),
        ),
        migrations.CreateModel(
            name="EmmissionSourceInput",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(decimal_places=5, max_digits=20, verbose_name="数量"),
                ),
                (
                    "emmission_source",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="finputs",
                        to="lca.emissionsource",
                        verbose_name="排放源",
                    ),
                ),
                (
                    "input",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="foutputs",
                        to="lca.emissionsource",
                        verbose_name="输入",
                    ),
                ),
                (
                    "unit",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.unit",
                        verbose_name="单位",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="EmmissionSourceProduct",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "product",
                    models.CharField(
                        choices=[
                            ("lithium_battery", "锂电池"),
                            ("photovoltaic_product", "光伏产品"),
                            ("steel", "钢铁"),
                            ("textile", "纺织品"),
                            ("electronics", "电子电器"),
                            ("tire", "轮胎"),
                            ("cement", "水泥"),
                            ("electrolytic_aluminum", "电解铝"),
                            ("phosphate_fertilizer", "磷铵"),
                            ("wood_product", "木制品"),
                            ("electric_vehicle", "电动汽车"),
                            ("power_battery", "动力电池"),
                            ("glass", "玻璃"),
                            ("ethylene", "乙烯"),
                            ("synthetic_ammonia", "合成氨"),
                        ],
                        db_index=True,
                        max_length=255,
                        verbose_name="产品",
                    ),
                ),
                (
                    "emmission_source",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.emissionsource",
                        verbose_name="排放源",
                    ),
                ),
            ],
        ),
    ]
