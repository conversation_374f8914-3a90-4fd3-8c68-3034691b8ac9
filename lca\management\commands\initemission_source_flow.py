from django.core.management import BaseCommand
import csv
from django.db import transaction

from lca.accounting.models import EmissionSource
from lca.database.models import (
    EmissionSourceInput,
    EmissionSourceOutput,
    Flow,
)


class Command(BaseCommand):
    help = "初始化因子输入输出"

    def handle(self, *args, **options):
        run()


def run():
    with open("lca/management/commands/data/emission_source_flow.csv", "r", encoding="utf-8") as file:
        reader = csv.DictReader(file)
        with transaction.atomic():
            esids = set([])  # 已经删除的id

            for row in reader:
                print(row["因子"])
                emission_source = EmissionSource.objects.get(name=row["因子"])
                if emission_source.id not in esids:
                    EmissionSourceInput.objects.filter(emission_source=emission_source).delete()
                    EmissionSourceOutput.objects.filter(emission_source=emission_source).delete()
                    esids.add(emission_source.id)
                flow = Flow.objects.get(name=row["流"])

                if row["方向"] == "输入":
                    input(emission_source, flow, row)
                else:
                    output(emission_source, flow, row)


def input(emission_source: EmissionSource, flow: Flow, row):
    related_emission_source = None
    if row["关联因子"] != "" and row["关联因子"] is not None:
        related_emission_source = EmissionSource.objects.get(name=row["关联因子"])
    data = dict(
        flow=flow,
        emission_source=emission_source,
        amount=row["数量"],
        unit_id=row["单位"],
        related_emission_source=related_emission_source,
    )

    item = EmissionSourceInput()
    for key, value in data.items():
        setattr(item, key, value)
    item.save()


def output(emission_source: EmissionSource, flow: Flow, row):
    data = dict(
        flow=flow,
        emission_source=emission_source,
        amount=row["数量"],
        unit_id=row["单位"],
        description=row["说明"],
    )

    item = EmissionSourceOutput()
    for key, value in data.items():
        setattr(item, key, value)
    item.save()
