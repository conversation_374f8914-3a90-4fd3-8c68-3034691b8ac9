from datetime import timedelta
from django.db import models
from lca.accounting.models import CERTIFICATE_PRODUCT, PRODUCT, Model
from lca.common.models import Region
from lca.file.models import File
from django.contrib.postgres.fields import ArrayField
from django.utils import timezone


class CERTIFICATION_APPLICATION_STATUS(models.TextChoices):
    """认证受理状态"""

    WAITING = "waiting", "等待受理"
    ACCEPTED = "accepted", "已受理"
    REJECTED = "rejected", "拒绝受理"
    WITHDRAWN = "withdrawn", "已撤回申请"


class SUPERVISION_STATUS(models.TextChoices):
    """监督管理状态"""

    WATING = "waiting", "待监督"
    ONGOING = "ongoing", "监督中"
    COMPLETED = "completed", "监督完成"


class CERTIFICATION_LIFE_CYCLES(models.TextChoices):
    """认证生命周期"""

    RAW_MATERIALS_AND_ENERGY = "raw-materials-and-energy", "原材料和能源获取阶段"
    PRODUCTION = "production", "生产阶段"
    USE = "use", "使用阶段"
    DISTRIBUTION = "distribution", "分销阶段"
    DISPOSAL = "disposal", "废弃阶段"


class CERTIFICATION_STATUS(models.TextChoices):
    """认证状态"""

    WAITING = "waiting", "等待认证"
    ONGOING = "ongoing", "认证中"
    APPROVED = "approved", "已通过"
    REJECTED = "rejected", "未通过"


class CERTIFICATION_STEPS(models.TextChoices):
    SUBMISSION = "submission", "发起认证申请"
    ACCEPTANCE_REVIEW = "acceptance-review", "认证受理评审"
    PLAN = "plan", "制定认证方案"
    DOCUMENT_REVIEW = "document-review", "开展文件评审"
    ONSITE_INSPECTION = "onsite-inspection", "开展现场检查"
    CARBON_FOOTPRINT_VERIFICATION = "carbon-footprint-verification", "碳足迹核查"
    VERIFICATION_REPORT_ISSUANCE = "verification-report-issuance", "出具核查报告"
    REVIEW_DECISION = "review-decision", "复合与认证决定"
    CERTIFICATE_ISSUANCE = "certificate-issuance", "发放证书及标识"
    SUPERVISION = "supervision", "获证后监督"


class CERTIFICATION_STEP_STATUS(models.TextChoices):
    """认证步骤状态"""

    WAITING = "waiting", "等待"
    ONGOING = "ongoing", "进行中"
    COMPLETED = "completed", "已完成"
    REJECTED = "rejected", "已拒绝"
    WITHDROWN = "withdrawn", "已撤回"


class Manufacturer(models.Model):
    """企业"""

    user = models.OneToOneField(
        "lca.User",
        on_delete=models.PROTECT,
        verbose_name="用户",
        null=True,
        blank=True,
        related_name="manufacturer",
    )
    name = models.CharField(max_length=100, verbose_name="企业名称", null=False)
    code = models.CharField(max_length=100, verbose_name="统一社会信用代码", null=False)
    geography = models.ForeignKey(
        "lca.Geography",
        on_delete=models.PROTECT,
        verbose_name="地区",
        null=False,
        blank=False,
        related_name="manufacturer_geography3",
    )
    contact_name = models.CharField(max_length=100, verbose_name="联系人", null=False)
    contact_phone = models.CharField(max_length=100, verbose_name="联系人手机", null=False)
    contact_email = models.CharField(max_length=100, verbose_name="联系人邮箱", null=False)
    address = models.CharField(max_length=100, verbose_name="企业地址", null=False)
    products = models.CharField(max_length=100, verbose_name="拟认证产品", null=False)
    description = models.TextField(max_length=500, verbose_name="企业主营业务简介", null=False)
    website = models.CharField(max_length=100, verbose_name="企业官网", null=True)
    license = models.ForeignKey(File, on_delete=models.PROTECT, verbose_name="营业执照", null=True)


class Certification(models.Model):
    """
    认证记录表
    """

    manufacturer = models.ForeignKey(
        Manufacturer,
        on_delete=models.PROTECT,
        verbose_name="生产企业",
        null=False,
        blank=False,
        related_name="certification",
    )
    authority = models.ForeignKey(
        "Authority",
        on_delete=models.PROTECT,
        verbose_name="认证机构",
        null=False,
        blank=False,
        related_name="certification",
    )
    model = models.ForeignKey(Model, on_delete=models.PROTECT, null=True, blank=True, verbose_name="模型历史")

    application_status = models.CharField(
        max_length=100,
        db_index=True,
        null=False,
        blank=False,
        choices=CERTIFICATION_APPLICATION_STATUS.choices,
        verbose_name="受理状态",
    )
    certification_status = models.CharField(
        max_length=100,
        db_index=True,
        null=True,
        blank=False,
        choices=CERTIFICATION_STATUS.choices,
        verbose_name="认证状态",
    )
    supervision_status = models.CharField(
        max_length=100,
        db_index=True,
        null=True,
        blank=False,
        choices=SUPERVISION_STATUS.choices,
        verbose_name="监督管理状态",
    )
    reason = models.TextField(max_length=500, verbose_name="认证申请被拒绝的原因", null=True, blank=True)

    category = models.ForeignKey(
        "lca.Category",
        on_delete=models.PROTECT,
        verbose_name="产品类别",
        null=False,
        blank=False,
        default="C",
    )
    principal = models.CharField(max_length=100, null=False, blank=False, verbose_name="委托人")
    principal_address = models.CharField(max_length=100, null=False, blank=False, verbose_name="委托人地址")
    manufacturer_name = models.CharField(max_length=100, null=False, blank=False, verbose_name="生产企业名称")
    manufacturer_address = models.CharField(max_length=100, null=False, blank=False, verbose_name="生产企业地址")
    producer_name = models.CharField(max_length=100, null=False, blank=False, verbose_name="生产者（制造商）名称")
    producer_address = models.CharField(max_length=100, null=False, blank=False, verbose_name="生产者（制造商）地址")
    contact_name = models.CharField(max_length=100, null=False, blank=False, verbose_name="联系人")
    contact_phone = models.CharField(max_length=100, null=False, blank=False, verbose_name="联系人手机")
    summary = models.TextField(max_length=500, null=False, blank=False, verbose_name="企业概况")
    product_name = models.CharField(max_length=100, null=False, blank=False, verbose_name="产品名称")
    product_description = models.TextField(max_length=500, null=False, blank=False, verbose_name="产品介绍")
    product_standard = models.CharField(max_length=100, null=True, blank=True, verbose_name="产品执行标准")
    product_performance = models.CharField(max_length=100, null=True, blank=True, verbose_name="产品性能指标")
    product_function = models.CharField(max_length=500, null=True, blank=True, verbose_name="产品功能")
    production_process = models.TextField(max_length=500, null=True, blank=True, verbose_name="产品生产工艺")

    image = models.ForeignKey(
        File,
        on_delete=models.SET_NULL,
        verbose_name="产品图片",
        null=True,
        blank=True,
        related_name="certification_image",
    )
    business_license = models.ForeignKey(
        File,
        on_delete=models.SET_NULL,
        verbose_name="营业执照",
        null=True,
        blank=True,
        related_name="certification_business_license",
    )
    agency_relationship = models.ForeignKey(
        File,
        on_delete=models.SET_NULL,
        verbose_name="委托关系",
        null=True,
        blank=True,
        related_name="certification_agency_relationship",
    )
    letter_of_authorization = models.ForeignKey(
        File,
        on_delete=models.SET_NULL,
        verbose_name="委托书",
        null=True,
        blank=True,
        related_name="certification_letter_of_authorization",
    )
    intellectual_property = models.ForeignKey(
        File,
        on_delete=models.SET_NULL,
        verbose_name="知识产权关系",
        null=True,
        blank=True,
        related_name="certification_intellectual_property",
    )
    organizational_chart = models.ForeignKey(
        File,
        on_delete=models.SET_NULL,
        verbose_name="生产企业组织机构图",
        null=True,
        blank=True,
        related_name="certification_organizational_chart",
    )
    bom = models.ForeignKey(
        File,
        on_delete=models.SET_NULL,
        verbose_name="原辅材料清单",
        null=True,
        blank=True,
        related_name="certification_bom",
    )

    application_finish_time = models.DateTimeField(verbose_name="受理完成时间", null=True, db_index=True)
    certification_finish_time = models.DateTimeField(verbose_name="认证完成时间", null=True, db_index=True)
    certification_start_time = models.DateTimeField(verbose_name="认证开始时间", null=True, db_index=True)
    create_time = models.DateTimeField(verbose_name="创建时间", auto_now_add=True, db_index=True)
    update_time = models.DateTimeField(verbose_name="更新时间", auto_now=True, db_index=True)

    submission_status = models.CharField(
        max_length=100,
        null=True,
        blank=False,
        choices=CERTIFICATION_STEP_STATUS.choices,
        verbose_name="发起认证申请状态",
    )
    acceptance_review_status = models.CharField(
        max_length=100,
        null=True,
        blank=False,
        choices=CERTIFICATION_STEP_STATUS.choices,
        verbose_name="认证受理评审状态",
    )
    plan_status = models.CharField(
        max_length=100,
        null=True,
        blank=False,
        choices=CERTIFICATION_STEP_STATUS.choices,
        verbose_name="制定认证方案状态",
    )
    document_review_status = models.CharField(
        max_length=100,
        null=True,
        blank=False,
        choices=CERTIFICATION_STEP_STATUS.choices,
        verbose_name="开展文件评审状态",
    )
    onsite_inspection_status = models.CharField(
        max_length=100,
        null=True,
        blank=False,
        choices=CERTIFICATION_STEP_STATUS.choices,
        verbose_name="开展现场检查状态",
    )
    carbon_footprint_verification_status = models.CharField(
        max_length=100,
        null=True,
        blank=False,
        choices=CERTIFICATION_STEP_STATUS.choices,
        verbose_name="碳足迹核查状态",
    )
    verification_report_issuance_status = models.CharField(
        max_length=100,
        null=True,
        blank=False,
        choices=CERTIFICATION_STEP_STATUS.choices,
        verbose_name="出具核查报告状态",
    )
    review_decision_status = models.CharField(
        max_length=100,
        null=True,
        blank=False,
        choices=CERTIFICATION_STEP_STATUS.choices,
        verbose_name="复合与认证决定状态",
    )
    certificate_issuance_status = models.CharField(
        max_length=100,
        null=True,
        blank=False,
        choices=CERTIFICATION_STEP_STATUS.choices,
        verbose_name="发放证书及标识状态",
    )
    supervision_status = models.CharField(
        max_length=100,
        null=True,
        blank=False,
        choices=CERTIFICATION_STEP_STATUS.choices,
        verbose_name="监督管理状态",
    )
    last_supervision = models.ForeignKey(
        "Supervision",
        on_delete=models.SET_NULL,
        verbose_name="最新一次监督管理",
        null=True,
        blank=True,
        related_name="supervision",
    )

    @property
    def day_left(self) -> int | None:
        # 从开始认证时间算起，最多90天需要完成
        if self.certification_start_time is None:
            return None
        if self.certification_finish_time is not None:
            return None
        return (self.certification_start_time + timedelta(days=90) - timezone.now()).days

    @property
    def can_supervision(self) -> bool:
        return (
            self.certification_status == CERTIFICATION_STATUS.APPROVED.value
            and self.supervision_status != SUPERVISION_STATUS.ONGOING.value
        )

    @property
    def unit(self) -> str:
        return self.model.unit


class CertificationVerificationReportIssuance(models.Model):
    """出具核查报告"""

    certification = models.OneToOneField(
        Certification,
        on_delete=models.CASCADE,
        verbose_name="认证记录",
        null=False,
        blank=False,
        related_name="verification_report_issuance",
    )
    report_file = models.ForeignKey(
        File,
        on_delete=models.SET_NULL,
        verbose_name="核查报告文件",
        null=True,
        blank=True,
        related_name="verification_report_issuance_report_file",
    )


class CertificationCarbonFootprintVerification(models.Model):
    """碳足迹核查"""

    certification = models.OneToOneField(
        Certification,
        on_delete=models.CASCADE,
        verbose_name="认证记录",
        null=False,
        blank=False,
        related_name="carbon_footprint_verification",
    )
    product_name = models.CharField(max_length=100, verbose_name="产品名称", null=False)
    specs = models.CharField(max_length=100, verbose_name="产品型号", null=False)
    functional_unit = models.CharField(max_length=100, verbose_name="功能单位", null=False)
    boundary = models.CharField(max_length=100, verbose_name="系统边界", null=False)
    client_name = models.CharField(max_length=100, verbose_name="受核查方名称", null=False)
    address = models.CharField(max_length=100, verbose_name="受核查方地址", null=False)
    credit_code = models.CharField(max_length=100, verbose_name="统一社会信用代码", null=False)
    legal_representative = models.CharField(max_length=100, verbose_name="法定代表人", null=False)
    verification_date = models.DateField(verbose_name="核查日期", null=False)
    initial_inspections = models.JSONField(verbose_name="初级数据核查", null=False)
    followup_inspections = models.JSONField(verbose_name="次级数据核查", null=False)
    data_acquisition_principle = models.TextField(max_length=500, verbose_name="数据取舍原则", null=True)
    data_quality_requirement = models.TextField(max_length=500, verbose_name="数据质量要求", null=True)
    data_quality_evaluation = models.TextField(max_length=500, verbose_name="数据质量评价", null=True)
    quantification_method = models.TextField(max_length=500, verbose_name="量化方法", null=True)
    product_quantifications = models.JSONField(verbose_name="产品碳足迹量化", null=False)
    verification_conclusion = models.TextField(max_length=500, verbose_name="核查结论", null=True)
    verification_recommendation = models.TextField(max_length=500, verbose_name="核查建议", null=True)
    uncertainty_analysis = models.TextField(max_length=500, verbose_name="不确定性分析", null=True)
    result = models.BooleanField(verbose_name="核查结果", null=True)
    result_file = models.ForeignKey(
        File,
        on_delete=models.SET_NULL,
        verbose_name="核查结果审批文件",
        null=True,
        blank=True,
        related_name="carbon_footprint_verification_result_file",
    )
    create_time = models.DateTimeField(verbose_name="创建时间", auto_now_add=True)


class CertificationOnsiteInspection(models.Model):
    """现场检查"""

    certification = models.OneToOneField(
        Certification,
        on_delete=models.CASCADE,
        verbose_name="认证记录",
        null=False,
        blank=False,
        related_name="onsite_inspection",
    )
    principal = models.CharField(max_length=100, verbose_name="委托人", null=False)
    principal_address = models.CharField(max_length=100, verbose_name="委托人地址", null=False)
    manufacturer = models.CharField(max_length=100, verbose_name="生产企业名称", null=False)
    manufacturer_address = models.CharField(max_length=100, verbose_name="生产企业地址", null=False)
    producer = models.CharField(max_length=100, verbose_name="生产者（制造商）名称", null=False)
    producer_address = models.CharField(max_length=100, verbose_name="生产者（制造商）地址", null=False)
    purpose = models.CharField(max_length=500, verbose_name="现场检查的目的", null=True)
    basis = models.CharField(max_length=500, verbose_name="现场检查的依据", null=True)
    scope = models.CharField(max_length=500, verbose_name="现场检查的范围", null=True)
    capability_accepted = models.BooleanField(verbose_name="企业保证能力检查", null=True)
    name_file_accepted = models.BooleanField(
        verbose_name="认证产品的名称、型号、生产企业及相关标识与申请文件或证书是否一致",
        null=True,
    )
    design_file_accepted = models.BooleanField(
        verbose_name="认证产品的设计、关键件、能源和资源、生产工艺、交付及储存、使用、回收与处置等适用环节碳足迹数据和信息与申请文件是否一致",
        null=True,
    )
    design_product_accepted = models.BooleanField(
        verbose_name="认证产品的设计、关键件、能源和资源、生产工艺、交付及储存、使用、回收与处置等环节与所确认产品是否一致",
        null=True,
    )
    process_description = models.TextField(max_length=500, verbose_name="现场检查过程的描述", null=True)
    nonconformities = models.JSONField(verbose_name="不符合项详情及整改建议", null=True)
    result = models.TextField(max_length=500, verbose_name="现场检查结果", null=True)
    file = models.ForeignKey(
        File,
        on_delete=models.SET_NULL,
        verbose_name="现场检查报告",
        null=True,
        blank=True,
        related_name="onsite_inspection_file",
    )
    create_time = models.DateTimeField(verbose_name="创建时间", auto_now_add=True)
    update_time = models.DateTimeField(verbose_name="更新时间", auto_now=True)


class CertificationPlan(models.Model):
    """认证方案"""

    certification = models.OneToOneField(
        Certification,
        on_delete=models.CASCADE,
        verbose_name="认证记录",
        null=False,
        blank=False,
        related_name="plan",
    )
    purpose = models.CharField(max_length=100, verbose_name="产品碳足迹标识认证的目的", null=False)
    certifying_unit = models.CharField(max_length=100, verbose_name="认证单元", null=False)
    product_type = models.CharField(max_length=100, verbose_name="产品种类", null=False)
    cert_standard = models.CharField(max_length=100, verbose_name="认证依据标准", null=False)
    data_time_range = models.CharField(max_length=100, verbose_name="数据时间边界", null=False)
    field_inspection_requirement = models.TextField(max_length=500, verbose_name="现场检查要求", null=True)
    inspectors = models.JSONField(verbose_name="检查组成员", null=False)
    reviewers = models.JSONField(verbose_name="技术评审组成员", null=False)
    accompanying_inspectors = models.TextField(max_length=500, verbose_name="检查组同行人员", null=True)
    progress_schedule = models.TextField(max_length=500, verbose_name="进度安排", null=True)
    file = models.ForeignKey(
        File,
        on_delete=models.SET_NULL,
        verbose_name="认证方案",
        null=True,
        blank=True,
        related_name="certification_plan_file",
    )
    create_time = models.DateTimeField(verbose_name="创建时间", auto_now_add=True)
    update_time = models.DateTimeField(verbose_name="更新时间", auto_now=True)


class CertificationDocumentReview(models.Model):
    """文件评审"""

    certification = models.OneToOneField(
        Certification,
        on_delete=models.CASCADE,
        verbose_name="认证记录",
        null=False,
        blank=False,
        related_name="document_review",
    )
    model_accepted = models.BooleanField(verbose_name="生命周期模型是否通过", null=True)
    material_accepted = models.BooleanField(verbose_name="认证材料是否通过", null=True)
    model_file = models.ForeignKey(
        File,
        on_delete=models.SET_NULL,
        verbose_name="生命周期模型审批文件",
        null=True,
        blank=True,
        related_name="document_review_model_file",
    )
    material_file = models.ForeignKey(
        File,
        on_delete=models.SET_NULL,
        verbose_name="认证材料审批文件",
        null=True,
        blank=True,
        related_name="document_review_material_file",
    )
    create_time = models.DateTimeField(verbose_name="创建时间", auto_now_add=True)


class CertificationReviewDecision(models.Model):
    """复合与认证决定"""

    certification = models.OneToOneField(
        Certification,
        on_delete=models.CASCADE,
        verbose_name="认证记录",
        null=False,
        blank=False,
        related_name="review_decision",
    )
    result = models.BooleanField(verbose_name="认证决定", null=True)
    result_file = models.ForeignKey(
        File,
        on_delete=models.SET_NULL,
        verbose_name="认证决定审批文件",
        null=True,
        blank=True,
        related_name="review_decision_result_file",
    )


class CertificateIssuance(models.Model):
    """发放证书及标识"""

    certification = models.OneToOneField(
        Certification,
        on_delete=models.CASCADE,
        verbose_name="认证记录",
        null=False,
        blank=False,
        related_name="certificate_issuance",
    )
    certificate_file = models.ForeignKey(
        File,
        on_delete=models.SET_NULL,
        verbose_name="证书文件",
        null=True,
        blank=True,
        related_name="certificate_issuance_certificate_file",
    )


class Authority(models.Model):
    """认证机构"""

    user = models.OneToOneField(
        "lca.User",
        on_delete=models.PROTECT,
        verbose_name="用户",
        null=False,
        blank=False,
        related_name="authority",
    )
    name = models.CharField(max_length=100, verbose_name="认证机构名称", null=False)
    code = models.CharField(max_length=100, verbose_name="统一社会信用代码", null=False)
    geography = models.ForeignKey(
        "lca.Geography",
        on_delete=models.PROTECT,
        verbose_name="机构地区",
        null=False,
        blank=False,
        related_name="authority_geography3",
    )
    address = models.CharField(max_length=100, verbose_name="机构详细地址", null=False)
    description = models.TextField(max_length=500, verbose_name="机构描述", null=False, default="")
    approval_number = models.CharField(max_length=100, verbose_name="机构批准号", null=False)
    contact_name = models.CharField(max_length=100, verbose_name="联系人名称", null=False)
    contact_phone = models.CharField(max_length=100, verbose_name="联系人手机", null=False)
    contact_email = models.CharField(max_length=100, verbose_name="联系人邮箱", null=False)
    products = ArrayField(
        models.CharField(max_length=100, choices=CERTIFICATE_PRODUCT.choices),
        verbose_name="认证产品",
        null=False,
    )
    approval_license = models.ForeignKey(
        File,
        on_delete=models.PROTECT,
        verbose_name="批准证书",
        null=True,
        blank=True,
        related_name="authority_approval_license3",
    )
    sign = models.ForeignKey(
        File,
        on_delete=models.PROTECT,
        verbose_name="电子签章",
        null=True,
        blank=True,
        related_name="authority_sign3",
    )
    logo = models.ForeignKey(
        File,
        on_delete=models.PROTECT,
        verbose_name="LOGO",
        null=True,
        blank=True,
        related_name="authority_logo3",
    )
    license = models.ForeignKey(
        File,
        on_delete=models.PROTECT,
        verbose_name="营业执照",
        null=True,
        related_name="authority_license3",
    )
    create_time = models.DateTimeField(verbose_name="创建时间", auto_now_add=True)
    update_time = models.DateTimeField(verbose_name="更新时间", auto_now=True)


class AuthorityProduct(models.Model):
    """认证机构和产品关联关系"""

    id = None
    authority = models.ForeignKey(Authority, on_delete=models.CASCADE, verbose_name="认证机构")
    product = models.CharField(max_length=100, verbose_name="产品名称", choices=PRODUCT.choices, db_index=True)


class RegionProduct(models.Model):
    """区域和产品关联关系"""

    region = models.ForeignKey(Region, on_delete=models.CASCADE, verbose_name="区域")
    product = models.CharField(max_length=100, verbose_name="产品名称", choices=PRODUCT.choices, db_index=True)


class Supervision(models.Model):
    """监督管理"""

    certification = models.ForeignKey(
        Certification,
        on_delete=models.CASCADE,
        verbose_name="认证记录",
        db_index=True,
        null=False,
        blank=False,
        related_name="supervisions",
    )
    authority = models.ForeignKey(
        Authority,
        on_delete=models.CASCADE,
        verbose_name="认证机构",
        db_index=True,
        null=False,
        blank=False,
        related_name="supervisions",
    )
    manufacturer = models.ForeignKey(
        Manufacturer,
        on_delete=models.CASCADE,
        verbose_name="生产企业",
        db_index=True,
        null=False,
        blank=False,
        related_name="supervisions",
    )
    status = models.CharField(
        max_length=100,
        db_index=True,
        null=False,
        blank=False,
        choices=SUPERVISION_STATUS.choices,
        verbose_name="监督管理状态",
    )
    issue_severe_quality_problem = models.BooleanField(verbose_name="是否出现严重质量问题", null=True)
    product_certification_quality_doubt = models.BooleanField(verbose_name="是否对碳足迹量化提出质疑", null=True)
    the_same = models.BooleanField(verbose_name="标准符合性或产品一致性", null=True)
    enterprise_capability_supervision = models.BooleanField(verbose_name="企业保证能力监督检查", null=True)
    product_consistency_supervision = models.BooleanField(verbose_name="产品一致性监督检查", null=True)
    product_footprint_verification = models.BooleanField(verbose_name="产品碳足迹核查", null=True)
    carbon_reduction_plan_supervision = models.BooleanField(verbose_name="降碳计划实施监督检查", null=True)
    product_footprint_decline_supervision = models.BooleanField(
        verbose_name="产品碳足迹量化是否较上一次核查有所下降", null=True
    )
    previous_review_issue_supervision = models.BooleanField(verbose_name="上次评价整改措施监督", null=True)
    supervision_result_evaluation = models.TextField(max_length=500, verbose_name="监督结果的评价", null=True)
    result = models.BooleanField(verbose_name="监督决定", null=True)
    start_time = models.DateTimeField(verbose_name="开始监督时间", null=True, db_index=True)
    end_time = models.DateTimeField(verbose_name="结束监督时间", null=True, db_index=True)
    result_file = models.ForeignKey(
        File,
        on_delete=models.SET_NULL,
        verbose_name="监督决定审批文件",
        null=True,
        blank=True,
        related_name="supervision_result_file",
    )
