import requests
from config import settings
from infra.utils import label_to_value
from lca.accounting.models import (
    BOUNDARIES,
    LIFE_CYCLE_STAGES,
    Category,
    Geography,
    Model,
)
import csv
from lca.file.models import File
import io


class AI:
    """AI 工具"""

    @staticmethod
    def get_workflow_params(key: str, inputs: dict, mode: str = "blocking"):
        return dict(
            json={
                "inputs": inputs,
                "response_mode": mode,
                "user": "test",
            },
            headers={
                "Authorization": f"Bearer {key}",
                "Content-Type": "application/json",
            },
        )

    @staticmethod
    def run_workflow(key: str, inputs: dict, value_key: str = None):
        """运行工作流"""
        response = requests.post(
            f"{settings.DIFY_API_URL}/workflows/run",
            **AI.get_workflow_params(key, inputs),
        )
        print(response.json())
        if response.status_code != 200:
            raise RunFailException()
        outputs = response.json()["data"]["outputs"]
        if value_key is not None:
            return outputs[value_key]
        return outputs

    @staticmethod
    def run_workflow_stream(key: str, inputs: dict):
        """运行工作流"""
        params = AI.get_workflow_params(key, inputs, "streaming")
        with requests.post(f"{settings.DIFY_API_URL}/workflows/run", **params, stream=True) as response:
            for line in response.iter_lines():
                print(line)
                yield line + b"\r\n"

    @staticmethod
    def get_process_markdown(process, indent_level=0, visited_processes=None):
        """
        生成过程的markdown文本（递归处理）

        Args:
            process: Process对象
            indent_level: 缩进级别，用于递归时的层级显示
            visited_processes: 已访问的过程集合，用于避免循环引用

        Returns:
            list: markdown文本行列表
        """
        if visited_processes is None:
            visited_processes = set()

        # 避免循环引用
        if process.id in visited_processes:
            return [f"{'  ' * indent_level}- 循环引用: {process.name}"]

        visited_processes.add(process.id)
        text = []

        # 过程基本信息
        indent = "  " * indent_level
        text.append(f"{indent}**过程名称：** {process.name}")
        if process.allocation_method:
            text.append(f"{indent}**分配方法：** {process.get_allocation_method_display()}")
        text.append(f"{indent}**CO2e：** {process.co2e}")

        # 输入
        if process.inputs.exists():
            text.append(f"{indent}#### 输入")
            for input_item in process.inputs.all():
                text.append(
                    f"{indent}- {input_item.name}: {input_item.amount} {input_item.unit.id if input_item.unit else ''}"
                )
                text.append(f"{indent}  - 类型: {input_item.get_type_display()}")
                text.append(f"{indent}  - 数据来源: {input_item.get_source_type_display()}")
                text.append(f"{indent}  - CO2e: {input_item.co2e}")

                if input_item.emission_source:
                    text.append(f"{indent}  - 排放源: {input_item.emission_source.name}")

                if input_item.custom_factor:
                    text.append(f"{indent}  - 自定义因子: {input_item.custom_factor.description}")

                # 运输信息
                if input_item.transports.exists():
                    text.append(f"{indent}  - 运输信息:")
                    for transport in input_item.transports.all():
                        text.append(f"{indent}    - 毛重: {transport.gross_weight}")
                        text.append(f"{indent}    - 起点: {transport.origin or '未指定'}")
                        text.append(f"{indent}    - 终点: {transport.terminal or '未指定'}")
                        text.append(f"{indent}    - 距离: {transport.distance}")
                        text.append(f"{indent}    - 运输方式: {transport.emission_source.name}")

                # 递归处理输入中的过程
                if input_item.process:
                    text.append(f"{indent}  - 子过程详情:")
                    sub_process_text = AI.get_process_markdown(
                        input_item.process, indent_level + 2, visited_processes.copy()
                    )
                    text.extend(sub_process_text)

        # 输出
        if process.outputs.exists():
            text.append(f"{indent}#### 输出")
            for output in process.outputs.all():
                text.append(f"{indent}- {output.name}: {output.amount} {output.unit.id if output.unit else ''}")
                text.append(f"{indent}  - 类型: {output.get_type_display()}")
                if hasattr(output, "green_house_gas") and output.green_house_gas:
                    text.append(f"{indent}  - 温室气体: {output.green_house_gas.name}")
                if hasattr(output, "allocation") and output.allocation:
                    text.append(f"{indent}  - 分配比例: {output.allocation.percent}%")

        visited_processes.remove(process.id)
        return text

    @staticmethod
    def get_model_data(model: Model):
        """获取模型数据"""

        text = ["## 目标与范围定义"]
        text.append(f"- 产品名称：{model.name}")
        text.append(f"- 功能单位：{model.functional_unit}")
        text.append(f"- 规格型号：{model.specs}")
        text.append(f"- 描述：{model.description}")
        text.append(f"- 公司名称：{model.company_name}")
        text.append(f"- 系统边界：{model.get_boundary_display()}")
        text.append("")

        # 生命周期阶段
        text.append("## 生命周期阶段")
        for life_cycle in model.life_cycles.all():
            text.append(f"### {LIFE_CYCLE_STAGES(life_cycle.stage).label}")

            if life_cycle.process:
                # 使用通用函数生成过程的markdown文本
                process_text = AI.get_process_markdown(life_cycle.process)
                text.extend(process_text)

            text.append("")

        text = "\n".join(text)
        print(f"====================================={text}")
        return text

    @staticmethod
    def input_quality_control(model: Model):
        """输入质量控制"""
        return AI.run_workflow(
            settings.DIFY_INPUT_CONTROLL_API_KEY,
            {
                "data": AI.get_model_data(model),
            },
            "result",
        )

    def craft_quality_control(model: Model):
        """工艺质量控制"""
        return AI.run_workflow(
            settings.DIFY_CRAFT_CONTROLL_API_KEY,
            {
                "data": AI.get_model_data(model),
            },
            "result",
        )

    def distribution_quality_control(model: Model):
        """分销质量控制"""
        return AI.run_workflow(
            settings.DIFY_DISTRIBUTION_CONTROLL_API_KEY,
            {
                "data": AI.get_model_data(model),
            },
            "result",
        )

    def generate_analysis_report(model: Model):
        """生成评估报告"""
        return AI.run_workflow_stream(
            settings.DIFY_ANALYSIS_REPORT_API_KEY,
            {
                "data": AI.get_model_data(model),
            },
        )

    def generate_goal_scope(file: File):
        """目标与范围提取"""
        text = AI.run_workflow(
            settings.DIFY_GOAL_SCOPE_API_KEY,
            {
                "file": {
                    "transfer_method": "remote_url",
                    "url": f"{file.url}",
                    "type": "document",
                }
            },
            "result",
        )

        reader = csv.DictReader(io.StringIO(text))
        for row in reader:
            return dict(
                amount=row["产量"] if row["产量"] else None,
                category=Category.objects.filter(name=row["所属行业"]).first(),
                company_name=row["公司名称"],
                description=row["产品描述"],
                functional_unit=row["功能单位"],
                geography=Geography.objects.filter(name=row["产品产地"]).first(),
                name=row["产品名称"],
                rule=row["取舍规则"],
                specs=row["产品型号"],
                unit=row["产量单位"],
                year=row["年份"] if row["年份"] != "" else None,
                boundary=label_to_value(row["系统边界"], BOUNDARIES),
            )

    def generate_life_cycle_by_bom(model: Model, file: File):
        """通过bom表生成模型生命周期"""

        pass


class RunFailException(Exception):
    message = "AI 运行失败"


class QualityControlFailException(Exception):
    message = "数据质控失败"
