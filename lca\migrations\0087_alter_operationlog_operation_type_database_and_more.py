# Generated by Django 5.2.1 on 2025-07-23 03:37

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('lca', '0086_operationlog'),
    ]

    operations = [
        migrations.CreateModel(
            name='Database',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ('name', models.CharField(max_length=255, unique=True, verbose_name='名称')),
                ('import_status', models.CharField(choices=[('pending', '待处理'), ('in_progress', '进行中'), ('success', '成功'), ('failed', '失败')], max_length=255, verbose_name='导入状态')),
                ('import_progress', models.FloatField(default=0, verbose_name='导入进度')),
                ('import_time', models.DateTimeField(blank=True, null=True, verbose_name='导入时间')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('file', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='lca.file', verbose_name='文件')),
            ],
        ),
        migrations.CreateModel(
            name='DatabaseImportTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('import_status', models.CharField(choices=[('pending', '待处理'), ('in_progress', '进行中'), ('success', '成功'), ('failed', '失败')], max_length=255, verbose_name='导入状态')),
                ('progress', models.FloatField(default=0, verbose_name='导入进度')),
                ('import_time', models.DateTimeField(blank=True, null=True, verbose_name='导入时间')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('database', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='lca.database', verbose_name='数据库')),
            ],
        ),
    ]
