from django.core.management import BaseCommand
from lca.accounting.models import Category
import csv


class Command(BaseCommand):
    help = "初始化分类"

    def handle(self, *args, **options):
        initial_category()


def initial_category():
    with open("lca/management/commands/data/category.csv") as file:
        reader = csv.DictReader(file)
        cat1 = ""  # 一级分类
        for row in reader:
            if row["id1"] != "":
                # 一级分类
                cat1 = row["id1"]
                print(row["name1"])
                Category.objects.update_or_create(id=row["id1"], name=row["name1"])
            if row["id2"] != "":
                print(row["name2"])
                if len(row["id2"]) == 2:
                    # 二级分类
                    Category.objects.update_or_create(id=row["id2"], name=row["name2"], parent_id=cat1)
                if len(row["id2"]) > 2:
                    # 三四级分类
                    Category.objects.update_or_create(id=row["id2"], name=row["name2"], parent_id=row["id2"][:-2])
            if len(row["id3"]) != "":
                print(row["name3"])
                Category.objects.update_or_create(id=row["id3"], name=row["name3"], parent_id=row["id3"][:-2])
