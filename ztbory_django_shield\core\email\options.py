from ztbory_django_shield.core.options import DjangoOptions


class Options(DjangoOptions):
    """邮件找回密码配置"""

    def __init__(
        self,
        logo_url: str = "",
        reset_password_url: str = "{token}",
        expire_in_seconds: int = 2 * 60 * 60,
    ):
        """

        :param logo_url: logo https地址
        :param reset_password_url: 重置密码url https://domain.com/reset-password/?token={token}
        :param expire_in_seconds: 有效时间 (s)
        """
        self.logo_url = logo_url
        self.reset_password_url = reset_password_url
        self.expire_in_seconds = expire_in_seconds

        self.load_django_settings("email")
