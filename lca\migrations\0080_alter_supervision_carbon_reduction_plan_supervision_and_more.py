# Generated by Django 5.2.1 on 2025-07-09 03:16

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0079_remove_supervision_current_certification"),
    ]

    operations = [
        migrations.AlterField(
            model_name="supervision",
            name="carbon_reduction_plan_supervision",
            field=models.BooleanField(null=True, verbose_name="降碳计划实施监督检查"),
        ),
        migrations.AlterField(
            model_name="supervision",
            name="enterprise_capability_supervision",
            field=models.BooleanField(null=True, verbose_name="企业保证能力监督检查"),
        ),
        migrations.AlterField(
            model_name="supervision",
            name="issue_severe_quality_problem",
            field=models.BooleanField(null=True, verbose_name="是否出现严重质量问题"),
        ),
        migrations.AlterField(
            model_name="supervision",
            name="previous_review_issue_supervision",
            field=models.BooleanField(null=True, verbose_name="上次评价整改措施监督"),
        ),
        migrations.Alter<PERSON>ield(
            model_name="supervision",
            name="product_certification_quality_doubt",
            field=models.BooleanField(null=True, verbose_name="是否对碳足迹量化提出质疑"),
        ),
        migrations.AlterField(
            model_name="supervision",
            name="product_consistency_supervision",
            field=models.BooleanField(null=True, verbose_name="产品一致性监督检查"),
        ),
        migrations.AlterField(
            model_name="supervision",
            name="product_footprint_decline_supervision",
            field=models.BooleanField(null=True, verbose_name="产品碳足迹量化是否较上一次核查有所下降"),
        ),
        migrations.AlterField(
            model_name="supervision",
            name="product_footprint_verification",
            field=models.BooleanField(null=True, verbose_name="产品碳足迹核查"),
        ),
        migrations.AlterField(
            model_name="supervision",
            name="the_same",
            field=models.BooleanField(null=True, verbose_name="标准符合性或产品一致性"),
        ),
    ]
