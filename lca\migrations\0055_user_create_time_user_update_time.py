# Generated by Django 5.2.1 on 2025-07-03 06:29

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0054_model_creator"),
    ]

    operations = [
        migrations.AddField(
            model_name="user",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_index=True,
                default=django.utils.timezone.now,
                verbose_name="创建时间",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="user",
            name="update_time",
            field=models.DateTimeField(auto_now=True, db_index=True, verbose_name="更新时间"),
        ),
    ]
