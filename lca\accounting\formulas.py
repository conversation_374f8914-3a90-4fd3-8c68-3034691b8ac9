from pydantic import BaseModel, Field

from lca.accounting.models import OUTPUT_TYPES, Formula, Output, Process
from lca.accounting.schema import FormulaInSchema
from lca.database.models import Flow


class Carbon(BaseModel):
    carbon: float = Field(None, description="煤炭重量(t)")

    def run(self):
        return {"二氧化碳": 20.5}


class Tansuanyan(BaseModel):
    lime: float = Field(None, description="石灰石重量(t)")
    percent: float = Field(100, description="煤炭中碳含量(%)")

    def run(self):
        return {"二氧化碳": 20.5}


def get_formula_info(name: str):
    cls = globals().get(name)
    info = []
    for name, field in cls.model_fields.items():
        info.append(
            {
                "name": name,
                "type": field.annotation.__name__,
                "default": field.default,
                "description": field.description,
            }
        )
    return info


def run(process: Process, params: list[FormulaInSchema]):
    flows = {}
    outputs = []
    for input in params:
        # 挨个公式计算，最后合并
        formula = Formula.objects.get(pk=input.id)
        class_path = formula.class_path
        cls = globals().get(class_path)
        kwgs = {}
        for item in input.params:
            kwgs[item.name] = item.value

        o = cls(**kwgs)
        ret = o.run()
        for g, v in ret.items():
            if g not in flows:
                flows[g] = v
            else:
                flows[g] += v
    # 生成对应的输出
    for g, v in flows.items():
        flow = Flow.objects.get(name=g)
        output = process.outputs.filter(flow=flow).first()
        if output is None:
            output = Output(
                flow=flow,
                name=flow.name,
                type=OUTPUT_TYPES.ENVIRONMENTAL_EMISSION.value,
                amount=v,
                unit_id="kg",
                process=process,
            )
        output.amount = v
        output.unit_id = "kg"
        output.save()
        outputs.append(output)
    return outputs
