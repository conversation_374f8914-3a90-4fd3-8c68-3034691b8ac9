from django.core.management import BaseCommand
from infra.utils import label_to_value
import csv
from django.db import transaction

from lca.accounting.models import BOUNDARIES, Category, EmissionSource, Geography, Unit
from lca.database.models import EmissionSourceDataSet


class Command(BaseCommand):
    help = "初始数据集"

    def handle(self, *args, **options):
        run()


def run():
    with open("lca/management/commands/data/dataset.csv", "r", encoding="utf-8") as file:
        reader = csv.DictReader(file)
        with transaction.atomic():
            for row in reader:
                print(row["产品名称"])
                item = EmissionSourceDataSet.objects.filter(name=row["产品名称"]).first()
                emission_source = EmissionSource.objects.get(name=row["产品名称"])
                data = dict(
                    emission_source=emission_source,
                    name=row["产品名称"],
                    alias=row["别名"],
                    year=row["时间代表性"],
                    geography=Geography.objects.get(name=row["地理代表性"]),
                    amount=row["数量"],
                    unit=Unit.objects.get(pk=row["单位"]),
                    functional_unit=row["功能单位"],
                    specs=row["规格型号"],
                    flow_chart_id=row["工艺流程图"] if row["工艺流程图"] != "" else None,
                    boundary=label_to_value(row["系统边界"], BOUNDARIES),
                    category=Category.objects.get(name=row["所属行业"]),
                    technical_description=row["技术描述"],
                    usage=row["产品或工艺用途"],
                    allocation_principles=row["分配原则"],
                    model_description=row["模型描述"],
                    data_treatment=row["数据处理"],
                )
                if item is None:
                    item = EmissionSourceDataSet()
                for key, value in data.items():
                    setattr(item, key, value)
                item.save()
