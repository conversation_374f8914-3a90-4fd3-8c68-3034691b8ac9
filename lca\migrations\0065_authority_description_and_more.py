# Generated by Django 5.2.1 on 2025-07-07 06:13

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0064_remove_certification_info_remove_authority_active_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="authority",
            name="description",
            field=models.TextField(default="", max_length=255, verbose_name="机构描述"),
        ),
        migrations.AddField(
            model_name="authoritycertification",
            name="description",
            field=models.TextField(default="", max_length=255, verbose_name="机构描述"),
        ),
        migrations.AlterField(
            model_name="authority",
            name="license",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="authority_license3",
                to="lca.file",
                verbose_name="营业执照",
            ),
        ),
        migrations.AlterField(
            model_name="manufacturer",
            name="license",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="lca.file",
                verbose_name="营业执照",
            ),
        ),
    ]
