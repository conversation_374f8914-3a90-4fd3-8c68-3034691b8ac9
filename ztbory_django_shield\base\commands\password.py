from django.core.management import BaseCommand

from ztbory_django_shield.base.shield import password_shield


class Command(BaseCommand):
    help = "Password helper"

    def add_arguments(self, parser):
        parser.add_argument(
            "username",
            type=str,
            nargs="?",
            help="EMPTY:clear all incorrect records; USERNAME:clear the user's incorrect records",
        )

    def handle(self, *args, **options):
        username = options["username"] or ""
        password_shield.clean_incorrect_username(username)
        self.stdout.write(
            self.style.SUCCESS(f"Clean incorrect records: {'ALL' if not username else 'USERNAME=' + username}")
        )
