# Generated by Django 5.1.7 on 2025-03-27 01:53

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0006_alter_craft_amount_alter_craft_product_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="Distribution",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("origin", models.CharField(max_length=100, verbose_name="起始地")),
                ("terminal", models.CharField(max_length=100, verbose_name="目的地")),
                (
                    "distance",
                    models.DecimalField(decimal_places=5, max_digits=20, verbose_name="运输距离"),
                ),
                (
                    "emmission_gas",
                    models.CharField(default="二氧化碳当量", max_length=100, verbose_name="排放气体"),
                ),
                (
                    "emmission_amount",
                    models.DecimalField(decimal_places=5, max_digits=20, verbose_name="排放量"),
                ),
                (
                    "co2e",
                    models.DecimalField(decimal_places=5, max_digits=20, verbose_name="CO2e"),
                ),
                (
                    "gross_weight",
                    models.DecimalField(decimal_places=5, max_digits=20, verbose_name="毛重"),
                ),
                (
                    "create_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "model",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="distributions",
                        to="lca.model",
                        verbose_name="模型",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="DistributionTransportRoute",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("origin", models.CharField(max_length=100, verbose_name="起始地")),
                ("terminal", models.CharField(max_length=100, verbose_name="目的地")),
                (
                    "distance",
                    models.DecimalField(decimal_places=5, max_digits=20, verbose_name="运输距离"),
                ),
                (
                    "distribution",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="transports",
                        to="lca.distribution",
                        verbose_name="分销阶段",
                    ),
                ),
                (
                    "trans_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.emissionsource",
                        verbose_name="运输方式",
                    ),
                ),
            ],
        ),
    ]
