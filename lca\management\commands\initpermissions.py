from django.core.management import BaseCommand
import csv
from django.db import transaction

from lca.users.models import Permission


class Command(BaseCommand):
    help = "初始化权限"

    def handle(self, *args, **options):
        run()


def run():
    with open("lca/management/commands/data/permission.csv", "r", encoding="utf-8") as file:
        reader = csv.DictReader(file)
        with transaction.atomic():
            for row in reader:
                print(row)
                obj = Permission.objects.filter(codename=row["codename"]).first()
                parent = None
                parent_id = row["父级"]
                if parent_id:
                    parent = Permission.objects.get(name=parent_id)
                data = dict(
                    name=row["名称"],
                    codename=row["codename"],
                    type="menu" if row["类型"] == "菜单" else "button",
                    order=row["排序"],
                    parent=parent,
                )
                if obj is None:
                    obj = Permission()
                for key, value in data.items():
                    setattr(obj, key, value)
                obj.save()
