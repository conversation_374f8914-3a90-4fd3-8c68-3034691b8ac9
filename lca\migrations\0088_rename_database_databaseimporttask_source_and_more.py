# Generated by Django 5.2.1 on 2025-07-23 03:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('lca', '0087_alter_operationlog_operation_type_database_and_more'),
    ]

    operations = [
        migrations.RenameField(
            model_name='databaseimporttask',
            old_name='database',
            new_name='source',
        ),
        migrations.AlterField(
            model_name='operationlog',
            name='operation_type',
            field=models.CharField(choices=[('CREATE', '新增'), ('EDIT', '编辑'), ('DELETE', '删除'), ('LOGIN', '登录'), ('LOGOUT', '登出'), ('LOGOFF', '注销'), ('SET_PERMISSIONS', '设置权限'), ('IMPORT_DATABASE', '导入数据库'), ('UPLOAD_FACTOR', '上传因子'), ('REVIEW_FACTOR', '审核因子'), ('SET_PLATFORM_MODEL', '设置平台模型'), ('UNPUBLISH_INFO', '下架信息'), ('CREATE_INFO', '新增了'), ('PUBLISH_INFO', '发布了')], db_comment='操作类型', max_length=32, verbose_name='操作类型'),
        ),
    ]
