import json
from functools import wraps
from typing import Any, Callable, Optional, Union

from django.http import HttpResponseBase, JsonResponse
from django.utils import timezone

from .models import OperationLog, OperationType


OperationFuncResult = Union[tuple[OperationType, str], str]
OperationFuncType = Callable[..., OperationFuncResult]


def _parse_operation_result(result: OperationFuncResult, default_type: OperationType) -> tuple[OperationType, str]:
    """统一处理操作函数的返回值"""
    match result:
        case (OperationType(op_type), description):
            return op_type, description
        case (_, description):  # 其他错误类型
            return default_type, description
        case description:  # 纯字符串
            return default_type, description


def _get_operator_info(user) -> dict[str, Any]:
    """提取操作人信息逻辑"""
    if user.is_authenticated:
        return {"operator": user, "operator_username": user.phone, "operator_nickname": user.nickname}
    return {"operator": None, "operator_username": "", "operator_nickname": ""}


def _is_success_response(response) -> bool:
    """判断响应是否成功"""
    if response is None:
        return True
    if isinstance(response, HttpResponseBase):
        return response.status_code in [200, 201, 204]
    if isinstance(response, tuple):
        return response[0] in [200, 201, 204]
    if getattr(response, "status_code", 0) in [200, 201, 204]:
        return True
    return bool(response)


def save_operation_log(user, operation_type: OperationType, description: Optional[str] = None) -> None:
    """保存操作日志的统一入口"""
    operator_data = _get_operator_info(user)
    log_description = description or _generate_description(
        operation_type,
    )

    OperationLog.objects.create(
        operation_type=operation_type, content=log_description, operation_time=timezone.now(), **operator_data
    )


def _generate_description(operation_type: OperationType, main_content: str = None, business_title: str = None) -> str:
    if main_content and business_title:
        return f"{operation_type.label}{main_content}{business_title}"
    elif main_content:
        return f"{operation_type.label}{main_content}"
    else:
        return f"{operation_type.label}"


def _generate_final_description(
    args,
    kwargs,
    response: Any | None = None,
    operation_type: OperationType = None,
    description: Optional[str] = None,
    operation_func: Optional[OperationFuncType] = None,
    run_before_view: bool = False,
    main_content: Optional[str] = None,
    content_field: Optional[str] = None,
) -> tuple[OperationType, str]:
    """
    根据优先级生成最终的日志描述和操作类型
    优先级：description > operation_func > 自动生成（content_field + main_content）
    """
    # 1. 优先使用用户传入的 description
    if description:
        return operation_type, description

    # 2. 其次使用 operation_func 的结果
    if operation_func and not run_before_view:
        return _parse_operation_result(operation_func(*args, **kwargs), operation_type)

    # 3. 最后使用 content_field + main_content 自动生成
    if content_field:
        business_title = None
        # 尝试从 response 中提取字段
        if isinstance(response, JsonResponse):
            try:
                data = json.loads(response.content)
                business_title = data.get(content_field)
            except AttributeError:
                pass
        elif isinstance(response, dict):
            business_title = response.get(content_field)
        else:
            business_title = getattr(response, content_field, None)

        # 如果没找到，尝试从 data 中提取字段
        if not business_title and args and hasattr(args[0], content_field):
            business_title = getattr(args[0], content_field)

        generated_desc = _generate_description(operation_type, main_content, business_title)
        return operation_type, generated_desc

    # 4. 如果都没生成描述，使用默认
    return operation_type, _generate_description(operation_type)


def log_operation(
    operation_type: OperationType,
    description: Optional[str] = None,
    operation_func: OperationFuncType | None = None,
    run_before_view: bool = False,
    main_content: str = None,
    content_field: str = None,
):
    """操作日志装饰器工厂函数，用于记录用户操作到数据库。

    该装饰器会拦截非GET请求，在操作执行成功后记录操作日志。
    对于DELETE操作，会先调用operation_func获取操作描述后再执行原函数。

    :param operation_type: 操作类型枚举值，指定当前操作的类型（如CREATE/DELETE等）
    :param description: 可选的操作描述文本。如果不提供且非DELETE操作，
                       会根据operation_type自动生成描述
    :param operation_func: 可选的操作函数，用于动态生成操作描述。函数返回值应为:
                          - tuple[OperationType, str]: 包含操作类型和描述
                          - str: 仅操作描述（操作类型使用参数传入的operation_type）
    :param run_before_view: 是否在view_func执行前运行operation_func (默认False)
    :param main_content: 主内容名称，用于生成描述文本（如“用户”）
    :param content_field: 业务字段名，用于从返回值或请求参数中提取业务名称（如"username"）

    描述优先级顺序：
    1. description（用户直接传入的描述）——优先级最高
    2. operation_func（动态生成描述）和 run_before_view 配合使用 ——次优先级
    3. 自动生成（content_field + main_content）——最低优先级

    :example:
    >>> @log_operation(OperationType.CREATE, description="自定义描述", main_content="用户", content_field="username")
    ... def create_user(request, data: CreateUserSchema):
    ...     return UserService.create_user(data)
    ...
    >>> @log_operation(OperationType.DELETE, operation_func=get_delete_description, run_before_view=True)
    ... def delete_user(request, user_id: int):
    ...     user = User.objects.get(pk=user_id)
    ...     user.delete()
    ...
    >>> @log_operation(OperationType.UPDATE, main_content="角色", content_field="name")
    ... def update_role(request, role_id: int, data: RoleUpdateSchema):
    ...     return RoleService.update_role(role_id, data)
    ...
    >>> @log_operation(OperationType.PUBLISH_INFO, operation_func=lambda *a: (OperationType.PUBLISH_INFO, "发布文章示例"))
    ... def publish_article(request, article_id: int):
    ...     return ArticleService.publish(article_id)
    """

    def decorator(view_func):
        @wraps(view_func)
        def wrapped_view(request, *args, **kwargs):
            if request.method == "GET":
                return view_func(request, *args, **kwargs)
            # 删除的要提前获取逻辑数据
            current_type, current_desc = operation_type, description
            if not description and operation_func and run_before_view:
                current_type, current_desc = _parse_operation_result(operation_func(*args, **kwargs), operation_type)
            response = view_func(request, *args, **kwargs)
            if _is_success_response(response):
                # 使用独立函数生成最终描述
                current_type, current_desc = _generate_final_description(
                    args,
                    kwargs,
                    response=response,
                    operation_type=current_type,
                    description=current_desc,
                    operation_func=operation_func,
                    run_before_view=run_before_view,
                    main_content=main_content,
                    content_field=content_field,
                )
                save_operation_log(
                    request.user,
                    current_type,
                    current_desc,
                )
            return response

        return wrapped_view

    return decorator
