from ninja import Query, Router
from ninja.pagination import paginate
from infra.PageNumberPagination import PageNumberPagination
from infra.decorators import certificated_user_required
from lca.accounting.formulas import run
from lca.accounting.models import Calculater, Input, Model, Output, Process
from lca.accounting.schema import (
    BaseModelSchema,
    CalculaterDetailSchema,
    CalculaterSchema,
    FlowSchema,
    FormulaInSchema,
    InputInSchema,
    InputSchema,
    LcaOutputSchema,
    LifeCycleSchema,
    ModelInSchema,
    ModelSchema,
    OutputInSchema,
    OutputSchema,
    ProcessInSchema,
    ProcessSchema,
)
from lca.accounting.services import LifeCycleService, ModelService
from lca.database.models import FLOW_TYPES, Flow
from lca.policies import authorize
from infra.filter import all_categories, filter

router = Router(tags=["碳核算"])


@router.get(
    "/models",
    response=list[BaseModelSchema],
    summary="模型列表",
    operation_id="getModelList",
)
@certificated_user_required()
@paginate(PageNumberPagination)
def list_models(
    request,
    id: int = Query(None, description="模型ID"),
    category_id: str = Query(None, description="分类ID"),
    year: int = Query(None, description="年份"),
    name: str = Query(None, description="名称"),
):
    query = (
        Model.objects.filter(plateform=False)
        .order_by("-id")
        .prefetch_related("category", "unit", "geography", "creator", "report", "image")
    )
    if not request.user.is_superuser:
        query = query.filter(creator=request.user)

    return filter(
        query,
        id=id,
        category_id__in=all_categories(category_id),
        year=year,
        name__icontains=name,
    )


@router.get(
    "/models/plateform",
    response=list[BaseModelSchema],
    summary="平台模型列表",
    operation_id="getPlateModelList",
)
@paginate(PageNumberPagination)
@certificated_user_required()
def list_plageform_models(
    request,
):
    return (
        Model.objects.filter(plateform=True)
        .order_by("-id")
        .prefetch_related("category", "unit", "geography", "creator", "report")
    )


@router.post(
    "/models/plateform/{id}/generate-to-model",
    response=BaseModelSchema,
    summary="从平台模型生成模型",
    operation_id="generateModelFromPlateform",
)
@certificated_user_required()
def generate_model_from_plateform(request, id: int):
    # model = Model.objects.filter(plateform=True).get(id=id)
    # return ModelService.copy_model(model)
    pass


@router.get("/models/{id}", response=ModelSchema, summary="模型详情", operation_id="getModel")
@certificated_user_required()
def get_model(request, id: int):
    model = Model.objects.get(pk=id)
    authorize(request.user, "view", model)
    return model


@router.post("/models", response=BaseModelSchema, summary="创建模型", operation_id="createModel")
@certificated_user_required()
def create_model(request, data: ModelInSchema):
    model = ModelService.create_model(data, request.user)
    return model


@router.put(
    "/models/{id}",
    response=BaseModelSchema,
    summary="编辑模型",
    operation_id="updateModel",
)
@certificated_user_required()
def update_model(request, id: int, data: ModelInSchema):
    model = Model.objects.get(pk=id)
    authorize(request.user, "update", model)
    ModelService.update_model(model, data)
    return model


@router.delete("/models/{id}", summary="删除模型", operation_id="deleteModel")
@certificated_user_required()
def delete_model(request, id: int):
    model = Model.objects.get(pk=id)
    authorize(request.user, "delete", model)
    ModelService.delete_model(model)
    return {"success": True}


@router.get(
    "/models/{id}/life-cycles",
    summary="获取模型生命周期",
    response=list[LifeCycleSchema],
)
@certificated_user_required()
def get_model_life_cycles(request, id: int):
    model = Model.objects.get(pk=id)
    authorize(request.user, "view", model)
    return model.life_cycles.all().prefetch_related("process")


@router.put(
    "/processes/{id}",
    summary="更新过程",
    response=ProcessSchema,
    operation_id="updateProcess",
)
@certificated_user_required()
def update_process(request, id: int, data: ProcessInSchema):
    process = Process.objects.get(pk=id)
    authorize(request.user, "update", process)
    LifeCycleService.update_process(process, data)
    return process


@router.get(
    "processes/{id}/detail",
    summary="过程详情",
    response=ProcessSchema,
    operation_id="getProcess",
)
@certificated_user_required()
def get_process(request, id: int):
    process = Process.objects.get(pk=id)
    authorize(request.user, "view", process)
    return Process.objects.get(pk=id)


@router.post(
    "/processes/{id}/inputs",
    summary="添加输入",
    response=InputSchema,
    operation_id="addInput",
)
@certificated_user_required()
def add_input(request, id: int, data: InputInSchema):
    process = Process.objects.get(pk=id)
    authorize(request.user, "update", process)
    input = LifeCycleService.create_input(process, data)
    return input


@router.put(
    "/processes/{id}/inputs/{input_id}",
    summary="编辑输入",
    response=InputSchema,
    operation_id="updateInput",
)
@certificated_user_required()
def update_input(request, id: int, input_id: int, data: InputInSchema):
    input = Input.objects.get(pk=input_id)
    authorize(request.user, "update", input)
    LifeCycleService.update_input(input, data)
    return input


@router.delete("/processes/{id}/inputs/{input_id}", summary="删除输入", operation_id="deleteInput")
@certificated_user_required()
def delete_input(request, id: int, input_id: int):
    input = Input.objects.get(pk=input_id)
    authorize(request.user, "update", input)
    LifeCycleService.delete_input(input)
    return {}


@router.post(
    "processes/{id}/outputs",
    summary="添加输出",
    response=OutputSchema,
    operation_id="addOutput",
)
@certificated_user_required()
def add_output(request, id: int, data: OutputInSchema):
    process = Process.objects.get(pk=id)
    authorize(request.user, "update", process)
    output = LifeCycleService.create_output(process, data)
    return output


@router.put(
    "processes/{id}/outputs/{output_id}",
    summary="编辑输出",
    response=OutputSchema,
    operation_id="updateOutput",
)
@certificated_user_required()
def update_output(request, id: int, output_id: int, data: OutputInSchema):
    output = Output.objects.get(pk=output_id)
    authorize(request.user, "update", output)
    LifeCycleService.update_output(output, data)
    return output


@router.delete(
    "processes/{id}/outputs/{output_id}",
    summary="删除输出",
    operation_id="deleteOutput",
)
@certificated_user_required()
def delete_output(request, id: int, output_id: int):
    output = Output.objects.get(id=output_id)
    authorize(request.user, "update", output)
    LifeCycleService.delete_output(output)
    return {}


@router.get("/calculaters", summary="计算器列表", response=list[CalculaterSchema])
@paginate(PageNumberPagination)
@certificated_user_required()
def calculaters(request):
    return Calculater.objects.all()


@router.get("/calculaters/{id}", summary="计算器", response=CalculaterDetailSchema)
@certificated_user_required()
def get_calculater(request, id: int):
    return Calculater.objects.get(pk=id)


@router.post("/processes/{id}/calculate", summary="运行计算器", response=list[OutputSchema])
@certificated_user_required()
def run_calculater(request, id: int, params: list[FormulaInSchema]):
    process = Process.objects.get(pk=id)
    authorize(request.user, "update", process)
    return run(process, params)


@router.get(
    "/green_house_gas",
    response=list[FlowSchema],
    summary="获取排放类型",
    operation_id="getGreenHouseGasList",
)
@certificated_user_required()
def list_green_house_gas(request):
    return Flow.objects.filter(type=FLOW_TYPES.ELEMENTARY.value, gwp__isnull=False).all()


@router.get(
    "/model/{id}/lca",
    response=LcaOutputSchema,
    summary="LCA计算",
    operation_id="getModelLca",
)
@certificated_user_required()
def get_lca(request, id: int):
    model = Model.objects.get(pk=id)
    authorize(request.user, "view", model)
    lca = LifeCycleService.get_lca(model)
    return lca
