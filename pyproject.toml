[project]
name = "lca"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "alibabacloud-dysmsapi20170525>=4.1.2",
    "django>=5.2.1",
    "django-cors-headers>=4.7.0",
    "django-environ>=0.12.0",
    "django-ninja>=1.4.1",
    "django-redis>=5.4.0",
    "docx>=0.2.4",
    "docxtpl>=0.20.0",
    "gevent>=25.5.1",
    "gmssl>=3.2.2",
    "gunicorn>=23.0.0",
    "matplotlib>=3.10.3",
    "minio>=7.2.15",
    "pandas>=2.2.3",
    "psycopg2>=2.9.10",
    "pypandoc>=1.15",
    "python-docx>=1.1.2",
    "requests>=2.32.3",
    "sentry-sdk>=2.28.0",
    "pytest>=8.0.0",
    "pytest-django>=4.8.0",
    "pytest-cov>=4.0.0",
    "factory-boy>=3.3.0",
    "freezegun>=1.4.0",
    "validators>=0.35.0",
]

[tool.pytest.ini_options]
DJANGO_SETTINGS_MODULE = "config.settings"
python_files = ["tests.py", "test_*.py", "*_tests.py"]
addopts = "--cov=lca --cov-report=html --cov-report=term-missing --reuse-db"

[tool.ruff]
line-length = 120
