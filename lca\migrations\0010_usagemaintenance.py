# Generated by Django 5.1.7 on 2025-03-27 03:10

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0009_distributionroute_delete_distributiontransportroute"),
    ]

    operations = [
        migrations.CreateModel(
            name="UsageMaintenance",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "usage_pathway",
                    models.CharField(max_length=100, verbose_name="使用途径"),
                ),
                (
                    "usage_emmission_amount",
                    models.DecimalField(decimal_places=5, max_digits=20, verbose_name="使用排放量"),
                ),
                (
                    "maintenance_emmission_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=5,
                        max_digits=20,
                        null=True,
                        verbose_name="维护排放量",
                    ),
                ),
                (
                    "maintenance_type",
                    models.CharField(blank=True, max_length=100, null=True, verbose_name="维护方式"),
                ),
                (
                    "co2e",
                    models.DecimalField(decimal_places=5, max_digits=20, verbose_name="CO2e"),
                ),
                (
                    "create_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "maintenance_unit_id",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="maintenance_unit",
                        to="lca.unit",
                        verbose_name="维护排放量单位",
                    ),
                ),
                (
                    "model",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="usage_maintenance",
                        to="lca.model",
                        verbose_name="模型",
                    ),
                ),
                (
                    "usage_unit_id",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="usage_unit",
                        to="lca.unit",
                        verbose_name="使用排放量单位",
                    ),
                ),
            ],
        ),
    ]
