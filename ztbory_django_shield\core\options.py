from django.conf import settings


class DjangoOptions:
    """通用配置类，从Django settings中加载配置项"""

    def load_django_settings(self, key=""):
        config = (settings.SHIELD or dict()).get(key, None)
        if config:  # type: dict
            for attr, _ in vars(self).items():
                value = config.get(attr, None)
                if value is not None:
                    setattr(self, attr, value)
