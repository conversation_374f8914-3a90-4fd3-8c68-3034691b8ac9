# Generated by Django 5.2.1 on 2025-07-11 05:49

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0082_group_is_government_group_is_predefined_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="certification",
            name="reason",
            field=models.TextField(
                blank=True,
                max_length=500,
                null=True,
                verbose_name="认证申请被拒绝的原因",
            ),
        ),
        migrations.AlterField(
            model_name="user",
            name="certificate_status",
            field=models.CharField(
                blank=True,
                choices=[
                    ("none", "未认证"),
                    ("ongoing", "认证中"),
                    ("approved", "已通过"),
                    ("rejected", "未通过"),
                ],
                db_index=True,
                default="none",
                max_length=20,
                null=True,
                verbose_name="认证状态",
            ),
        ),
    ]
