import functools
from typing import Any, List, Union

from django.conf import settings
from django.db.models import QuerySet
from django.http import HttpRequest, JsonResponse
from ninja import Schema
from ninja.pagination import PaginationBase
from ninja.types import DictStrAny
from ninja.errors import HttpError
from pydantic import Field

from lca.users.models import USER_CERTIFICATION_STATUS, USER_TYPES


def login_required(view_func):
    @functools.wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return JsonResponse({"error": "Unauthorized"}, status=401)
        return view_func(request, *args, **kwargs)

    return _wrapped_view


def permission_required(permissions: Union[str, List[str]], require_all: bool = False):
    """
    权限验证装饰器

    Args:
        permissions: 权限代码，可以是单个字符串或字符串列表
        require_all: 是否需要所有权限，默认False（只需要其中一个权限）

    Usage:
        @permission_required('user.view')
        @permission_required(['user.view', 'user.edit'])
        @permission_required(['user.view', 'user.edit'], require_all=True)
    """

    def decorator(view_func):
        @functools.wraps(view_func)
        def wrapper(request: HttpRequest, *args, **kwargs):
            # 检查用户是否已登录
            if not request.user.is_authenticated:
                raise HttpError(401, "用户未登录")

            # 将权限转换为列表
            required_permissions = permissions if isinstance(permissions, list) else [permissions]

            # 获取用户权限
            user_permissions = request.user.permissions

            # 如果不可用，提示未登录
            if not request.user.is_active:
                raise HttpError(401, "您已被禁用")

            # 如果是超级管理员，什么都能做
            if request.user.is_superuser:
                return view_func(request, *args, **kwargs)

            # 检查权限
            if require_all:
                # 需要所有权限
                missing_permissions = [perm for perm in required_permissions if perm not in user_permissions]
                if missing_permissions:
                    raise HttpError(403, "您没有权限执行该操作")
            else:
                # 只需要其中一个权限
                has_permission = any(perm in user_permissions for perm in required_permissions)
                if not has_permission:
                    raise HttpError(403, "您没有权限执行该操作")

            return view_func(request, *args, **kwargs)

        return wrapper

    return decorator


def check_certificated_user(request: HttpRequest, types: Union[str, List[str]] = []):
    # 检查用户是否已登录
    if not request.user.is_authenticated:
        raise HttpError(401, "用户未登录")

    if request.user.is_superuser:
        return

    # 将权限转换为列表
    types2 = types if isinstance(types, list) else [types]

    if request.user.certificate_status != USER_CERTIFICATION_STATUS.APPROVED.value:
        raise HttpError(403, "用户未认证")

    # 需要检查具体的类型，只要符合一条就行
    if len(types2) > 0:
        if request.user.type not in types2:
            raise HttpError(403, "您没有权限访问该页面")


def certificated_user_required_manufacturer():
    """
    用户认证装饰器
    """

    def decorator(view_func):
        @functools.wraps(view_func)
        def wrapper(request: HttpRequest, *args, **kwargs):
            check_certificated_user(request, USER_TYPES.MANUFACTURER.value)
            return view_func(request, *args, **kwargs)

        return wrapper

    return decorator


def certificated_user_required_authority():
    """
    用户认证装饰器
    """

    def decorator(view_func):
        @functools.wraps(view_func)
        def wrapper(request: HttpRequest, *args, **kwargs):
            check_certificated_user(request, USER_TYPES.AUTHORITY.value)
            return view_func(request, *args, **kwargs)

        return wrapper

    return decorator


def certificated_user_required_government():
    """
    用户认证装饰器
    """

    def decorator(view_func):
        @functools.wraps(view_func)
        def wrapper(request: HttpRequest, *args, **kwargs):
            check_certificated_user(request, USER_TYPES.GOVERNMENT.value)
            return view_func(request, *args, **kwargs)

        return wrapper

    return decorator


def certificated_user_required(types: Union[str, List[str]] = []):
    """
    用户认证装饰器
    """

    def decorator(view_func):
        @functools.wraps(view_func)
        def wrapper(request: HttpRequest, *args, **kwargs):
            check_certificated_user(request, types)
            return view_func(request, *args, **kwargs)

        return wrapper

    return decorator


def admin_required(view_func):
    """
    管理员权限装饰器
    """

    @functools.wraps(view_func)
    def wrapper(request: HttpRequest, *args, **kwargs):
        if not request.user.is_authenticated:
            raise HttpError(401, "用户未登录")

        if not request.user.is_staff:
            raise HttpError(403, "需要管理员权限")

        return view_func(request, *args, **kwargs)

    return wrapper


def superuser_required(view_func):
    """
    超级管理员权限装饰器
    """

    @functools.wraps(view_func)
    def wrapper(request: HttpRequest, *args, **kwargs):
        if not request.user.is_authenticated:
            raise HttpError(401, "用户未登录")

        if not request.user.is_superuser:
            raise HttpError(403, "需要超级管理员权限")

        return view_func(request, *args, **kwargs)

    return wrapper


class PageNumberPagination(PaginationBase):
    class Input(Schema):
        page: int = Field(1, ge=1)
        page_size: int = Field(None, ge=1, le=100)

    def paginate_queryset(
        self,
        queryset: QuerySet,
        pagination: Input,
        **params: DictStrAny,
    ) -> Any:
        size = pagination.page_size if pagination.page_size else settings.NINJA_PAGINATION_PER_PAGE
        offset = (pagination.page - 1) * size
        return {
            "items": queryset[offset : offset + size],  # noqa: E203
            "count": self._items_count(queryset),
        }
