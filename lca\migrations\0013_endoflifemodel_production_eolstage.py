# Generated by Django 5.1.7 on 2025-03-27 08:49

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0012_rename_usage_unit_id_usagemaintenance_usage_unit_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="EndofLifeModel",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("RECYCLE", "回收"),
                            ("ENERGY_RECOVERY", "能量回收"),
                            ("COMPOST", "堆肥"),
                            ("LANDFILL", "填埋"),
                            ("COMBUST", "焚烧"),
                            ("BIODEGRADATION", "生物降解"),
                            ("OTHER", "其他"),
                        ],
                        max_length=100,
                        verbose_name="类型",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="名称")),
                (
                    "factor",
                    models.DecimalField(decimal_places=5, max_digits=20, verbose_name="因子"),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Production",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("ALUMINUM_CANS", "铝罐"),
                            ("ALUMINUM_INGOT", "铝锭"),
                            ("STEEL_CANS", "钢罐"),
                            ("COPPER_WIRE", "铜线"),
                            ("GLASS", "玻璃"),
                            ("HDPE", "高密度聚乙烯"),
                            ("LDPE", "低密度聚乙烯"),
                            ("PET", "聚对苯二甲酸乙二醇酯"),
                            ("LLDPE", "线性低密度聚乙烯"),
                            ("PP", "聚丙烯"),
                            ("PS", "聚苯乙烯"),
                            ("PVC", "聚氯乙烯"),
                            ("PLA", "聚乳酸"),
                            ("CORRUGATED_CONTAINERS", "瓦楞纸箱"),
                            ("MAGAZINES_THIRD_CLASS_MAIL", "杂志第三类邮件"),
                            ("NEWSPAPER", "报纸"),
                            ("OFFICE_PAPER", "办公用纸"),
                            ("PHONEBOOKS", "电话簿"),
                            ("TEXTBOOKS", "教科书"),
                            ("DIMENSIONAL_LUMBER", "规格木材"),
                            ("MEDIUM_DENSITY_FIBERBOARD", "中密度纤维板"),
                            ("FOOD_WASTE_NON_MEAT", "食物废物非肉类"),
                            ("FOOD_WASTE_MEAT_ONLY", "食物废物仅肉类"),
                            ("BEEF", "牛肉"),
                            ("POULTRY", "家禽"),
                            ("GRAINS", "谷物"),
                            ("BREAD", "面包"),
                            ("FRUITS_AND_VEGETABLES", "水果和蔬菜"),
                            ("DAIRY_PRODUCTS", "乳制品"),
                            ("YARD_TRIMMINGS", "庭院修剪物"),
                            ("GRASS", "草"),
                            ("LEAVES", "树叶"),
                            ("BRANCHES", "树枝"),
                            ("MIXED_PAPER_GENERAL", "混合纸一般"),
                            (
                                "MIXED_PAPER_PRIMARILY_RESIDENTIAL",
                                "混合纸主要来自居民区",
                            ),
                            (
                                "MIXED_PAPER_PRIMARILY_FROM_OFFICES",
                                "混合纸主要来自办公室",
                            ),
                            ("MIXED_METALS", "混合金属"),
                            ("MIXED_PLASTICS", "混合塑料"),
                            ("MIXED_RECYCLABLES", "混合可回收物"),
                            ("FOOD_WASTE", "食物废物"),
                            ("MIXED_ORGANICS", "混合有机物"),
                            ("MIXED_MSW", "混合城市固体废物"),
                            ("CARPET", "地毯"),
                            ("DESKTOP_CPUS", "台式机CPU"),
                            ("PORTABLE_ELECTRONIC_DEVICES", "便携式电子设备"),
                            ("FLAT_PANEL_DISPLAYS", "平板显示器"),
                            ("CRT_DISPLAYS", "阴极射线管显示器"),
                            ("ELECTRONIC_PERIPHERALS", "电子外围设备"),
                            ("HARD_COPY_DEVICES", "硬拷贝设备"),
                            ("MIXED_ELECTRONICS", "混合电子设备"),
                            ("CLAY_BRICKS", "粘土砖"),
                            ("CONCRETE", "混凝土"),
                            ("FLY_ASH", "飞灰"),
                            ("TIRES", "轮胎"),
                            ("ASPHALT_CONCRETE", "沥青混凝土"),
                            ("ASPHALT_SHINGLES", "沥青瓦"),
                            ("DRYWALL", "石膏板"),
                            ("FIBERGLASS_INSULATION", "玻璃纤维绝缘材料"),
                        ],
                        max_length=100,
                        verbose_name="类型",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="名称")),
            ],
        ),
        migrations.CreateModel(
            name="EOLStage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "emmission_gas",
                    models.CharField(default="二氧化碳当量", max_length=100, verbose_name="排放气体"),
                ),
                (
                    "emmission_amount",
                    models.DecimalField(decimal_places=5, max_digits=20, verbose_name="排放量"),
                ),
                (
                    "eol_model",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.endoflifemodel",
                        verbose_name="EOL模型",
                    ),
                ),
                (
                    "model",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="eol_stage",
                        to="lca.model",
                        verbose_name="模型",
                    ),
                ),
                (
                    "product",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.production",
                        verbose_name="产品",
                    ),
                ),
            ],
        ),
    ]
