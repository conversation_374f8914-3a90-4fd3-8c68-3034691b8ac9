from django.core.management import BaseCommand
from ztbory_django_shield.base.shield import slide_captcha_shield
from ztbory_django_shield.core.exceptions import SlideCaptchaNotFoundError


class Command(BaseCommand):
    help = "Captcha helper"

    def add_arguments(self, parser):
        parser.add_argument(
            "uuid",
            type=str,
            nargs="?",
            help="EMPTY:generate new captcha; UUID:fetch existing captcha",
        )

    def handle(self, *args, **options):
        uuid = options["uuid"]
        if uuid is None:
            captcha = slide_captcha_shield.generate_captcha()
            self.stdout.write(self.style.SUCCESS("Captcha generated = {"))
            self.stdout.write(self.style.SUCCESS(f"    uuid => {captcha.uuid}"))
            self.stdout.write(self.style.SUCCESS(f"  secret => {captcha.secret}"))
            self.stdout.write(self.style.SUCCESS("}"))
        else:
            try:
                captcha = slide_captcha_shield.gain_captcha(uuid)
                self.stdout.write(self.style.SUCCESS("Captcha fetched = {"))
                self.stdout.write(self.style.SUCCESS(f"    uuid => {captcha.uuid}"))
                self.stdout.write(self.style.SUCCESS(f"  secret => {captcha.secret}"))
                self.stdout.write(self.style.SUCCESS("}"))
            except SlideCaptchaNotFoundError:
                self.stdout.write(self.style.ERROR(f"Captcha uuid:{uuid} not found"))
