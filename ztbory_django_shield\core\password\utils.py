import re
import string


def validate_regex(content: str, regex: str) -> bool:
    return True if re.match(regex, content) else False


def validate_continuous(content: str) -> bool:
    content = content.lower()
    is_digit_continuous = True
    is_letter_continuous = True
    if re.match(r"^(\d+|[a-z]+|\d+[a-z]+|[a-z]+\d+)$", content, re.IGNORECASE):
        digits = re.findall(r"\d+", content)
        if digits:
            is_digit_continuous = not (len(digits[0]) >= 2 and digits[0] in "01234567890")
        else:
            is_digit_continuous = False

        letters = re.findall(r"[a-z]+", content)
        if letters:
            is_letter_continuous = not (len(letters[0]) >= 2 and letters[0] in string.ascii_lowercase)
        else:
            is_letter_continuous = False

    return any([is_digit_continuous, is_letter_continuous])


def validate_contains(content: str, contains: str) -> bool:
    return False if re.search(contains, content, re.IGNORECASE) else True
