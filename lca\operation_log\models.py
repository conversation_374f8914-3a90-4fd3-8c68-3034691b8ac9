from django.db import models


class OperationType(models.TextChoices):
    CREATE = "CREATE", "新增"
    EDIT = "EDIT", "编辑"
    DELETE = "DELETE", "删除"
    LOGIN = "LOGIN", "登录"
    LOGOUT = "LOGOUT", "登出"
    LOGOFF = "LOGOFF", "注销"
    SET_PERMISSIONS = "SET_PERMISSIONS", "设置权限"
    IMPORT_DATABASE = "IMPORT_DATABASE", "导入数据库"
    UPLOAD_FACTOR = "UPLOAD_FACTOR", "上传因子"
    REVIEW_FACTOR = "REVIEW_FACTOR", "审核因子"
    SET_PLATFORM_MODEL = "SET_PLATFORM_MODEL", "设置平台模型"
    UNPUBLISH_INFO = "UNPUBLISH_INFO", "下架信息"
    CREATE_INFO = "CREATE_INFO", "新增了"
    PUBLISH_INFO = "PUBLISH_INFO", "发布了"


class OperationLog(models.Model):
    operation_type = models.CharField(
        max_length=32, choices=OperationType.choices, verbose_name="操作类型", db_comment="操作类型"
    )
    operator = models.ForeignKey(
        "lca.User", db_index=True, on_delete=models.SET_NULL, null=True, verbose_name="操作人", db_comment="操作人"
    )
    operator_username = models.CharField(
        max_length=255, db_index=True, default="", verbose_name="操作人账号", db_comment="操作人账号"
    )
    operator_nickname = models.CharField(
        max_length=255, db_index=True, default="", verbose_name="账号用户名", db_comment="账号用户名"
    )
    content = models.TextField(verbose_name="操作内容", db_comment="操作内容")
    operation_time = models.DateTimeField(db_index=True, verbose_name="操作时间", db_comment="操作时间")
    create_time = models.DateTimeField(verbose_name="创建时间", db_comment="创建时间", auto_now_add=True)
    update_time = models.DateTimeField(verbose_name="更新时间", db_comment="更新时间", auto_now=True)

    class Meta:
        verbose_name = "日志记录"
