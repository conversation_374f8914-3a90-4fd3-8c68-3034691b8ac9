import validators

from lca.accounting.services import GeographyService
from lca.common.categoryServices import CategoryServices
from lca.file.models import File
from lca.resource_center.errors import ResourceCenterError, raise_error
from lca.resource_center.models import InformationStatus, ResourceCenter
from lca.resource_center.schema import BaseNewsAndReportPolicySchema, StandardSpecificationSchema


class ResourceCenterPermissionValidator:
    """权限验证类"""

    def validate_view(self, user, status: InformationStatus):
        if user.is_superuser:
            return
        need_checked_permission = ""
        if status == InformationStatus.PUBLISHED:
            need_checked_permission = "DataFileManage:Library:View"
        elif status == InformationStatus.DRAFT:
            need_checked_permission = "DraftBox:View"

        if need_checked_permission and need_checked_permission not in user.permissions:
            raise_error(ResourceCenterError.PERMISSION_DENIED)

    def validate_save(self, user, status: InformationStatus):
        if user.is_superuser:
            return

        need_checked_permission = ""
        if status == InformationStatus.PUBLISHED:
            need_checked_permission = "DraftBox:Publish"
        elif status == InformationStatus.DRAFT:
            need_checked_permission = "DraftBox:Add"

        if need_checked_permission and need_checked_permission not in user.permissions:
            raise_error(ResourceCenterError.PERMISSION_DENIED)

    def validate_detail(self, user, id_: int):
        resource_center = ResourceCenter.objects.get(id=id_)
        self.validate_view(user, InformationStatus(resource_center.status))


class ResourceCenterValidator:
    """验证类"""

    def validate_news(self, data: BaseNewsAndReportPolicySchema):
        """验证信息公告、国内碳资讯、国外碳资讯、研究报告数据"""
        self.validate_common(
            data.category_id,
            data.article_link,
            data.cover_image_id,
            data.file_upload_id,
            data.content,
        )

    def validate_standard(self, data: StandardSpecificationSchema):
        """验证标准规范"""
        self.validate_common(
            data.category_id,
            data.article_link,
            data.cover_image_id,
            data.file_upload_id,
            data.content,
        )

        if not data.applicable_area:
            raise_error(ResourceCenterError.APPLICABLE_AREA_REQUIRED)
        for area in data.applicable_area:
            if not GeographyService.check_geography_exists(area):
                raise_error(ResourceCenterError.APPLICABLE_AREA_INVALID)

    def validate_common(
        self,
        category_id: str | None = None,
        article_link: str | None = None,
        cover_image_id: str | None = None,
        file_upload_id: str | None = None,
        content: str | None = None,
    ):
        if category_id:
            if not CategoryServices.is_first_level_category(category_id):
                raise_error(ResourceCenterError.CATEGORY_NOT_FIRST_LEVEL)

        if not any([article_link, file_upload_id, content]):
            raise_error(ResourceCenterError.ALL_FIELDS_EMPTY)

        if article_link:
            try:
                is_valid_url = validators.url(article_link)
                if not is_valid_url:
                    raise_error(ResourceCenterError.INVALID_URL)
            except Exception:
                raise_error(ResourceCenterError.INVALID_URL)

        if cover_image_id:
            if not File.objects.filter(id=cover_image_id).exists():
                raise_error(ResourceCenterError.COVER_IMAGE_NOT_EXIST)
        if file_upload_id:
            if not File.objects.filter(id=file_upload_id).exists():
                raise_error(ResourceCenterError.FILE_NOT_EXIST)


permission_validator = ResourceCenterPermissionValidator()
validator = ResourceCenterValidator()
