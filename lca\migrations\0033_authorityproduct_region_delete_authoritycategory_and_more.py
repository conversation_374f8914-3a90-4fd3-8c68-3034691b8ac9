# Generated by Django 5.2.1 on 2025-05-15 08:18

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0032_authoritycategory_model_parent_id_authority_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="AuthorityProduct",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "product",
                    models.CharField(
                        choices=[
                            ("lithium_battery", "锂电池"),
                            ("photovoltaic_product", "光伏产品"),
                            ("steel", "钢铁"),
                            ("textile", "纺织品"),
                            ("electronics", "电子电器"),
                            ("tire", "轮胎"),
                            ("cement", "水泥"),
                            ("electrolytic_aluminum", "电解铝"),
                            ("phosphate_fertilizer", "磷铵"),
                            ("wood_product", "木制品"),
                            ("electric_vehicle", "电动汽车"),
                            ("power_battery", "动力电池"),
                            ("glass", "玻璃"),
                            ("ethylene", "乙烯"),
                            ("synthetic_ammonia", "合成氨"),
                        ],
                        db_index=True,
                        max_length=100,
                        verbose_name="产品名称",
                    ),
                ),
                (
                    "authority",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="lca.authority",
                        verbose_name="认证机构",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Region",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=50, verbose_name="地理区域名称")),
                (
                    "level",
                    models.CharField(
                        blank=True,
                        choices=[("province", "省"), ("city", "市"), ("county", "县")],
                        max_length=50,
                        verbose_name="区域级别",
                    ),
                ),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="children",
                        to="lca.region",
                        verbose_name="上级地理区域",
                    ),
                ),
            ],
        ),
        migrations.DeleteModel(
            name="AuthorityCategory",
        ),
        migrations.AddField(
            model_name="certification",
            name="product",
            field=models.CharField(
                choices=[
                    ("lithium_battery", "锂电池"),
                    ("photovoltaic_product", "光伏产品"),
                    ("steel", "钢铁"),
                    ("textile", "纺织品"),
                    ("electronics", "电子电器"),
                    ("tire", "轮胎"),
                    ("cement", "水泥"),
                    ("electrolytic_aluminum", "电解铝"),
                    ("phosphate_fertilizer", "磷铵"),
                    ("wood_product", "木制品"),
                    ("electric_vehicle", "电动汽车"),
                    ("power_battery", "动力电池"),
                    ("glass", "玻璃"),
                    ("ethylene", "乙烯"),
                    ("synthetic_ammonia", "合成氨"),
                ],
                max_length=100,
                null=True,
                verbose_name="产品",
            ),
        ),
        migrations.AddField(
            model_name="model",
            name="product",
            field=models.CharField(
                choices=[
                    ("lithium_battery", "锂电池"),
                    ("photovoltaic_product", "光伏产品"),
                    ("steel", "钢铁"),
                    ("textile", "纺织品"),
                    ("electronics", "电子电器"),
                    ("tire", "轮胎"),
                    ("cement", "水泥"),
                    ("electrolytic_aluminum", "电解铝"),
                    ("phosphate_fertilizer", "磷铵"),
                    ("wood_product", "木制品"),
                    ("electric_vehicle", "电动汽车"),
                    ("power_battery", "动力电池"),
                    ("glass", "玻璃"),
                    ("ethylene", "乙烯"),
                    ("synthetic_ammonia", "合成氨"),
                ],
                max_length=100,
                null=True,
                verbose_name="产品",
            ),
        ),
        migrations.AddField(
            model_name="authorityproduct",
            name="region",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                to="lca.region",
                verbose_name="区域",
            ),
        ),
    ]
