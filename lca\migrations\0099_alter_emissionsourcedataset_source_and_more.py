# Generated by Django 5.2.1 on 2025-07-24 08:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('lca', '0098_emissionsourcedataset_source_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='emissionsourcedataset',
            name='source',
            field=models.JSONField(blank=True, null=True, verbose_name='数据来源'),
        ),
        migrations.AlterField(
            model_name='emissionsourcedatasetapplication',
            name='alias',
            field=models.JSONField(max_length=255, null=True, verbose_name='别名'),
        ),
        migrations.AlterField(
            model_name='emissionsourcedatasetapplication',
            name='allocation_principles',
            field=models.JSONField(blank=True, max_length=255, null=True, verbose_name='分配原则'),
        ),
        migrations.AlterField(
            model_name='emissionsourcedatasetapplication',
            name='data_treatment',
            field=models.JSONField(blank=True, max_length=255, null=True, verbose_name='数据处理'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='emissionsourcedatasetapplication',
            name='functional_unit',
            field=models.JSONField(max_length=100, verbose_name='功能单位'),
        ),
        migrations.AlterField(
            model_name='emissionsourcedatasetapplication',
            name='model_description',
            field=models.JSONField(blank=True, max_length=255, null=True, verbose_name='模型描述'),
        ),
        migrations.AlterField(
            model_name='emissionsourcedatasetapplication',
            name='name',
            field=models.JSONField(verbose_name='名称'),
        ),
        migrations.AlterField(
            model_name='emissionsourcedatasetapplication',
            name='source',
            field=models.JSONField(blank=True, null=True, verbose_name='数据来源'),
        ),
        migrations.AlterField(
            model_name='emissionsourcedatasetapplication',
            name='specs',
            field=models.JSONField(max_length=100, null=True, verbose_name='产品型号'),
        ),
        migrations.AlterField(
            model_name='emissionsourcedatasetapplication',
            name='technical_description',
            field=models.JSONField(max_length=255, null=True, verbose_name='技术描述'),
        ),
        migrations.AlterField(
            model_name='emissionsourcedatasetapplication',
            name='usage',
            field=models.JSONField(max_length=255, null=True, verbose_name='产品或工艺用途'),
        ),
    ]
