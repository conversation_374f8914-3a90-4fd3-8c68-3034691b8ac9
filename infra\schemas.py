from functools import wraps
from typing import Any, Callable, Optional
from ninja.operation import Operation
from django.http import HttpRequest
from ninja import Schema
from pydantic import BaseModel, Field
from ninja.utils import (
    contribute_operation_callback,
)


class Page(BaseModel):
    page: int = Field(default=1, gt=0)
    page_size: int = Field(default=20, gt=0, le=100)


def successful() -> Callable:
    class Output(Schema):
        success: bool = Field(True, title="是否成功")
        message: Optional[str] = Field(None, title="错误提示")

    def wrapper(func: Callable) -> Any:
        return inject(func)

    def inject(func: Callable):
        @wraps(func)
        def view_with_success(request: HttpRequest, **kwargs: Any) -> Any:
            text = func(request, **kwargs)

            result = Output(
                success=True,
            )
            if text.strip() != "":
                result = Output(
                    success=False,
                    message=text,
                )
            return result

        contribute_operation_callback(
            view_with_success,
            response_successful,
        )
        return view_with_success

    def response_successful(operation: Operation):
        item_schema = operation.response_models[200].__fields__["response"].annotation
        new_name = f"Successful_{item_schema.__name__}"
        new_schema = type(
            new_name,
            (Output,),
            {
                "__annotations__": {"message": Optional[item_schema]},  # type: ignore
            },
        )  # typing: ignore

        response = operation._create_response_model(new_schema)
        operation.response_models[200] = response

    return wrapper
