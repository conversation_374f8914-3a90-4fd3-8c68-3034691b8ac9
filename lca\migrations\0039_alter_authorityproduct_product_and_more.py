# Generated by Django 5.2.1 on 2025-05-20 08:13

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0038_remove_certificationinfo_status"),
    ]

    operations = [
        migrations.AlterField(
            model_name="authorityproduct",
            name="product",
            field=models.CharField(
                choices=[
                    ("cement", "水泥"),
                    ("steel", "钢铁"),
                    ("photovoltaic", "光伏"),
                    ("electric_vehicle", "电动汽车"),
                    ("power_battery", "动力电池"),
                    ("glass", "玻璃"),
                    ("electrolytic_aluminum", "电解铝"),
                    ("ethylene", "乙烯"),
                    ("synthetic_ammonia", "合成氨"),
                ],
                db_index=True,
                max_length=100,
                verbose_name="产品名称",
            ),
        ),
        migrations.AlterField(
            model_name="certification",
            name="product",
            field=models.CharField(
                choices=[
                    ("cement", "水泥"),
                    ("steel", "钢铁"),
                    ("photovoltaic", "光伏"),
                    ("electric_vehicle", "电动汽车"),
                    ("power_battery", "动力电池"),
                    ("glass", "玻璃"),
                    ("electrolytic_aluminum", "电解铝"),
                    ("ethylene", "乙烯"),
                    ("synthetic_ammonia", "合成氨"),
                ],
                max_length=100,
                null=True,
                verbose_name="产品",
            ),
        ),
        migrations.AlterField(
            model_name="emmissionsourceproduct",
            name="product",
            field=models.CharField(
                choices=[
                    ("cement", "水泥"),
                    ("steel", "钢铁"),
                    ("photovoltaic", "光伏"),
                    ("electric_vehicle", "电动汽车"),
                    ("power_battery", "动力电池"),
                    ("glass", "玻璃"),
                    ("electrolytic_aluminum", "电解铝"),
                    ("ethylene", "乙烯"),
                    ("synthetic_ammonia", "合成氨"),
                ],
                db_index=True,
                max_length=255,
                verbose_name="产品",
            ),
        ),
        migrations.AlterField(
            model_name="model",
            name="product",
            field=models.CharField(
                choices=[
                    ("cement", "水泥"),
                    ("steel", "钢铁"),
                    ("photovoltaic", "光伏"),
                    ("electric_vehicle", "电动汽车"),
                    ("power_battery", "动力电池"),
                    ("glass", "玻璃"),
                    ("electrolytic_aluminum", "电解铝"),
                    ("ethylene", "乙烯"),
                    ("synthetic_ammonia", "合成氨"),
                ],
                max_length=100,
                null=True,
                verbose_name="产品",
            ),
        ),
    ]
