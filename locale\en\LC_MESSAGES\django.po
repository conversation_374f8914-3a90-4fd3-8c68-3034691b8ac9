# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-18 12:02+0800\n"
"PO-Revision-Date: 2025-07-18 14:00+0000\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: English <<EMAIL>>\n"
"Language: en\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
#: .\config\settings.py:219
msgid "English"
msgstr "English"

#: .\config\settings.py:220
msgid "Simplified Chinese"
msgstr "Simplified Chinese"

#: .\infra\permissions.py:17
msgid "You do not have permission to perform this action."
msgstr "You do not have permission to perform this action."

#: .\lca\resource_center\errors.py:6
msgid "请选择适用区域"
msgstr "Please select applicable region"

#: .\lca\resource_center\errors.py:7
msgid "适用区域参数错误"
msgstr "Applicable region parameter error"

#: .\lca\resource_center\errors.py:8
msgid "相关行业参数错误"
msgstr "Related industry parameter error"

#: .\lca\resource_center\errors.py:9
msgid "文件、链接、内容不能都为空"
msgstr "File, link, and content cannot all be empty"

#: .\lca\resource_center\errors.py:10
msgid "文章链接不是正确的链接地址"
msgstr "The article link is not a valid URL"

#: .\lca\resource_center\errors.py:11
msgid "资料已经发布，请勿重复发布"
msgstr "The material has been published, please do not publish again"

#: .\lca\resource_center\errors.py:12
msgid "资料未发布，请勿重复下架"
msgstr "The material has not been published, please do not repeatedly take down"

#: .\lca\resource_center\errors.py:13
msgid "资料不在草稿箱中，无法删除"
msgstr "The material is not in the draft box, cannot be deleted"

#: .\lca\resource_center\errors.py:14
msgid "文章类型参数错误"
msgstr "Article type parameter error"

#: .\lca\resource_center\errors.py:17
msgid "资料已经发布，无法更新，请先下架"
msgstr "The material has been published, cannot be updated, please take it down first"

#: .\lca\resource_center\errors.py:19
msgid "请先上传正确的封面图片"
msgstr "Please upload a valid cover image first"

#: .\lca\resource_center\errors.py:20
msgid "请先上传正确的文件"
msgstr "Please upload a valid file first"

#: .\lca\resource_center\models.py:12
msgid "政策资讯"
msgstr "Policy News"

#: .\lca\resource_center\models.py:13
msgid "碳资料库"
msgstr "Carbon Resource Library"

#: .\lca\resource_center\models.py:21
msgid "信息公告"
msgstr "Information Notice"

#: .\lca\resource_center\models.py:22
msgid "国内碳资讯"
msgstr "Domestic Carbon News"

#: .\lca\resource_center\models.py:23
msgid "国外碳资讯"
msgstr "International Carbon News"

#: .\lca\resource_center\models.py:24
msgid "研究报告"
msgstr "Research Report"

#: .\lca\resource_center\models.py:25
msgid "标准规范"
msgstr "Standards and Regulations"

#: .\lca\resource_center\models.py:33
msgid "国际标准"
msgstr "International Standards"

#: .\lca\resource_center\models.py:34
msgid "国家标准"
msgstr "National Standards"

#: .\lca\resource_center\models.py:35
msgid "行业标准"
msgstr "Industry Standards"

#: .\lca\resource_center\models.py:36
msgid "地方标准"
msgstr "Local Standards"

#: .\lca\resource_center\models.py:37
msgid "团体标准"
msgstr "Association Standards"

#: .\lca\resource_center\models.py:45
msgid "现行"
msgstr "Current"

#: .\lca\resource_center\models.py:46
msgid "试行"
msgstr "Trial"

#: .\lca\resource_center\models.py:47
msgid "编制中"
msgstr "In Preparation"

#: .\lca\resource_center\models.py:55
msgid "草稿"
msgstr "Draft"

#: .\lca\resource_center\models.py:56
msgid "已发布"
msgstr "Published"

#: .\lca\resource_center\models.py:75
msgid "标题"
msgstr "Title"

#: .\lca\resource_center\models.py:81
msgid "简介"
msgstr "Introduction"

#: .\lca\resource_center\models.py:84
msgid "内容"
msgstr "Content"

#: .\lca\resource_center\models.py:85
msgid "发布日期"
msgstr "Publication Date"

#: .\lca\resource_center\models.py:88
msgid "发布组织"
msgstr "Publishing Organization"

#: .\lca\resource_center\models.py:96
msgid "相关行业"
msgstr "Related Industries"

#: .\lca\resource_center\models.py:105
msgid "宣传封面"
msgstr "Promotional Cover"

#: .\lca\resource_center\models.py:114
msgid "文件上传"
msgstr "File Upload"

#: .\lca\resource_center\models.py:122
msgid "文章链接"
msgstr "Article Link"

#: .\lca\resource_center\models.py:128
msgid "分类组别"
msgstr "Category Group"

#: .\lca\resource_center\models.py:134
msgid "内容类型"
msgstr "Content Type"

#: .\lca\resource_center\models.py:141
msgid "状态"
msgstr "Status"

#: .\lca\resource_center\models.py:144
msgid "发布时间"
msgstr "Publish Time"

#: .\lca\resource_center\models.py:148
msgid "下架时间"
msgstr "Unpublish Time"

#: .\lca\resource_center\models.py:162
msgid "标准号"
msgstr "Standard Number"

#: .\lca\resource_center\models.py:170
msgid "标准范围"
msgstr "Standard Scope"

#: .\lca\resource_center\models.py:176
msgid "适用区域"
msgstr "Applicable Region"

#: .\lca\resource_center\models.py:188
msgid "实施状态"
msgstr "Implementation Status"

#: .\lca\resource_center\models.py:194
msgid "实施日期"
msgstr "Implementation Date"

#: .\lca\resource_center\models.py:199 .\lca\resource_center\models.py:200
msgid "政策信息库"
msgstr "Policy Information Library"

#: .\lca\users\models.py:278
msgid "staff status"
msgstr "Staff Status"

#: .\lca\users\models.py:280
msgid "Designates whether the user can log into this admin site."
msgstr "Designates whether the user can log into this admin site."

#: .\lca\users\models.py:283
msgid "active"
msgstr "Active"

#: .\lca\users\models.py:286
msgid ""
"Designates whether this user should be treated as active. Unselect this "
"instead of deleting accounts."
msgstr ""
"Designates whether this user should be treated as active. Unselect this "
"instead of deleting accounts."

#: .\lca\users\models.py:295
msgid "user"
msgstr "User"

#: .\lca\users\models.py:296
msgid "users"
msgstr "Users"