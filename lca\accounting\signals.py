from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from lca.accounting.models import Input, LifeCycle, Output, Process
from lca.accounting.services import LifeCycleService, ModelService


@receiver(post_save, sender=Process)
def on_save_process(sender, instance: Process, created, **kwargs):
    if created:
        print(f"新建过程: {instance} =============================================================")
    else:
        print(f"更新过程: {instance} =============================================================")
        input = instance.input
        if input is not None:
            LifeCycleService.calc_input_co2e(input)
        life_cycle = instance.life_cycle
        if life_cycle is not None:
            life_cycle.co2e = instance.co2e
            life_cycle.save()


@receiver(post_delete, sender=Process)
def on_delete_process(sender, instance, **kwargs):
    print(f"删除过程: {instance} =============================================================")
    # 更新所有上级
    for life_cycle in LifeCycle.objects.filter(process=instance).all():
        life_cycle.co2e = instance.co2e
        life_cycle.save()

    for input in Input.objects.filter(process=instance).all():
        input.co2e = instance.co2e
        input.save()


@receiver(post_save, sender=Input)
def on_save_input(sender, instance: Input, created, **kwargs):
    print(f"更新输入: {instance} co2e = {instance.co2e}=============================================================")
    process = instance.parent_process
    LifeCycleService.calc_process_co2e(process=process)


@receiver(post_delete, sender=Input)
def on_delete_input(sender, instance: Input, **kwargs):
    print(f"删除输入: {instance} co2e = {instance.co2e}=============================================================")
    process = instance.parent_process
    LifeCycleService.calc_process_co2e(process=process)


@receiver(post_save, sender=Output)
def on_save_output(sender, instance: Output, created, **kwargs):
    print(f"输出: {instance} =============================================================")
    process = instance.process
    LifeCycleService.calc_process_co2e(process=process)


@receiver(post_delete, sender=Output)
def on_delete_output(sender, instance: Output, **kwargs):
    print(f"删除输出: {instance}")
    process = instance.process
    LifeCycleService.calc_process_co2e(process=process)


@receiver(post_save, sender=LifeCycle)
def on_save_life_cycle(sender, instance: LifeCycle, created, **kwargs):
    print(f"life cycle: {instance} =============================================================")
    model = instance.model

    ModelService.calc_model_co2e(model)
