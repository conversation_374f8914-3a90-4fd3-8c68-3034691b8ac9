from datetime import datetime
import random
from config import settings
from infra.types import Code, Phone
from lca.certification.models import Authority, AuthorityProduct, Manufacturer
from lca.common.Dysms import Dysms
from lca.users.models import (
    USER_CERTIFICATION_STATUS,
    USER_TYPES,
    AuthorityCertification,
    Group,
    ManufacturerCertification,
    Permission,
    User,
    UserCertification,
)
from lca.users.schema import (
    CreateUserSchema,
    GroupInSchema,
    RegisterInSchema,
    UserCertificationInSchema,
)
from ztbory_django_shield.base.shield import slide_captcha_shield
from django.core.cache import cache
from django.contrib.auth import login, logout
from django.db import transaction
from django.db.models import Q
from ninja.errors import HttpError


class UserService:
    @staticmethod
    def send_code_sms(phone: Phone, uuid: str, secret: str):
        print(phone, uuid, secret)
        """发送验证码短信"""
        if slide_captcha_shield.validate_captcha_secret(uuid, secret):
            # 如果成功，将 phone 设置为可用
            dysms = Dysms()
            code = UserService.gen_code()
            dysms.send_sms(phone, settings.ALIBABA_CLOUD_SMS_CODE_TEMPLATE, {"code": code})
            cache.set(UserService.gen_phone_key(phone), code, 60 * 10)
            return True
        raise HttpError(422, "滑块校验失败")

    @staticmethod
    def verify_code(phone: Phone, code: Code):
        """校验验证码"""
        return
        # TODO
        cache_code = cache.get(UserService.gen_phone_key(phone))
        cache.delete(UserService.gen_phone_key(phone))
        if cache_code is not None:
            if cache_code == code:
                return
        raise HttpError(422, "验证码错误")

    @staticmethod
    def gen_phone_key(phone: Phone) -> str:
        return f"user:code:{phone}"

    @staticmethod
    def gen_code() -> str:
        return f"{random.randint(0, 999999):06d}"

    @staticmethod
    def login(request, phone):
        user = User.objects.filter(phone=phone).first()
        if user is None:
            raise HttpError(401, "用户不存在")
        if user.is_active is False:
            raise HttpError(403, "用户已被禁用")
        login(request, user)
        return user

    @staticmethod
    def logout(request):
        logout(request)

    @staticmethod
    def register(request, data: RegisterInSchema):
        phone = data.phone
        nickname = data.nickname
        user = User.objects.filter(phone=phone).first()
        if user is not None:
            raise HttpError(422, "手机号已存在")

        user = User.objects.create(
            phone=phone,
            nickname=nickname,
            certificate_status="none",
            last_login=datetime.now(),
            is_superuser=False,
            is_staff=False,
            is_active=True,
            date_joined=datetime.now(),
        )
        login(request, user)
        return user

    @staticmethod
    def update_phone(user: User, phone: Phone):
        if User.objects.filter(phone=phone, id__ne=user.id).exists():
            raise HttpError(422, "手机号已存在")
        user.phone = phone
        user.save()
        return user

    @staticmethod
    def submit_user_certification(user: User, data: UserCertificationInSchema):
        # 保证当前只有一个正在进行中的认证
        with transaction.atomic():
            UserCertification.objects.select_for_update().filter(user=user)
            last = UserCertification.objects.filter(
                user=user, status__in=[USER_CERTIFICATION_STATUS.ONGOING.value]
            ).first()
            manufacturer_certification = None
            authority_certification = None
            # 员工不能认证
            if user.is_staff:
                raise HttpError(422, "员工不能认证")
            # 如果用户已经认证通过，不能修改认证类型
            if user.certificate_status == USER_CERTIFICATION_STATUS.APPROVED.value:
                if user.type != data.type:
                    raise HttpError(422, "用户已经认证通过，不能修改认证类型")
            if last is not None:
                raise HttpError(422, "当前有一个正在进行中的认证")
            if data.type == USER_TYPES.MANUFACTURER.value:
                if data.manufacturer_certification is None:
                    raise HttpError(422, "企业认证信息不能为空")

                manufacturer_certification = ManufacturerCertification.objects.create(
                    **data.manufacturer_certification.model_dump()
                )
            if data.type == USER_TYPES.AUTHORITY.value:
                if data.authority_certification is None:
                    raise HttpError(422, "认证机构认证信息不能为空")

                authority_certification = AuthorityCertification.objects.create(
                    **data.authority_certification.model_dump()
                )

            if (
                user.certificate_status == USER_CERTIFICATION_STATUS.NONE.value
                or user.certificate_status == USER_CERTIFICATION_STATUS.REJECTED.value
            ):
                user.certificate_status = USER_CERTIFICATION_STATUS.ONGOING.value
                user.type = data.type
                user.save()
            return UserCertification.objects.create(
                user=user,
                status=USER_CERTIFICATION_STATUS.ONGOING.value,
                type=data.type,
                manufacturer_certification=manufacturer_certification,
                authority_certification=authority_certification,
            )

    @staticmethod
    def get_all_permissions():
        return Permission.objects.order_by("order").all()

    @staticmethod
    def create_user(data: CreateUserSchema):
        # 判断用户是否存在
        if User.objects.filter(phone=data.phone).exists():
            raise HttpError(422, "手机号已存在")
        with transaction.atomic():
            user = User.objects.create(
                phone=data.phone,
                nickname=data.nickname,
                is_active=data.is_active,
                certificate_status=USER_CERTIFICATION_STATUS.NONE.value,
                last_login=datetime.now(),
                is_superuser=False,  # 员工默认不是超级管理员
                is_staff=True,
                description=data.description,
                date_joined=datetime.now(),
            )
            for group_id in data.groups:
                group = Group.objects.get(pk=group_id)
                user.groups.add(group)
            UserService.guess_goverment_type(user)  # 自动设置政府类型
        return user

    @staticmethod
    def guess_goverment_type(user: User):
        # 判断如果是员工，设置了政府部门的角色，就设置为政府类型，否则设置为None
        if user.is_staff is False:
            return
        if user.groups.filter(is_government=True).exists() is False:
            user.type = USER_TYPES.GOVERNMENT.value
            user.certificate_status = USER_CERTIFICATION_STATUS.APPROVED.value
        else:
            user.type = None
        user.save()

    @staticmethod
    def update_user(user: User, data: CreateUserSchema):
        if user.is_superuser:
            raise HttpError(403, "不能修改超级管理员")
        # 如果不是员工，只能修改昵称、手机号和是否禁用
        if user.is_staff is False:
            data.groups = []
        with transaction.atomic():
            for key, value in data.model_dump(exclude=["groups"]).items():
                setattr(user, key, value)
            user.save()
            user.groups.clear()
            for group_id in data.groups:
                group = Group.objects.get(pk=group_id)
                user.groups.add(group)
            UserService.guess_goverment_type(user)  # 自动设置政府类型
        return user

    @staticmethod
    def create_group(data: GroupInSchema, user: User):
        return Group.objects.create(creator=user, **data.model_dump())

    @staticmethod
    def update_group(group: Group, data: GroupInSchema):
        for key, value in data.model_dump().items():
            setattr(group, key, value)
        group.save()
        return group

    @staticmethod
    def update_group_permissions(group: Group, data: list[int]):
        with transaction.atomic():
            group.permissions.clear()
            for permission_id in data:
                permission = Permission.objects.get(pk=permission_id)
                group.permissions.add(permission)
        return group

    @staticmethod
    def search_current_certification(type: USER_TYPES | None = None):
        query = UserCertification.objects.filter(status=USER_CERTIFICATION_STATUS.ONGOING.value)
        if type is not None:
            query = query.filter(type=type.value)
        return query.all()

    @staticmethod
    def search_certification_logs(type: USER_TYPES | None = None, status: USER_CERTIFICATION_STATUS | None = None):
        query = UserCertification.objects.filter(~Q(status=USER_CERTIFICATION_STATUS.ONGOING.value)).order_by(
            "-finish_time"
        )
        if type is not None:
            query = query.filter(type=type.value)
        if status is not None:
            query = query.filter(status=status.value)
        return query.all()

    @staticmethod
    def get_user_list(
        group_id: str | None = None,
        is_active: bool | None = None,
        certificate_status: USER_CERTIFICATION_STATUS | None = None,
    ):
        """获取用户列表，支持按角色ID、是否有效、认证状态进行过滤"""
        query = User.objects.all()

        # 按角色ID过滤
        if group_id is not None:
            query = query.filter(groups__id=group_id)

        # 按是否有效过滤
        if is_active is not None:
            query = query.filter(is_active=is_active)

        # 按认证状态过滤
        if certificate_status is not None:
            query = query.filter(certificate_status=certificate_status.value)

        # 保持原有的排序逻辑：超级管理员(id=1)在前，其他按id倒序
        return query.order_by("-is_superuser", "-id").all()

    @staticmethod
    def accept_user_certification(certification: UserCertification, user: User):
        with transaction.atomic():
            certification = UserCertification.objects.select_for_update().get(pk=certification.id)
            if certification.status != USER_CERTIFICATION_STATUS.ONGOING.value:
                raise HttpError(403, "当前状态不允许该操作")
            certification.status = USER_CERTIFICATION_STATUS.APPROVED.value
            certification.finish_time = datetime.now()
            certification.operator = user
            certification.save()
            certification.user.certificate_status = USER_CERTIFICATION_STATUS.APPROVED.value
            certification.user.type = certification.type
            if certification.type == USER_TYPES.MANUFACTURER.value:
                certification.user.manufacturer_certification = certification.manufacturer_certification
            if certification.type == USER_TYPES.AUTHORITY.value:
                certification.user.authority_certification = certification.authority_certification
            certification.user.save()

            # 认证完成后，生成或修改对应的认证机构或生产企业
            if certification.type == USER_TYPES.MANUFACTURER.value:
                Manufacturer.objects.update_or_create(
                    user=certification.user,
                    defaults=dict(
                        name=certification.manufacturer_certification.name,
                        code=certification.manufacturer_certification.code,
                        geography=certification.manufacturer_certification.geography,
                        address=certification.manufacturer_certification.address,
                        contact_name=certification.manufacturer_certification.contact_name,
                        contact_phone=certification.manufacturer_certification.contact_phone,
                        contact_email=certification.manufacturer_certification.contact_email,
                        products=certification.manufacturer_certification.products,
                        website=certification.manufacturer_certification.website,
                        license_id=certification.manufacturer_certification.license.id,
                        description=certification.manufacturer_certification.description,
                    ),
                )
            if certification.type == USER_TYPES.AUTHORITY.value:
                Authority.objects.update_or_create(
                    user=certification.user,
                    defaults=dict(
                        name=certification.authority_certification.name,
                        code=certification.authority_certification.code,
                        geography=certification.authority_certification.geography,
                        address=certification.authority_certification.address,
                        approval_number=certification.authority_certification.approval_number,
                        contact_name=certification.authority_certification.contact_name,
                        contact_phone=certification.authority_certification.contact_phone,
                        contact_email=certification.authority_certification.contact_email,
                        products=certification.authority_certification.products,
                        approval_license_id=certification.authority_certification.approval_license.id,
                        sign_id=certification.authority_certification.sign.id,
                        logo_id=certification.authority_certification.logo.id,
                        license_id=certification.authority_certification.license.id,
                        description=certification.authority_certification.description,
                    ),
                )

                # 批量导入到 authority_product 里
                AuthorityProduct.objects.filter(authority=certification.user.authority).delete()
                for product in certification.authority_certification.products:
                    AuthorityProduct.objects.create(authority=certification.user.authority, product=product)
        return UserCertification.objects.get(pk=certification.id)

    @staticmethod
    def reject_user_certification(certification: UserCertification, reason: str, user: User):
        with transaction.atomic():
            certification = UserCertification.objects.select_for_update().get(pk=certification.id)
            if certification.status != USER_CERTIFICATION_STATUS.ONGOING.value:
                raise HttpError(403, "当前状态不允许该操作")
            certification.status = USER_CERTIFICATION_STATUS.REJECTED.value
            certification.reason = reason
            certification.operator = user
            certification.finish_time = datetime.now()
            certification.save()
            if certification.user.certificate_status == USER_CERTIFICATION_STATUS.ONGOING.value:
                certification.user.certificate_status = USER_CERTIFICATION_STATUS.REJECTED.value
                certification.user.save()
        return UserCertification.objects.get(pk=certification.id)
