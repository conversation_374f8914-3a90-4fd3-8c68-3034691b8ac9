# Generated by Django 5.1.7 on 2025-04-28 03:20

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0030_remove_model_analysis_report_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="emissionsource",
            name="type",
            field=models.CharField(
                choices=[
                    ("raw-material", "原辅料"),
                    ("energy", "能源"),
                    ("natural-resource", "自然资源"),
                    ("packaging", "包装"),
                    ("transport", "运输"),
                    ("infrastructure", "基础设备设施"),
                    ("recycle-material", "再生原料"),
                    ("material-content", "材料/成分含量"),
                ],
                max_length=50,
                verbose_name="排放源类型",
            ),
        ),
        migrations.AlterField(
            model_name="input",
            name="type",
            field=models.CharField(
                choices=[
                    ("raw-material", "原辅料"),
                    ("energy", "能源"),
                    ("natural-resource", "自然资源"),
                    ("packaging", "包装"),
                    ("transport", "运输"),
                    ("infrastructure", "基础设备设施"),
                    ("recycle-material", "再生原料"),
                    ("material-content", "材料/成分含量"),
                ],
                max_length=50,
                verbose_name="原材料类型",
            ),
        ),
    ]
