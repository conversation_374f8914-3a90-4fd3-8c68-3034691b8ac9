import base64
import os
import random
from io import BytesIO

from PIL import Image


class Trimmer:
    """图片裁剪类，用于生成校验码的前景图片、背景图片"""

    def __init__(self, image: Image, width, height):
        self.origin = image
        self.width = width
        self.height = height
        self._point = None

    @property
    def size(self):
        width, height = self.origin.size
        return int(width * (self.width / width)), int(height * (self.height / height))

    def get_file_path(self, filename):
        return os.path.join(os.path.dirname(os.path.abspath(__file__)), "resources", filename)

    def range_x_axis(self):
        return 100, self.size[0] - 100

    def range_y_axis(self):
        return 30, self.size[1] - 80

    def random_point(self):
        point = (
            random.randint(*self.range_x_axis()),
            random.randint(*self.range_y_axis()),
        )
        self._point = point

    def set_point(self, point):
        self._point = point

    @property
    def point(self):
        if self._point is not None:
            return self._point
        self.random_point()
        return self._point

    @property
    def background(self):
        backend = self.origin.copy().resize(self.size)
        mark = Image.open(self.get_file_path("cover.png"))

        buffered = BytesIO()
        background = Image.alpha_composite(Image.new("RGBA", backend.size), backend.convert("RGBA"))
        background.paste(mark.convert("RGBA"), self.point, mark)
        background.save(buffered, format="PNG")
        buffered.seek(0)
        return buffered

    @property
    def base64_background(self):
        return "data:image/png;base64," + base64.b64encode(self.background.read()).decode("utf-8")

    @property
    def foreground(self):
        backend = self.origin.copy().resize(self.size)
        cropped = backend.crop((self.point[0], 0, self.point[0] + 50, self.size[1]))

        buffered = BytesIO()
        mask_square = Image.open(self.get_file_path("mask.png")).convert("L")
        mask = Image.new("L", (50, self.size[1]), 0)
        mask.paste(mask_square, (0, self.point[1]))
        foreground = Image.new("RGBA", cropped.size)
        foreground.paste(cropped, (0, 0), mask)
        foreground.save(buffered, format="PNG")
        buffered.seek(0)
        return buffered

    @property
    def base64_foreground(self):
        return "data:image/png;base64," + base64.b64encode(self.foreground.read()).decode("utf-8")
