from infra.PageNumberPagination import PageNumberPagination
from ninja import Query, Router
from ninja.pagination import paginate
from lca.accounting.models import EmissionSource, INPUT_TYPES
from lca.accounting.schema import EmmissionSourceSchema
from lca.database.models import EmissionSourceInput, EmissionSourceOutput
from lca.database.schema import EmmissionSourceDetailSchema
from lca.database.services import DatabaseService
from django.db.models import Prefetch
from django.http import HttpResponse


router = Router(tags=["数据库"])


@router.get(
    "",
    response=list[EmmissionSourceSchema],
    summary="搜索",
    operation_id="searchEmmissionSource",
)
@paginate(PageNumberPagination)
def search_emmission_source(
    request,
    text: str = Query(None, title="搜索关键字"),
    geography_id: str = Query(None, title="地理ID"),
    year: int = Query(None, title="年份"),
    type: INPUT_TYPES = Query(None, title="类型"),
    category_id: str = Query(None, title="分类ID"),
):
    data = dict(
        text=text,
        geography_id=geography_id,
        year=year,
        type=type,
        category_id=category_id,
    )
    data = {k: v for k, v in data.items() if v not in ("", None)}

    return DatabaseService.search(**data)


@router.get(
    "emmission-source/{id}",
    response=EmmissionSourceDetailSchema,
    summary="获取排放源详情",
    operation_id="getEmmissionSource",
)
def get_emmission_source(request, id: int):
    return EmissionSource.objects.prefetch_related(
        Prefetch(
            "einputs",
            queryset=EmissionSourceInput.objects.prefetch_related("flow", "unit"),
        ),
        Prefetch(
            "eoutputs",
            queryset=EmissionSourceOutput.objects.prefetch_related("flow", "unit"),
        ),
    ).get(id=id)


@router.get(
    "emmission-source/{id}/export",
    response=str,
    summary="导出排放源",
    description="返回值是下载链接",
    operation_id="exportEmmissionSource",
)
def export_emmission_source(request, id: int):
    process = EmissionSource.objects.get(id=id)
    content = DatabaseService.export_process_ilcd(process)
    response = HttpResponse(content, content_type="application/json")
    response["Content-Disposition"] = f'attachment; filename="process_{process.uuid}.json"'

    return response
