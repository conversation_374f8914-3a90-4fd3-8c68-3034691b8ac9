# Generated by Django 5.2.1 on 2025-06-26 23:43

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lca", "0048_model_analysis_report_alter_authorityproduct_product_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="Formula",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200, verbose_name="公式名称")),
                ("class_path", models.Char<PERSON>ield(max_length=200, verbose_name="类路径")),
            ],
        ),
        migrations.RemoveField(
            model_name="process",
            name="craft",
        ),
        migrations.RemoveField(
            model_name="distribution",
            name="model",
        ),
        migrations.RemoveField(
            model_name="distributionroute",
            name="distribution",
        ),
        migrations.RemoveField(
            model_name="distributionroute",
            name="emmission_source",
        ),
        migrations.RemoveField(
            model_name="endoflifemodel",
            name="product",
        ),
        migrations.RemoveField(
            model_name="eolstage",
            name="eol_model",
        ),
        migrations.RemoveField(
            model_name="eolstage",
            name="model",
        ),
        migrations.RemoveField(
            model_name="eolstage",
            name="product",
        ),
        migrations.RemoveField(
            model_name="output",
            name="green_house_gas",
        ),
        migrations.RemoveField(
            model_name="usagemaintenance",
            name="model",
        ),
        migrations.RemoveField(
            model_name="usagemaintenance",
            name="usage_unit",
        ),
        migrations.RemoveField(
            model_name="usagemaintenanceitem",
            name="usage_maintenance",
        ),
        migrations.RemoveField(
            model_name="usagemaintenanceitem",
            name="maintenance_unit",
        ),
        migrations.RenameField(
            model_name="transport",
            old_name="emmission_source",
            new_name="emission_source",
        ),
        migrations.RemoveField(
            model_name="emissionsource",
            name="average_trans_distance",
        ),
        migrations.RemoveField(
            model_name="emissionsource",
            name="transport_type",
        ),
        migrations.RemoveField(
            model_name="input",
            name="emmission_source",
        ),
        migrations.RemoveField(
            model_name="input",
            name="files",
        ),
        migrations.RemoveField(
            model_name="input",
            name="model",
        ),
        migrations.RemoveField(
            model_name="output",
            name="input",
        ),
        migrations.AddField(
            model_name="flow",
            name="gwp",
            field=models.DecimalField(
                blank=True,
                decimal_places=5,
                max_digits=20,
                null=True,
                verbose_name="问世气体当量",
            ),
        ),
        migrations.AddField(
            model_name="input",
            name="emission_source",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="lca.emissionsource",
                verbose_name="排放源",
            ),
        ),
        migrations.AddField(
            model_name="input",
            name="parent_process",
            field=models.ForeignKey(
                default=None,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="inputs",
                to="lca.process",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="input",
            name="process",
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to="lca.process"),
        ),
        migrations.AddField(
            model_name="input",
            name="source_type",
            field=models.CharField(
                choices=[
                    ("process", "单元过程"),
                    ("emission-source", "排放因子"),
                    ("custom-factor", "自定义因子"),
                ],
                default="emission-source",
                max_length=50,
                verbose_name="数据来源",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="output",
            name="flow",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="lca.flow",
                verbose_name="流",
            ),
        ),
        migrations.AddField(
            model_name="process",
            name="allocation_method",
            field=models.CharField(
                choices=[
                    ("system-expansion", "系统扩展法"),
                    ("mass", "质量分配"),
                    ("energy", "热值分配"),
                    ("economic", "经济价值分配"),
                    ("other", "其他分配方法"),
                    ("no", "无需分配"),
                ],
                default=None,
                max_length=50,
                null=True,
                verbose_name="分配方法",
            ),
        ),
        migrations.AlterField(
            model_name="emissionsource",
            name="type",
            field=models.CharField(
                choices=[("raw-material", "原辅料"), ("energy", "能源")],
                max_length=50,
                verbose_name="排放源类型",
            ),
        ),
        migrations.AlterField(
            model_name="input",
            name="co2e",
            field=models.DecimalField(decimal_places=5, default=0, max_digits=20, verbose_name="CO2e"),
        ),
        migrations.AlterField(
            model_name="input",
            name="type",
            field=models.CharField(
                choices=[("raw-material", "原辅料"), ("energy", "能源")],
                max_length=50,
                verbose_name="原材料类型",
            ),
        ),
        migrations.AlterField(
            model_name="output",
            name="emission_source",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="lca.emissionsource",
                verbose_name="回收排放源",
            ),
        ),
        migrations.AlterField(
            model_name="output",
            name="process",
            field=models.ForeignKey(
                default=None,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="outputs",
                to="lca.process",
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="output",
            name="type",
            field=models.CharField(
                choices=[
                    ("main-product", "主产品"),
                    ("by-product", "共生产品"),
                    ("environmental-emission", "直接排放"),
                    ("waste", "待处置废弃物"),
                ],
                default="main-product",
                max_length=50,
                verbose_name="输出类型",
            ),
        ),
        migrations.AlterField(
            model_name="process",
            name="co2e",
            field=models.DecimalField(decimal_places=5, default=0, max_digits=20, verbose_name="CO2e"),
        ),
        migrations.CreateModel(
            name="Allocation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "percent",
                    models.DecimalField(
                        decimal_places=5,
                        max_digits=20,
                        null=True,
                        verbose_name="百分比",
                    ),
                ),
                (
                    "description",
                    models.CharField(blank=True, max_length=200, null=True, verbose_name="描述"),
                ),
                (
                    "amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=10,
                        max_digits=20,
                        null=True,
                        verbose_name="替代数量",
                    ),
                ),
                (
                    "emission_source",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.emissionsource",
                        verbose_name="排放源",
                    ),
                ),
                (
                    "unit",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.unit",
                        verbose_name="单位",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="output",
            name="allocation",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="lca.allocation",
                verbose_name="分配",
            ),
        ),
        migrations.CreateModel(
            name="CustomFactor",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "source",
                    models.CharField(
                        choices=[
                            ("literature", "文献报告"),
                            ("industry", "行业数据"),
                            ("measured", "实测数据"),
                            ("theoretical", "理论计算"),
                            ("other", "其他"),
                        ],
                        max_length=50,
                        verbose_name="自定义因子数据来源来源",
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(decimal_places=5, default=1, max_digits=20, verbose_name="数量"),
                ),
                (
                    "co2e",
                    models.DecimalField(decimal_places=5, max_digits=20, verbose_name="二氧化碳当量"),
                ),
                (
                    "description",
                    models.CharField(blank=True, max_length=200, null=True, verbose_name="描述"),
                ),
                (
                    "unit",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.unit",
                        verbose_name="单位",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="allocation",
            name="custom_factor",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="lca.customfactor",
                verbose_name="自定义因子",
            ),
        ),
        migrations.AddField(
            model_name="input",
            name="custom_factor",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="lca.customfactor",
            ),
        ),
        migrations.AddField(
            model_name="output",
            name="custom_factor",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="lca.customfactor",
                verbose_name="自定义因子",
            ),
        ),
        migrations.CreateModel(
            name="Calculater",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200, verbose_name="公式名称")),
                (
                    "formulas",
                    models.ManyToManyField(related_name="formulas", to="lca.formula"),
                ),
            ],
        ),
        migrations.CreateModel(
            name="LifeCycle",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "stage",
                    models.CharField(
                        choices=[
                            ("production", "生产阶段"),
                            ("use", "使用阶段"),
                            ("distribution", "分销阶段"),
                            ("disposal", "废弃阶段"),
                        ],
                        default="production",
                        max_length=50,
                        verbose_name="生命周期阶段",
                    ),
                ),
                (
                    "co2e",
                    models.DecimalField(decimal_places=5, default=0, max_digits=20, verbose_name="CO2e"),
                ),
                (
                    "model",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="life_cycles",
                        to="lca.model",
                        verbose_name="模型",
                    ),
                ),
                (
                    "process",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.process",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="OutputFormula",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "calculater",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.calculater",
                        verbose_name="计算器",
                    ),
                ),
                (
                    "formula",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="lca.formula",
                        verbose_name="公式",
                    ),
                ),
            ],
            options={
                "unique_together": {("calculater", "formula")},
            },
        ),
        migrations.DeleteModel(
            name="Craft",
        ),
        migrations.DeleteModel(
            name="Distribution",
        ),
        migrations.DeleteModel(
            name="DistributionRoute",
        ),
        migrations.DeleteModel(
            name="EndofLifeModel",
        ),
        migrations.DeleteModel(
            name="EOLStage",
        ),
        migrations.DeleteModel(
            name="Product",
        ),
        migrations.DeleteModel(
            name="GreenhouseGas",
        ),
        migrations.DeleteModel(
            name="UsageMaintenance",
        ),
        migrations.DeleteModel(
            name="UsageMaintenanceItem",
        ),
    ]
