import os
import tempfile
import uuid
from datetime import date, datetime, timedelta
from django.conf import settings
from django.db import models
from dateutil.tz import tz
from ninja.errors import HttpError
from docxtpl import DocxTemplate

from lca.file.models import File
from lca.file.service import FileService


def uuid1():
    return uuid.uuid1().hex


cn = tz.gettz("Asia/Shanghai")


def now_tz_aware():
    return DatetimeISO.now(tz=cn)


class DatetimeISO(datetime):
    def __str__(self):
        return self.isoformat()


def local_datetime_iso():
    return DatetimeISO.now(tz=cn).replace(hour=0, minute=0, second=0, microsecond=0)


def to_local_datetime(dt: date):
    return DatetimeISO(dt.year, dt.month, dt.day, hour=0, minute=0, second=0, microsecond=0, tzinfo=cn)


def to_time_range_tz(start_date: date, end_date: date, raise_exception: bool = True) -> (DatetimeISO, DatetimeISO):
    if not start_date:
        start_date = local_datetime_iso()
    else:
        start_date = to_local_datetime(start_date)
    if not end_date:
        end_date = local_datetime_iso()
    else:
        end_date = to_local_datetime(end_date)
    end_date = end_date + timedelta(days=1)

    if start_date >= end_date and raise_exception:
        raise HttpError(400, "开始时间应小于结束时间")

    return start_date, end_date


def label_to_value(label: str, choices: models.TextChoices):
    """获取枚举值"""
    values = {label: value for value, label in choices.choices}
    return values.get(label, None)


def render_docx(template_filename: str, name: str, context: dict) -> File:
    """生成word模板"""
    template_path = os.path.join(settings.TEMPLATE_ROOT, template_filename)
    doc = DocxTemplate(template_path)
    doc.render(context)
    with tempfile.NamedTemporaryFile(suffix=".docx", delete=False) as tmp:
        doc.save(tmp.name)
        file = FileService.upload_file(tmp.name, name)
        return file
