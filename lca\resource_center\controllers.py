from typing import List, Optional

from ninja import Query, Router
from ninja.pagination import paginate

from infra.PageNumberPagination import PageNumberPagination
from lca.resource_center.models import InformationStatus, ResourceType
from lca.resource_center.schema import (
    AllResourceCenterOutSchema,
)
from lca.resource_center.services import ResourceCenterService


router = Router(tags=["资料库"])


@router.get(
    "",
    response=List[AllResourceCenterOutSchema],
    summary="获取资料库列表（前台）",
    description="所有数据",
    operation_id="listResourceCenter",
)
@paginate(PageNumberPagination)
def list_resource_center(
    request,
    content_type: ResourceType = Query(None, title="内容类型"),
    title: Optional[str] = Query(None, title="文章标题"),
    publish_organization: Optional[str] = Query(None, title="发布组织"),
):
    return ResourceCenterService.list(InformationStatus.PUBLISHED, content_type, title, publish_organization)


@router.get(
    "/{id_}",
    response=AllResourceCenterOutSchema,
    summary="获取资料库详情（前台）",
    description="所有数据",
    operation_id="getResourceCenterDetail",
)
def get_resource_center_detail(request, id_: int):
    return ResourceCenterService.get_detail(id_, InformationStatus.PUBLISHED)
