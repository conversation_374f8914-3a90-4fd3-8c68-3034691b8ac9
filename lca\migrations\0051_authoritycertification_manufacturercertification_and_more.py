# Generated by Django 5.2.1 on 2025-07-03 05:53

import django.contrib.auth.models
import django.contrib.postgres.fields
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
        ("lca", "0050_remove_authorityproduct_region_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="AuthorityCertification",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="认证机构名称")),
                (
                    "code",
                    models.CharField(max_length=100, verbose_name="统一社会信用代码"),
                ),
                (
                    "address",
                    models.CharField(max_length=100, verbose_name="机构详细地址"),
                ),
                (
                    "approval_number",
                    models.Char<PERSON>ield(max_length=100, verbose_name="机构批准号"),
                ),
                (
                    "contact_name",
                    models.CharField(max_length=100, verbose_name="联系人名称"),
                ),
                (
                    "contact_phone",
                    models.CharField(max_length=100, verbose_name="联系人手机"),
                ),
                (
                    "contact_email",
                    models.CharField(max_length=100, verbose_name="联系人邮箱"),
                ),
                (
                    "products",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(
                            choices=[
                                ("lithium_battery", "锂电池"),
                                ("photovoltaic", "光伏产品"),
                                ("steel", "钢铁"),
                                ("textile", "纺织品"),
                                ("electronics", "电子电器"),
                                ("tire", "轮胎"),
                                ("cement", "水泥"),
                                ("electrolytic_aluminum", "电解铝"),
                                ("phosphate_fertilizer", "磷铵"),
                                ("wood_product", "木制品"),
                                ("electric_vehicle", "电动汽车"),
                                ("power_battery", "动力电池"),
                                ("glass", "玻璃"),
                                ("ethylene", "乙烯"),
                                ("synthetic_ammonia", "合成氨"),
                            ],
                            max_length=100,
                        ),
                        size=None,
                        verbose_name="认证产品",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "approval_license",
                    models.ForeignKey(
                        blank=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="authority_approval_license",
                        to="lca.file",
                        verbose_name="批准证书",
                    ),
                ),
                (
                    "geography",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="authority_geography",
                        to="lca.geography",
                        verbose_name="机构地区",
                    ),
                ),
                (
                    "license",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="authority_license",
                        to="lca.file",
                        verbose_name="营业执照",
                    ),
                ),
                (
                    "logo",
                    models.ForeignKey(
                        blank=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="authority_logo",
                        to="lca.file",
                        verbose_name="LOGO",
                    ),
                ),
                (
                    "sign",
                    models.ForeignKey(
                        blank=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="authority_sign",
                        to="lca.file",
                        verbose_name="电子签章",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ManufacturerCertification",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="企业名称")),
                (
                    "code",
                    models.CharField(max_length=100, verbose_name="统一社会信用代码"),
                ),
                (
                    "contact_name",
                    models.CharField(max_length=100, verbose_name="联系人"),
                ),
                (
                    "contact_phone",
                    models.CharField(max_length=100, verbose_name="联系人手机"),
                ),
                (
                    "contact_email",
                    models.CharField(max_length=100, verbose_name="联系人邮箱"),
                ),
                ("address", models.CharField(max_length=100, verbose_name="企业地址")),
                ("description", models.TextField(verbose_name="企业主营业务简介")),
                (
                    "website",
                    models.CharField(max_length=100, null=True, verbose_name="企业官网"),
                ),
                (
                    "create_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "geography",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="manufacturer_geography",
                        to="lca.geography",
                        verbose_name="地区",
                    ),
                ),
                (
                    "license",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="lca.file",
                        verbose_name="营业执照",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="User",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "last_login",
                    models.DateTimeField(blank=True, null=True, verbose_name="last login"),
                ),
                (
                    "is_superuser",
                    models.BooleanField(
                        default=False,
                        help_text="Designates that this user has all permissions without explicitly assigning them.",
                        verbose_name="superuser status",
                    ),
                ),
                (
                    "is_staff",
                    models.BooleanField(
                        default=False,
                        help_text="Designates whether the user can log into this admin site.",
                        verbose_name="staff status",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Designates whether this user should be treated as active. Unselect this instead of deleting accounts.",
                        verbose_name="active",
                    ),
                ),
                (
                    "date_joined",
                    models.DateTimeField(default=django.utils.timezone.now, verbose_name="date joined"),
                ),
                (
                    "phone",
                    models.CharField(max_length=1, unique=True, verbose_name="手机号"),
                ),
                (
                    "nickname",
                    models.CharField(max_length=20, unique=True, verbose_name="昵称"),
                ),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("manufacturer", "企业"),
                            ("authority", "认证机构"),
                            ("government", "政府"),
                        ],
                        db_index=True,
                        max_length=20,
                        verbose_name="用户类型",
                    ),
                ),
                (
                    "certificate_status",
                    models.CharField(
                        choices=[
                            ("none", "未认证"),
                            ("ongoing", "认证中"),
                            ("approved", "已通过"),
                            ("rejected", "未通过"),
                        ],
                        db_index=True,
                        default="ongoing",
                        max_length=20,
                        verbose_name="认证状态",
                    ),
                ),
                (
                    "groups",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The groups this user belongs to. A user will get all permissions granted to each of their groups.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.group",
                        verbose_name="groups",
                    ),
                ),
                (
                    "user_permissions",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Specific permissions for this user.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.permission",
                        verbose_name="user permissions",
                    ),
                ),
                (
                    "authority_certification",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="certification_authority_certification",
                        to="lca.authoritycertification",
                        verbose_name="认证机构信息",
                    ),
                ),
                (
                    "manufacturer_certification",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="certification_manufacturer_certification",
                        to="lca.manufacturercertification",
                        verbose_name="企业认证信息",
                    ),
                ),
            ],
            options={
                "verbose_name": "user",
                "verbose_name_plural": "users",
                "abstract": False,
            },
            managers=[
                ("objects", django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name="UserCertification",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("none", "未认证"),
                            ("ongoing", "认证中"),
                            ("approved", "已通过"),
                            ("rejected", "未通过"),
                        ],
                        db_index=True,
                        default="ongoing",
                        max_length=100,
                        verbose_name="认证状态",
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("manufacturer", "企业"),
                            ("authority", "认证机构"),
                            ("government", "政府"),
                        ],
                        db_index=True,
                        max_length=100,
                        verbose_name="用户类型",
                    ),
                ),
                ("reason", models.TextField(null=True, verbose_name="拒绝原因")),
                (
                    "finish_time",
                    models.DateTimeField(db_index=True, null=True, verbose_name="认证完成时间"),
                ),
                (
                    "create_time",
                    models.DateTimeField(auto_now_add=True, db_index=True, verbose_name="创建时间"),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, db_index=True, verbose_name="更新时间"),
                ),
                (
                    "authority_certification",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="user_certification_authority_certification",
                        to="lca.authoritycertification",
                        verbose_name="认证机构信息",
                    ),
                ),
                (
                    "manufacturer_certification",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="user_certification_manufacturer_certification",
                        to="lca.manufacturercertification",
                        verbose_name="企业认证信息",
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="user_certification",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
            ],
        ),
    ]
